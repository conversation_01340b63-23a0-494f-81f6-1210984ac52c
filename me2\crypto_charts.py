#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Crypto Charts with CCXT and Lightweight Charts
---------------------------------------------
A script to display cryptocurrency charts with technical indicators using CCXT and lightweight-charts.
"""

import asyncio
import pandas as pd
# import pandas_ta as ta
import ccxt
import time
from datetime import datetime
from lightweight_charts import Chart

class CryptoCharts:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1h'):
        """
        Initialize the CryptoCharts class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1h')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def calculate_indicators(self, df):
        """
        Calculate technical indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            
        Returns:
            dict: Dictionary with indicator DataFrames
        """
        indicators = {}
        
        # Calculate SMA
        sma20 = df.ta.sma(length=20).to_frame()
        sma20 = sma20.reset_index()
        sma20 = sma20.rename(columns={"index": "time", "SMA_20": "value"})
        sma20 = sma20.dropna()
        indicators['sma20'] = sma20
        
        # Calculate EMA
        ema50 = df.ta.ema(length=50).to_frame()
        ema50 = ema50.reset_index()
        ema50 = ema50.rename(columns={"index": "time", "EMA_50": "value"})
        ema50 = ema50.dropna()
        indicators['ema50'] = ema50
        
        # Calculate Bollinger Bands
        bbands = df.ta.bbands(length=20, std=2).reset_index()
        bbands = bbands.rename(columns={
            "index": "time", 
            "BBL_20_2.0": "lower",
            "BBM_20_2.0": "middle",
            "BBU_20_2.0": "upper"
        })
        bbands = bbands.dropna()
        indicators['bbands'] = bbands
        
        # Calculate RSI
        rsi = df.ta.rsi(length=14).to_frame()
        rsi = rsi.reset_index()
        rsi = rsi.rename(columns={"index": "time", "RSI_14": "value"})
        rsi = rsi.dropna()
        indicators['rsi'] = rsi
        
        # Calculate MACD
        macd = df.ta.macd(fast=12, slow=26, signal=9)
        macd = macd.reset_index()
        macd = macd.rename(columns={
            "index": "time",
            "MACD_12_26_9": "macd",
            "MACDs_12_26_9": "signal",
            "MACDh_12_26_9": "histogram"
        })
        macd = macd.dropna()
        indicators['macd'] = macd
        
        return indicators
    
    def create_chart(self, df, indicators, title=None):
        """
        Create and display a chart with indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            indicators (dict): Dictionary with indicator DataFrames
            title (str): Chart title (default: None)
        """
        # Create chart
        chart = Chart(volume_enabled=True)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Set chart data
        chart.set(df_formatted)
        
        # Add SMA line
        if 'sma20' in indicators:
            sma_line = chart.create_line(color='blue', price_line=True, price_label=True)
            sma_line.set(indicators['sma20'])
        
        # Add EMA line
        if 'ema50' in indicators:
            ema_line = chart.create_line(color='red', price_line=True, price_label=True)
            ema_line.set(indicators['ema50'])
        
        # Add Bollinger Bands
        if 'bbands' in indicators:
            bb_upper = chart.create_line(color='green', price_line=False)
            bb_middle = chart.create_line(color='orange', price_line=False)
            bb_lower = chart.create_line(color='green', price_line=False)
            
            bb_upper.set(indicators['bbands'][['time', 'upper']].rename(columns={'upper': 'value'}))
            bb_middle.set(indicators['bbands'][['time', 'middle']].rename(columns={'middle': 'value'}))
            bb_lower.set(indicators['bbands'][['time', 'lower']].rename(columns={'lower': 'value'}))
        
        # Add RSI in a subchart
        if 'rsi' in indicators:
            rsi_chart = chart.create_subchart(height=0.2, position='bottom')
            rsi_line = rsi_chart.create_line(color='purple')
            rsi_line.set(indicators['rsi'])
            
            # Add overbought/oversold lines
            rsi_chart.create_horizontal_line(70, color='red')
            rsi_chart.create_horizontal_line(30, color='green')
            rsi_chart.watermark('RSI')
        
        # Add MACD in a subchart
        if 'macd' in indicators:
            macd_chart = chart.create_subchart(height=0.2, position='bottom')
            
            # MACD line
            macd_line = macd_chart.create_line(color='blue')
            macd_line.set(indicators['macd'][['time', 'macd']].rename(columns={'macd': 'value'}))
            
            # Signal line
            signal_line = macd_chart.create_line(color='red')
            signal_line.set(indicators['macd'][['time', 'signal']].rename(columns={'signal': 'value'}))
            
            # Histogram
            histogram_data = indicators['macd'][['time', 'histogram']].rename(columns={'histogram': 'value'})
            histogram = macd_chart.create_histogram(color_up='green', color_down='red')
            histogram.set(histogram_data)
            
            macd_chart.watermark('MACD')
        
        # Set chart title
        if title:
            chart.watermark(title)
        else:
            chart.watermark(f"{self.symbol} - {self.timeframe}")
        
        # Show chart
        chart.show(block=True)

class CryptoChartsGrid:
    def __init__(self, symbols, exchange_id='binance', timeframe='1h'):
        """
        Initialize the CryptoChartsGrid class.
        
        Args:
            symbols (list): List of symbols to display
            exchange_id (str): The exchange ID (default: 'binance')
            timeframe (str): The chart timeframe (default: '1h')
        """
        self.symbols = symbols
        self.exchange_id = exchange_id
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, symbol, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            symbol (str): The trading pair symbol
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data for {symbol}: {e}")
            return None
    
    def create_grid(self):
        """Create and display a grid of charts."""
        # Determine grid dimensions
        num_symbols = len(self.symbols)
        if num_symbols <= 2:
            cols = num_symbols
            rows = 1
        else:
            cols = 2
            rows = (num_symbols + 1) // 2
        
        # Create main chart
        chart = Chart(inner_width=1/cols, inner_height=1/rows)
        charts = [chart]
        
        # Create subcharts
        for i in range(1, num_symbols):
            if i == 1:
                subchart = chart.create_subchart(position='right', width=1/cols, height=1/rows)
            elif i % 2 == 0:
                subchart = charts[i-2].create_subchart(position='bottom', width=1/cols, height=1/rows)
            else:
                subchart = charts[i-2].create_subchart(position='right', width=1/cols, height=1/rows)
            charts.append(subchart)
        
        # Fetch data and set charts
        for i, symbol in enumerate(self.symbols):
            df = self.fetch_ohlcv(symbol)
            if df is not None:
                df_formatted = df.copy()
                df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                charts[i].set(df_formatted)
                charts[i].watermark(f"{symbol} - {self.timeframe}")
        
        # Show grid
        chart.show(block=True)

def main():
    """Main function to demonstrate the CryptoCharts class."""
    # Single chart example
    crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='1h')
    df = crypto_chart.fetch_ohlcv(limit=500)
    if df is not None:
        indicators = crypto_chart.calculate_indicators(df)
        crypto_chart.create_chart(df, indicators)
    
    # Grid example
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT']
    grid = CryptoChartsGrid(symbols, exchange_id='binance', timeframe='1h')
    grid.create_grid()

if __name__ == '__main__':
    main()
