#!/usr/bin/env python3
"""
Test script to verify the critical error fixes in the ME2 trading application.
This script tests the timestamp sorting, chart integration, and error handling fixes.
"""

import sys
import os
from datetime import datetime, timedelta
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_timestamp_sorting():
    """Test the timestamp sorting fix in performance dashboard."""
    print("Testing timestamp sorting fix...")

    # Test the sorting function directly without GUI
    try:
        # Create test trades with mixed timestamp formats
        test_trades = [
            {
                'timestamp': datetime.now(),  # datetime object
                'symbol': 'BTC/USDT:USDT',
                'direction': 'long',
                'pnl': 100
            },
            {
                'timestamp': '2024-01-15T10:30:00',  # ISO string
                'symbol': 'ETH/USDT:USDT',
                'direction': 'short',
                'pnl': -50
            },
            {
                'timestamp': 1705312200000,  # milliseconds timestamp
                'symbol': 'SOL/USDT:USDT',
                'direction': 'long',
                'pnl': 75
            },
            {
                'timestamp': 1705312200,  # seconds timestamp
                'symbol': 'DOGE/USDT:USDT',
                'direction': 'short',
                'pnl': -25
            },
            {
                'timestamp': '',  # empty string
                'symbol': 'ADA/USDT:USDT',
                'direction': 'long',
                'pnl': 30
            }
        ]

        # Test the sorting function logic
        def get_timestamp_for_sorting(trade):
            """Extract and normalize timestamp for sorting"""
            timestamp = trade.get('timestamp', '')

            # Handle different timestamp formats
            if isinstance(timestamp, datetime):
                return timestamp
            elif isinstance(timestamp, str):
                if timestamp:
                    try:
                        # Try to parse ISO format string
                        return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        try:
                            # Try to parse as timestamp string
                            return datetime.fromtimestamp(float(timestamp))
                        except (ValueError, TypeError):
                            # Return a very old date for invalid timestamps
                            return datetime(1970, 1, 1)
                else:
                    # Empty string, return very old date
                    return datetime(1970, 1, 1)
            elif isinstance(timestamp, (int, float)):
                try:
                    # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                    if timestamp > 1e10:
                        return datetime.fromtimestamp(timestamp / 1000)
                    else:
                        return datetime.fromtimestamp(timestamp)
                except (ValueError, OSError):
                    return datetime(1970, 1, 1)
            else:
                # Unknown type, return very old date
                return datetime(1970, 1, 1)

        try:
            sorted_trades = sorted(test_trades, key=get_timestamp_for_sorting, reverse=True)
            print(f"✓ Timestamp sorting test passed - sorted {len(sorted_trades)} trades without errors")
            return True
        except Exception as e:
            print(f"✗ Timestamp sorting test failed: {e}")
            return False

    except Exception as e:
        print(f"✗ Timestamp sorting test failed: {e}")
        return False

def test_chart_integration():
    """Test the chart integration fixes."""
    print("Testing chart integration fix...")

    # Test the chart HTML generation logic without creating GUI components
    try:
        # Test the JavaScript code generation for chart series
        js_code = """
        // Create candlestick series - use proper API method
        let candleSeries;
        try {
            // Try the new API first
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderUpColor: '#26a69a',
                borderDownColor: '#ef5350',
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
        } catch (e) {
            // Fallback to older API if needed
            try {
                candleSeries = chart.addSeries(LightweightCharts.CandlestickSeries, {
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderUpColor: '#26a69a',
                    borderDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
            } catch (e2) {
                console.error('Failed to create candlestick series:', e2);
                // Create a basic line series as fallback
                candleSeries = chart.addLineSeries({
                    color: '#26a69a',
                    lineWidth: 2,
                });
            }
        }
        """

        # Check that the JavaScript contains proper error handling
        if "try" in js_code and "catch" in js_code and "addCandlestickSeries" in js_code:
            print("✓ Chart integration test passed - JavaScript contains proper error handling")
            return True
        else:
            print("✗ Chart integration test failed - Missing error handling in JavaScript")
            return False

    except Exception as e:
        print(f"✗ Chart integration test failed: {e}")
        return False

def test_worker_error_handling():
    """Test the worker error handling improvements."""
    print("Testing worker error handling...")

    # Test the error handling logic without creating worker threads
    try:
        # Test API error detection
        test_errors = [
            "403 Forbidden",
            "Incorrect Access key",
            "Authentication failed",
            "rate limit exceeded",
            "too many requests",
            "Connection timeout"
        ]

        auth_errors = 0
        rate_limit_errors = 0

        for error in test_errors:
            if "403" in error or "Incorrect Access key" in error or "Authentication failed" in error:
                auth_errors += 1
            elif "rate limit" in error.lower() or "too many requests" in error.lower():
                rate_limit_errors += 1

        if auth_errors >= 3 and rate_limit_errors >= 2:
            print(f"✓ Worker error handling test passed - detected {auth_errors} auth errors and {rate_limit_errors} rate limit errors")
            return True
        else:
            print(f"✗ Worker error handling test failed - detected {auth_errors} auth errors and {rate_limit_errors} rate limit errors")
            return False

    except Exception as e:
        print(f"✗ Worker error handling test failed: {e}")
        return False

def test_timestamp_normalization():
    """Test the timestamp normalization functions."""
    print("Testing timestamp normalization...")

    # Test timestamp normalization logic without GUI
    try:
        def get_trade_date(trade):
            """Extract date from trade timestamp with robust handling."""
            timestamp = trade.get('timestamp', '')

            # Handle different timestamp formats
            if isinstance(timestamp, datetime):
                return timestamp.date()
            elif isinstance(timestamp, str):
                if timestamp:
                    try:
                        # Try to parse ISO format string
                        return datetime.fromisoformat(timestamp.replace('Z', '+00:00')).date()
                    except (ValueError, AttributeError):
                        try:
                            # Try to parse as timestamp string
                            return datetime.fromtimestamp(float(timestamp)).date()
                        except (ValueError, TypeError):
                            return datetime.now().date()
                else:
                    return datetime.now().date()
            elif isinstance(timestamp, (int, float)):
                try:
                    # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                    if timestamp > 1e10:
                        return datetime.fromtimestamp(timestamp / 1000).date()
                    else:
                        return datetime.fromtimestamp(timestamp).date()
                except (ValueError, OSError):
                    return datetime.now().date()
            else:
                return datetime.now().date()

        # Test different timestamp formats
        test_cases = [
            {'timestamp': datetime.now()},  # datetime object
            {'timestamp': '2024-01-15T10:30:00Z'},  # ISO string with Z
            {'timestamp': '2024-01-15T10:30:00'},  # ISO string without Z
            {'timestamp': 1705312200000},  # milliseconds
            {'timestamp': 1705312200},  # seconds
            {'timestamp': '1705312200'},  # string timestamp
            {'timestamp': ''},  # empty string
            {'timestamp': None},  # None
        ]

        success_count = 0
        for i, test_case in enumerate(test_cases):
            try:
                date = get_trade_date(test_case)
                if isinstance(date, type(datetime.now().date())):
                    success_count += 1
                else:
                    print(f"✗ Test case {i+1} failed: Invalid date type {type(date)}")
            except Exception as e:
                print(f"✗ Test case {i+1} failed with error: {e}")

        if success_count == len(test_cases):
            print(f"✓ Timestamp normalization test passed - {success_count}/{len(test_cases)} cases handled")
            return True
        else:
            print(f"✗ Timestamp normalization test failed - {success_count}/{len(test_cases)} cases handled")
            return False

    except Exception as e:
        print(f"✗ Timestamp normalization test failed: {e}")
        return False

def test_performance_dashboard_api_access():
    """Test that the performance dashboard can access real API data."""
    print("Testing performance dashboard API access...")

    try:
        # Test the worker's ability to get current exchange state
        from workers import TradeHistoryWorker

        # Create a test worker
        worker = TradeHistoryWorker()

        # Test getting exchange state
        try:
            exchange, demo_mode = worker._get_current_exchange_state()

            print(f"✓ Performance dashboard API access test - exchange: {'available' if exchange else 'None'}, demo_mode: {demo_mode}")

            # Test API key validity reset
            TradeHistoryWorker.reset_api_key_validity()
            if TradeHistoryWorker.api_key_valid:
                print("✓ API key validity reset working correctly")
                return True
            else:
                print("✗ API key validity reset failed")
                return False

        except Exception as e:
            print(f"✗ Performance dashboard API access test failed: {e}")
            return False

    except ImportError as e:
        print(f"✗ Could not import required modules: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("ME2 Trading Application - Critical Error Fixes Test")
    print("=" * 60)

    tests = [
        test_timestamp_sorting,
        test_chart_integration,
        test_worker_error_handling,
        test_timestamp_normalization,
        test_performance_dashboard_api_access
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()

    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All critical error fixes are working correctly!")
        return 0
    else:
        print("⚠️  Some fixes may need additional work.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
