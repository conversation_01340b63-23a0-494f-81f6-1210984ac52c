html {
	--checkbox-color: var(--color-tv-blue-400);
	--checkbox-label-hover-color: var(--color-tv-blue-500);
	--checkbox-border-hover-color: var(--color-tv-blue-500);
}

html[data-theme=dark] {
	--checkbox-color: var(--color-tv-blue-600);
	--checkbox-label-hover-color: var(--color-tv-blue-200);
	--checkbox-border-hover-color: var(--color-tv-blue-200);
}

input[type='checkbox'] {
    vertical-align: middle;
    margin-right: 0.4rem;
    accent-color: var(--checkbox-color);
    margin-bottom: 0.4rem;
	appearance: none;
    border: 2px solid grey;
    border-radius: 4px;
    height: 1.5rem;
    width: 1.5rem;
	cursor: pointer;
	overflow: hidden;
}

input[type='checkbox']:hover {
	border-color: var(--checkbox-border-hover-color);
}

input[type='checkbox']:checked {
	background-color: var(--checkbox-color);
	border-color: var(--checkbox-color);
}

input[type='checkbox']::after {
	position: relative;
	content: '';
	width: 1.5rem;
	height: 1.5rem;
	display: inline-block;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+DQo8c3ZnIHdpZHRoPSIxMiIgaGVpZ2h0PSI5IiB2aWV3Qm94PSIwIDAgMTIgOSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgPHBhdGggZD0iTTQuNTc1IDguOTc3cy0uNDA0LS4wMDctLjUzNi0uMTY1TC4wNTcgNS42NGwuODI5LTEuMjI3TDQuNDcgNy4yNjggMTAuOTIxLjA4NmwuOTIzIDEuMTAzLTYuODYzIDcuNjRjLS4xMzQtLjAwMy0uNDA2LjE0OC0uNDA2LjE0OHoiIGZpbGw9IiNGRkYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPg0KPC9zdmc+) 40% 40% no-repeat;
	transform: scale(1);
	transition: transform 0.5s;
	opacity: 0;
}

input[type='checkbox']:checked::after {
	transform: scale(1.5);
	opacity: 1;
	transition: transform 0.5s, opacity 0.2s;
}

.toggle-label {
	cursor: pointer;
}

.toggle-label:hover {
	color: var(--checkbox-label-hover-color);
}

input.toggle-hidden-lines:checked ~ div.theme-code-block .code-block-hide-line {
	display: block;
	user-select: all;
}

input.toggle-hidden-lines:not(:checked)
	~ div.theme-code-block
	.token-line:not(.code-block-hide-line)
	+ .token-line.code-block-hide-line {
	visibility: hidden;
	display: block;
	color: transparent;
}

input.toggle-hidden-lines:not(:checked)
	~ div.theme-code-block
	.token-line:not(.code-block-hide-line)
	+ .token-line.code-block-hide-line::before {
	content: "... hidden lines ...";
	visibility: visible;
	position: absolute;
	background-color: var(--hidden-lines-warning-bg, rgba(0, 0, 0, 0.1));
	text-align: left;
	padding: 0 var(--ifm-pre-padding);
	width: 100%;
	border-radius: 0.2rem;
}