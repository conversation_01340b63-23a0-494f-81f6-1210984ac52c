#!/usr/bin/env python3
"""
Test script to debug TradeHistoryWorker issues.
"""

import sys
import os
import yaml
import ccxt
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_trade_history_worker():
    """Test TradeHistoryWorker functionality."""
    print("=" * 60)
    print("TradeHistoryWorker Debug Test")
    print("=" * 60)
    
    # Initialize exchange
    try:
        with open('credentials.yaml', 'r') as file:
            credentials = yaml.safe_load(file)
        
        default_account = credentials.get('default_account', 'EPX')
        account_info = None
        for account in credentials.get('accounts', []):
            if account.get('name') == default_account:
                account_info = account
                break
        
        api_key = account_info.get('api_key')
        secret_key = account_info.get('secret_key')
        
        exchange_config = {
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
            },
            'sandbox': False,
            'urls': {
                'api': {
                    'public': 'https://api.huobi.pro',
                    'private': 'https://api.huobi.pro',
                    'swap': 'https://api.hbdm.com',
                }
            }
        }
        
        exchange = ccxt.huobi(exchange_config)
        exchange.load_markets()
        print("✓ Exchange initialized successfully")
        
    except Exception as e:
        print(f"❌ Exchange initialization failed: {e}")
        return False
    
    # Test fetching closed orders
    try:
        print("\n--- Testing Closed Orders Fetch ---")
        closed_orders = exchange.fetch_closed_orders(symbol='BTC/USDT:USDT', limit=5)
        print(f"✓ Fetched {len(closed_orders)} closed orders")
        
        # Analyze each order
        for i, order in enumerate(closed_orders):
            print(f"\nOrder {i+1} Raw Data:")
            print(f"  ID: {order.get('id')}")
            print(f"  Symbol: {order.get('symbol')}")
            print(f"  Side: {order.get('side')}")
            print(f"  Status: {order.get('status')}")
            print(f"  Price: {order.get('price')} (type: {type(order.get('price'))})")
            print(f"  Amount: {order.get('amount')} (type: {type(order.get('amount'))})")
            print(f"  Filled: {order.get('filled')} (type: {type(order.get('filled'))})")
            print(f"  Average: {order.get('average')} (type: {type(order.get('average'))})")
            print(f"  Timestamp: {order.get('timestamp')} (type: {type(order.get('timestamp'))})")
            print(f"  Fee: {order.get('fee')}")
            
    except Exception as e:
        print(f"❌ Error fetching closed orders: {e}")
        return False
    
    # Test the _format_order method
    print("\n--- Testing _format_order Method ---")
    
    # Import the worker
    try:
        from workers import TradeHistoryWorker
        worker = TradeHistoryWorker()
        
        formatted_trades = []
        for i, order in enumerate(closed_orders):
            print(f"\nProcessing Order {i+1}:")
            try:
                formatted_trade = worker._format_order(order)
                if formatted_trade:
                    formatted_trades.append(formatted_trade)
                    print(f"  ✓ Successfully formatted trade")
                    print(f"    Symbol: {formatted_trade['symbol']}")
                    print(f"    Direction: {formatted_trade['direction']}")
                    print(f"    Price: {formatted_trade['entry_price']}")
                    print(f"    Size: {formatted_trade['size']}")
                else:
                    print(f"  ❌ Order was filtered out (returned None)")
            except Exception as e:
                print(f"  ❌ Error formatting order: {e}")
        
        print(f"\n--- Summary ---")
        print(f"Raw orders fetched: {len(closed_orders)}")
        print(f"Successfully formatted trades: {len(formatted_trades)}")
        
        if len(formatted_trades) > 0:
            print("✅ TradeHistoryWorker is working correctly!")
            return True
        else:
            print("❌ TradeHistoryWorker is filtering out all trades")
            return False
            
    except Exception as e:
        print(f"❌ Error testing _format_order: {e}")
        return False

def main():
    """Main function."""
    success = test_trade_history_worker()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
