#!/usr/bin/env python3
"""
Test script to verify API authentication is working correctly.
"""

import sys
import os
import yaml
import ccxt

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_authentication():
    """Test API authentication with current credentials."""
    print("=" * 60)
    print("API Authentication Test")
    print("=" * 60)
    
    # Load credentials
    try:
        with open('credentials.yaml', 'r') as file:
            credentials = yaml.safe_load(file)
        
        default_account = credentials.get('default_account', 'EPX')
        print(f"Default account: {default_account}")
        
        # Find the account
        account_info = None
        for account in credentials.get('accounts', []):
            if account.get('name') == default_account:
                account_info = account
                break
        
        if not account_info:
            print("❌ Account not found")
            return False
        
        api_key = account_info.get('api_key')
        secret_key = account_info.get('secret_key')
        
        if not api_key or not secret_key:
            print("❌ API credentials not found")
            return False
        
        print(f"✓ API Key: {api_key[:8]}...")
        print(f"✓ Secret Key: {secret_key[:8]}...")
        
    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return False
    
    # Test exchange initialization
    try:
        print("\n--- Testing Exchange Initialization ---")
        
        exchange_config = {
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
            },
            'sandbox': False,
            'urls': {
                'api': {
                    'public': 'https://api.huobi.pro',
                    'private': 'https://api.huobi.pro',
                    'swap': 'https://api.hbdm.com',
                }
            }
        }
        
        exchange = ccxt.huobi(exchange_config)
        print("✓ Exchange created successfully")
        
        # Load markets
        exchange.load_markets()
        print("✓ Markets loaded successfully")
        
    except Exception as e:
        print(f"❌ Exchange initialization failed: {e}")
        return False
    
    # Test API calls
    print("\n--- Testing API Calls ---")
    
    # Test 1: Fetch balance
    try:
        print("Testing fetch_balance()...")
        balance = exchange.fetch_balance()
        print(f"✓ Balance fetched successfully. Currencies: {len(balance.get('info', {}))}")
    except Exception as e:
        print(f"❌ fetch_balance() failed: {e}")
        if "403" in str(e) or "Incorrect Access key" in str(e):
            print("   This indicates API authentication failure")
        return False
    
    # Test 2: Fetch positions
    try:
        print("Testing fetch_positions()...")
        positions = exchange.fetch_positions()
        print(f"✓ Positions fetched successfully. Count: {len(positions)}")
    except Exception as e:
        print(f"❌ fetch_positions() failed: {e}")
        if "403" in str(e) or "Incorrect Access key" in str(e):
            print("   This indicates API authentication failure")
        return False
    
    # Test 3: Fetch closed orders
    try:
        print("Testing fetch_closed_orders()...")
        closed_orders = exchange.fetch_closed_orders(symbol='BTC/USDT:USDT', limit=5)
        print(f"✓ Closed orders fetched successfully. Count: {len(closed_orders)}")
        
        # Show order details
        for i, order in enumerate(closed_orders[:2]):  # Show first 2 orders
            print(f"   Order {i+1}: {order.get('symbol')} {order.get('side')} {order.get('amount')} @ {order.get('price')}")
            
    except Exception as e:
        print(f"❌ fetch_closed_orders() failed: {e}")
        if "403" in str(e) or "Incorrect Access key" in str(e):
            print("   This indicates API authentication failure")
        return False
    
    print("\n✅ All API authentication tests passed!")
    return True

def main():
    """Main function."""
    success = test_api_authentication()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
