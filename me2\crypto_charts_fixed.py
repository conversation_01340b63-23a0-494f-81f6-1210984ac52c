#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Crypto Charts with CCXT and Lightweight Charts (Fixed Version)
---------------------------------------------
A script to display cryptocurrency charts with technical indicators using CCXT and lightweight-charts.
This version doesn't rely on pandas_ta to avoid the numpy.NaN import error.
"""

import asyncio
import pandas as pd
import numpy as np
import ccxt
import time
from datetime import datetime
from lightweight_charts import Chart

class CryptoCharts:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1h'):
        """
        Initialize the CryptoCharts class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1h')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def calculate_sma(self, df, period=20):
        """Calculate Simple Moving Average."""
        sma = df['close'].rolling(window=period).mean()
        sma_df = pd.DataFrame({'time': df['time'], 'value': sma})
        return sma_df.dropna()
    
    def calculate_ema(self, df, period=50):
        """Calculate Exponential Moving Average."""
        ema = df['close'].ewm(span=period, adjust=False).mean()
        ema_df = pd.DataFrame({'time': df['time'], 'value': ema})
        return ema_df.dropna()
    
    def calculate_bollinger_bands(self, df, period=20, std_dev=2):
        """Calculate Bollinger Bands."""
        # Calculate middle band (SMA)
        middle_band = df['close'].rolling(window=period).mean()
        
        # Calculate standard deviation
        std = df['close'].rolling(window=period).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)
        
        # Create DataFrame
        bbands_df = pd.DataFrame({
            'time': df['time'],
            'middle': middle_band,
            'upper': upper_band,
            'lower': lower_band
        })
        
        return bbands_df.dropna()
    
    def calculate_rsi(self, df, period=14):
        """Calculate Relative Strength Index."""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # For periods after the initial period
        for i in range(period, len(df)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (period-1) + gain.iloc[i]) / period
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (period-1) + loss.iloc[i]) / period
        
        rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))
        
        rsi_df = pd.DataFrame({'time': df['time'], 'value': rsi})
        return rsi_df.dropna()
    
    def calculate_macd(self, df, fast_period=12, slow_period=26, signal_period=9):
        """Calculate Moving Average Convergence Divergence."""
        # Calculate fast and slow EMAs
        fast_ema = df['close'].ewm(span=fast_period, adjust=False).mean()
        slow_ema = df['close'].ewm(span=slow_period, adjust=False).mean()
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
        
        # Calculate histogram
        histogram = macd_line - signal_line
        
        # Create DataFrame
        macd_df = pd.DataFrame({
            'time': df['time'],
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        })
        
        return macd_df.dropna()
    
    def calculate_indicators(self, df):
        """
        Calculate technical indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            
        Returns:
            dict: Dictionary with indicator DataFrames
        """
        indicators = {}
        
        # Calculate SMA
        sma20 = self.calculate_sma(df, period=20)
        indicators['sma20'] = sma20
        
        # Calculate EMA
        ema50 = self.calculate_ema(df, period=50)
        indicators['ema50'] = ema50
        
        # Calculate Bollinger Bands
        bbands = self.calculate_bollinger_bands(df, period=20, std_dev=2)
        indicators['bbands'] = bbands
        
        # Calculate RSI
        rsi = self.calculate_rsi(df, period=14)
        indicators['rsi'] = rsi
        
        # Calculate MACD
        macd = self.calculate_macd(df, fast_period=12, slow_period=26, signal_period=9)
        indicators['macd'] = macd
        
        return indicators
    
    def create_chart(self, df, indicators, title=None):
        """
        Create and display a chart with indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            indicators (dict): Dictionary with indicator DataFrames
            title (str): Chart title (default: None)
        """
        # Create chart
        chart = Chart(volume_enabled=True)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Set chart data
        chart.set(df_formatted)
        
        # Add SMA line
        if 'sma20' in indicators:
            sma_formatted = indicators['sma20'].copy()
            sma_formatted['time'] = sma_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            sma_line = chart.create_line(color='blue', price_line=True, price_label=True)
            sma_line.set(sma_formatted)
        
        # Add EMA line
        if 'ema50' in indicators:
            ema_formatted = indicators['ema50'].copy()
            ema_formatted['time'] = ema_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            ema_line = chart.create_line(color='red', price_line=True, price_label=True)
            ema_line.set(ema_formatted)
        
        # Add Bollinger Bands
        if 'bbands' in indicators:
            bbands_formatted = indicators['bbands'].copy()
            bbands_formatted['time'] = bbands_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            bb_upper = chart.create_line(color='green', price_line=False)
            bb_middle = chart.create_line(color='orange', price_line=False)
            bb_lower = chart.create_line(color='green', price_line=False)
            
            bb_upper.set(bbands_formatted[['time', 'upper']].rename(columns={'upper': 'value'}))
            bb_middle.set(bbands_formatted[['time', 'middle']].rename(columns={'middle': 'value'}))
            bb_lower.set(bbands_formatted[['time', 'lower']].rename(columns={'lower': 'value'}))
        
        # Add RSI in a subchart
        if 'rsi' in indicators:
            rsi_formatted = indicators['rsi'].copy()
            rsi_formatted['time'] = rsi_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            rsi_chart = chart.create_subchart(height=0.2, position='bottom')
            rsi_line = rsi_chart.create_line(color='purple')
            rsi_line.set(rsi_formatted)
            
            # Add overbought/oversold lines
            rsi_chart.create_horizontal_line(70, color='red')
            rsi_chart.create_horizontal_line(30, color='green')
            rsi_chart.watermark('RSI')
        
        # Add MACD in a subchart
        if 'macd' in indicators:
            macd_formatted = indicators['macd'].copy()
            macd_formatted['time'] = macd_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            macd_chart = chart.create_subchart(height=0.2, position='bottom')
            
            # MACD line
            macd_line = macd_chart.create_line(color='blue')
            macd_line.set(macd_formatted[['time', 'macd']].rename(columns={'macd': 'value'}))
            
            # Signal line
            signal_line = macd_chart.create_line(color='red')
            signal_line.set(macd_formatted[['time', 'signal']].rename(columns={'signal': 'value'}))
            
            # Histogram
            histogram_data = macd_formatted[['time', 'histogram']].rename(columns={'histogram': 'value'})
            histogram = macd_chart.create_histogram(color_up='green', color_down='red')
            histogram.set(histogram_data)
            
            macd_chart.watermark('MACD')
        
        # Set chart title
        if title:
            chart.watermark(title)
        else:
            chart.watermark(f"{self.symbol} - {self.timeframe}")
        
        # Show chart
        chart.show(block=True)

class CryptoChartsGrid:
    def __init__(self, symbols, exchange_id='binance', timeframe='1h'):
        """
        Initialize the CryptoChartsGrid class.
        
        Args:
            symbols (list): List of symbols to display
            exchange_id (str): The exchange ID (default: 'binance')
            timeframe (str): The chart timeframe (default: '1h')
        """
        self.symbols = symbols
        self.exchange_id = exchange_id
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, symbol, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            symbol (str): The trading pair symbol
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data for {symbol}: {e}")
            return None
    
    def create_grid(self):
        """Create and display a grid of charts."""
        # Determine grid dimensions
        num_symbols = len(self.symbols)
        if num_symbols <= 2:
            cols = num_symbols
            rows = 1
        else:
            cols = 2
            rows = (num_symbols + 1) // 2
        
        # Create main chart
        chart = Chart(inner_width=1/cols, inner_height=1/rows)
        charts = [chart]
        
        # Create subcharts
        for i in range(1, num_symbols):
            if i == 1:
                subchart = chart.create_subchart(position='right', width=1/cols, height=1/rows)
            elif i % 2 == 0:
                subchart = charts[i-2].create_subchart(position='bottom', width=1/cols, height=1/rows)
            else:
                subchart = charts[i-2].create_subchart(position='right', width=1/cols, height=1/rows)
            charts.append(subchart)
        
        # Fetch data and set charts
        for i, symbol in enumerate(self.symbols):
            df = self.fetch_ohlcv(symbol)
            if df is not None:
                df_formatted = df.copy()
                df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                charts[i].set(df_formatted)
                charts[i].watermark(f"{symbol} - {self.timeframe}")
        
        # Show grid
        chart.show(block=True)

def main():
    """Main function to demonstrate the CryptoCharts class."""
    # Single chart example
    crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='1h')
    df = crypto_chart.fetch_ohlcv(limit=500)
    if df is not None:
        indicators = crypto_chart.calculate_indicators(df)
        crypto_chart.create_chart(df, indicators)
    
    # Grid example
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT']
    grid = CryptoChartsGrid(symbols, exchange_id='binance', timeframe='1h')
    grid.create_grid()

if __name__ == '__main__':
    main()
