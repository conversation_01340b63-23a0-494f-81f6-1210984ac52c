#!/usr/bin/env python3
"""
Launcher script for ME2 Trading Application.
This script ensures all dependencies are available and runs the application.
"""

import sys
import os
import subprocess
import importlib.util

def check_dependency(module_name, package_name=None):
    """Check if a Python module is available."""
    if package_name is None:
        package_name = module_name
    
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is None:
            return False
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a Python package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main launcher function."""
    print("=" * 60)
    print("ME2 Trading Application Launcher")
    print("=" * 60)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Check if me2_stable.py exists
    me2_path = os.path.join(current_dir, "me2_stable.py")
    if not os.path.exists(me2_path):
        print("❌ ERROR: me2_stable.py not found in current directory")
        print("Please run this script from the ME2 application directory")
        input("Press Enter to exit...")
        return 1
    
    print("✅ Found me2_stable.py")
    
    # Check if credentials.yaml exists
    credentials_path = os.path.join(current_dir, "credentials.yaml")
    if not os.path.exists(credentials_path):
        print("⚠️  WARNING: credentials.yaml not found")
        print("The application may not work properly without API credentials")
    else:
        print("✅ Found credentials.yaml")
    
    # Check dependencies
    dependencies = [
        ("PySide6", "PySide6"),
        ("ccxt", "ccxt"),
        ("pyqtgraph", "pyqtgraph"),
        ("yaml", "pyyaml"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("websocket", "websocket-client"),
        ("requests", "requests")
    ]
    
    print("\n📦 Checking dependencies...")
    missing_deps = []
    
    for module_name, package_name in dependencies:
        if check_dependency(module_name):
            print(f"✅ {module_name}")
        else:
            print(f"❌ {module_name} (missing)")
            missing_deps.append(package_name)
    
    # Install missing dependencies
    if missing_deps:
        print(f"\n🔧 Installing missing dependencies: {', '.join(missing_deps)}")
        for package in missing_deps:
            print(f"Installing {package}...")
            if install_package(package):
                print(f"✅ {package} installed successfully")
            else:
                print(f"❌ Failed to install {package}")
                input("Press Enter to exit...")
                return 1
    
    print("\n🚀 All dependencies are available!")
    print("Starting ME2 Trading Application...")
    print("=" * 60)
    
    # Change to the script directory to ensure relative imports work
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run the application
    try:
        # Import and run the main application
        sys.path.insert(0, script_dir)
        import me2_stable
        return 0
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("\nTrying alternative launch method...")
        
        # Try running as subprocess
        try:
            result = subprocess.run([sys.executable, "me2_stable.py"], 
                                  cwd=script_dir, 
                                  check=True)
            return result.returncode
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start application: {e}")
            input("Press Enter to exit...")
            return 1
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            input("Press Enter to exit...")
            return 1

if __name__ == "__main__":
    sys.exit(main())
