{"private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "cross-env TYPEDOC_WATCH=true docusaurus start", "build": "node scripts/generate-versions-dts.js && docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "3.7.0", "@docusaurus/faster": "3.7.0", "@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/preset-classic": "3.7.0", "@docusaurus/theme-search-algolia": "3.7.0", "@types/react": "18.3", "cross-env": "7.0.3", "docusaurus-plugin-typedoc": "1.0.1", "lightweight-charts": "5.0.6", "lightweight-charts-3.8": "npm:lightweight-charts@~3.8.0", "lightweight-charts-4.0": "npm:lightweight-charts@~4.0.1", "lightweight-charts-4.1": "npm:lightweight-charts@~4.1.5", "lightweight-charts-4.2": "npm:lightweight-charts@~4.2.2", "lightweight-charts-5.0": "npm:lightweight-charts@5.0.6", "lightweight-charts-local": "file:..", "prism-react-renderer": "2.4.1", "raw-loader": "4.0.2", "react": "18.3", "react-dom": "18.3", "typedoc": "0.25.13", "typedoc-plugin-markdown": "4.0.3", "typescript": "5.4.5", "vue": "3.5.13"}, "devDependencies": {"@tsconfig/docusaurus": "2.0.3", "typedoc-plugin-frontmatter": "1.0.0"}}