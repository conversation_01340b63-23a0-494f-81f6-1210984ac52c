{"compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": true, "importHelpers": true, "lib": ["dom", "es2020"], "module": "esnext", "moduleResolution": "node", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "preserveConstEnums": true, "resolveJsonModule": true, "rootDir": "./", "strict": true, "stripInternal": false, "target": "es2020", "typeRoots": ["./src/typings", "node_modules/@types"], "types": ["_global-types", "dom-not-standarted"]}}