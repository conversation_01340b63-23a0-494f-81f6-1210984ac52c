---
title: Whitespace data
sidebar_label: Whitespace data
description: An example of how to provide whitespace data
pagination_prev: null
pagination_next: null
keywords:
  - whitespace data
---

This sample demonstrates the usage of "whitespace data" in Lightweight Charts™.
Rather than a complete set of pricing information, these data points only
provide a timestamp. This generates a gap or "whitespace" on the chart,
signifying periods without trading. An example in the code is `{time: { year:
2018, month: 9, day: 24 }}`, which results in a visual break in the candlestick
series.

### API Reference

- [WhitespaceData](/docs/api/interfaces/WhitespaceData)

import UsageGuidePartial from '../_usage-guide-partial.mdx';
import CodeBlock from '@theme/CodeBlock';
import code from '!!raw-loader!./whitespace.js';

<CodeBlock
	replaceThemeConstants
	chart
	className="language-js"
	hideableCode
	chartOnTop
	codeUsage={<UsageGuidePartial />}
>
	{code}
</CodeBlock>
