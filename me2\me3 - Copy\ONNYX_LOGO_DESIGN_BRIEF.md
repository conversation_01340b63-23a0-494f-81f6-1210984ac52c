# ONNYX Logo Design Brief
## Cryptographic Trust Platform

### 🎯 **Design Concept**
Create a professional logo for Onnyx, a cryptographic trust platform, featuring a stylized elephant as the central symbol representing resilience, memory, and protection.

### 🐘 **Core Symbolism**
- **Elephant**: Represents strength, memory, wisdom, and protection
- **Polygonal/Faceted Design**: Symbolizes cryptographic complexity and technological sophistication
- **Onyx Black**: Represents security, elegance, and trust
- **Neon Accents**: Convey innovation and cutting-edge technology

### 🎨 **Design Specifications**

#### **Primary Design Elements**
- **Main Subject**: Stylized elephant in polygonal/faceted geometric style
- **Primary Color**: Onyx black (#1C1C1C) to dark charcoal (#2D2D2D)
- **Secondary Color**: Clean white (#FFFFFF) for highlights and contrast
- **Accent Colors**: Choose one of:
  - Violet: #8A2BE2 (Electric Purple)
  - Teal: #00CED1 (Dark Turquoise) 
  - Electric Gold: #FFD700 (Gold) with #FFFF00 (Electric Yellow) glow

#### **Style Requirements**
- **Aesthetic**: Modern, minimalist, technological
- **Geometry**: Low-poly/faceted design with clean edges
- **Lighting**: Subtle depth with strategic highlights
- **Background**: Transparent for versatility

#### **Technical Specifications**
- **Format**: PNG (transparent) + SVG (vector)
- **Resolution**: Minimum 1024x1024px for PNG
- **Scalability**: Must work from 16x16px (favicon) to large format
- **Variations**: 
  - Full logo with "ONNYX" wordmark
  - Icon-only version
  - Monochrome version for single-color applications

### 🎯 **Use Cases**
1. **App Icon**: iOS/Android application icon
2. **Dark UI Interface**: Primary logo for dark-themed applications
3. **Validator Node Badge**: Small format identification
4. **Marketing Materials**: Business cards, presentations, website
5. **Social Media**: Profile pictures, cover images

### 🔧 **AI Generation Prompts**

#### **DALL-E 3 Prompt**
```
Professional logo design featuring a geometric polygonal elephant head in onyx black with white highlights. Modern low-poly faceted style with subtle neon violet edge lighting. Clean minimalist design for a cryptographic trust platform called "ONNYX". Transparent background, high contrast, suitable for app icon and tech branding. Cybernetic aesthetic with sharp angular facets creating depth and dimension.
```

#### **Midjourney Prompt**
```
geometric polygonal elephant logo, onyx black faceted design, white highlights, subtle neon violet glow edges, modern minimalist tech aesthetic, transparent background, high contrast, scalable vector style, cryptographic platform branding, clean sharp facets, cybernetic look --v 6 --style raw --ar 1:1
```

#### **Stable Diffusion Prompt**
```
professional polygonal elephant logo, geometric faceted design, onyx black color palette, white highlights, subtle neon violet accent lighting, modern minimalist style, transparent background, high contrast, tech company branding, clean vector graphics, cybernetic aesthetic, sharp angular geometry, suitable for app icon
```

### 🎨 **Color Variations**

#### **Version A: Violet Accent**
- Primary: Onyx Black (#1C1C1C)
- Secondary: White (#FFFFFF)
- Accent: Electric Violet (#8A2BE2)
- Glow: Soft violet aura (#8A2BE2 at 30% opacity)

#### **Version B: Teal Accent**
- Primary: Onyx Black (#1C1C1C)
- Secondary: White (#FFFFFF)
- Accent: Dark Turquoise (#00CED1)
- Glow: Soft teal aura (#00CED1 at 30% opacity)

#### **Version C: Gold Accent**
- Primary: Onyx Black (#1C1C1C)
- Secondary: White (#FFFFFF)
- Accent: Electric Gold (#FFD700)
- Glow: Soft gold aura (#FFD700 at 30% opacity)

### 📐 **Design Guidelines**

#### **Geometric Structure**
- Use triangular facets to create the elephant silhouette
- Maintain recognizable elephant features (trunk, ears, tusks optional)
- Ensure facets create depth through strategic shading
- Keep vertex count moderate for clean scaling

#### **Typography (for wordmark)**
- Font: Modern sans-serif (similar to Montserrat Bold or Roboto Bold)
- Spacing: Generous letter spacing for tech aesthetic
- Color: Match primary onyx black or white depending on background
- Position: Below or beside the elephant icon

#### **Composition**
- Icon should work in square format (1:1 ratio)
- Leave adequate padding for small format usage
- Ensure elephant faces forward or slightly angled for engagement
- Balance geometric complexity with simplicity

### ✅ **Quality Checklist**
- [ ] Recognizable as an elephant at small sizes
- [ ] Works on both light and dark backgrounds
- [ ] Scales cleanly from 16px to large format
- [ ] Conveys trust, security, and innovation
- [ ] Unique and memorable design
- [ ] Professional appearance suitable for enterprise use
- [ ] Consistent with crypto/blockchain industry aesthetics

### 🚀 **Delivery Requirements**
1. **Primary Logo**: Full color with chosen accent
2. **Icon Version**: Standalone elephant symbol
3. **Monochrome**: Black and white version
4. **Wordmark**: Text-only version of "ONNYX"
5. **Usage Guide**: Minimum sizes, spacing, color codes

### 💡 **Additional Concepts**
Consider these alternative approaches if the primary concept needs refinement:
- **Circuit Pattern Integration**: Subtle circuit board patterns within facets
- **Blockchain Nodes**: Small connected dots representing network nodes
- **Crystalline Structure**: More crystal-like faceting for premium feel
- **Gradient Facets**: Subtle gradients within individual facets for depth
