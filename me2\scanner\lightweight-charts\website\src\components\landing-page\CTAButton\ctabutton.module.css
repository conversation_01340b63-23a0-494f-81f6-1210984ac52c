.SquareButton {
	font-style: normal;
	font-weight: 600;
	font-size: 18px;
	line-height: 24px;
	letter-spacing: -0.025em;
	font-feature-settings: 'tnum' on, 'lnum' on;

	border-radius: 12px;
	padding: 16px 32px;
	display: flex;
	align-items: center;
	background-color: var(--cta-button-secondary-background);
	color: var(--cta-button-text-color);
	/* Safari doesn't support border-radius and outline being used together */
	box-shadow:inset 0px 0px 0px 1px var(--cta-button-border-color);
}

.SquareButton:hover {
	text-decoration: none;
	color: var(--cta-button-text-color);
	background-color: var(--cta-button-secondary-background-hover);
	box-shadow: none;
}

.SquareButton:active {
	background-color: var(--cta-button-secondary-background-active);
	box-shadow: none;
}

.SquareButton[data-primary] {
	background-color: var(--cta-button-primary-background);
	color: #fff;
	box-shadow: none;
}

.SquareButton[data-primary]:hover {
	background-color: var(--cta-button-primary-background-hover);
	color: #ffffff;
}

.SquareButton[data-primary]:active {
	background-color: var(--cta-button-primary-background-active);
}

.SquareButton svg {
	margin-left: 12px;
	color: var(--cta-svg-color);
}

.SquareButton:focus-visible {
	outline-color: var(--tv-blue-500);
	outline-width: 2px;
	outline-offset: 4px;
}

/* Phone-Vertical */
@media (max-width: 567px) {
	.SquareButton {
		justify-content: center;
	}
}
