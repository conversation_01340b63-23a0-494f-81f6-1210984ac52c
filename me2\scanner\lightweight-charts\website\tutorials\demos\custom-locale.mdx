---
title: Custom locale
sidebar_label: Custom locale
description: An example of how to set a custom locale for the chart
pagination_prev: null
pagination_next: null
keywords:
  - locale
  - language
  - international
  - internationalization
  - i18n
---

In this example, the Lightweight Charts™ library allows for a change in the
locale of the chart rendering, enabling customization to best suit the end-user.
An initial chart is displayed in the default locale.

The function `setLocale(locale)` is defined to change the locale of the chart
using `chart.applyOptions` method. It adjusts the `localization` property of the
chart options, specifically the `locale` and `dateFormat` options. The
`dateFormat` varies depending on the set locale to mirror customary date formats
in respective regions.

A selection of buttons are created, each representing a distinct locale (like
'es-ES', 'en-US', 'ja-JP'). On clicking any of these buttons, its respective
locale is applied to the chart by invoking `setLocale(locale)`. This dynamically
adjusts the date formatting for the chart data, demonstrating the flexibility of
the Lightweight Charts™ in catering to an international audience.

### API Reference

- [ChartOptions.localization](/docs/api/interfaces/ChartOptionsBase#localization)

import UsageGuidePartial from '../_usage-guide-partial.mdx';
import CodeBlock from '@theme/CodeBlock';
import code from '!!raw-loader!./custom-locale.js';

<CodeBlock
	replaceThemeConstants
	chart
	className="language-js"
	hideableCode
	chartOnTop
	codeUsage={<UsageGuidePartial />}
>
	{code}
</CodeBlock>
