#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Crypto Charts with Support and Resistance Levels
-----------------------------------------------
A script to display cryptocurrency charts with support and resistance levels using CCXT and lightweight-charts.
"""

import pandas as pd
import pandas_ta as ta
import numpy as np
import ccxt
from lightweight_charts import Chart

class SupportResistanceChart:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1h'):
        """
        Initialize the SupportResistanceChart class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1h')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def find_support_resistance(self, df, window=10, threshold=0.02):
        """
        Find support and resistance levels.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            window (int): Window size for finding local extrema (default: 10)
            threshold (float): Threshold for clustering levels (default: 0.02)
            
        Returns:
            tuple: (support_levels, resistance_levels)
        """
        # Find local minima (support)
        df['min'] = df['low'].rolling(window=window, center=True).min()
        df['is_support'] = (df['low'] == df['min']) & (df['low'].shift(window//2) > df['low']) & (df['low'].shift(-window//2) > df['low'])
        
        # Find local maxima (resistance)
        df['max'] = df['high'].rolling(window=window, center=True).max()
        df['is_resistance'] = (df['high'] == df['max']) & (df['high'].shift(window//2) < df['high']) & (df['high'].shift(-window//2) < df['high'])
        
        # Get support and resistance levels
        support_levels = df[df['is_support']]['low'].tolist()
        resistance_levels = df[df['is_resistance']]['high'].tolist()
        
        # Cluster levels
        support_levels = self._cluster_levels(support_levels, threshold)
        resistance_levels = self._cluster_levels(resistance_levels, threshold)
        
        return support_levels, resistance_levels
    
    def _cluster_levels(self, levels, threshold):
        """
        Cluster similar price levels.
        
        Args:
            levels (list): List of price levels
            threshold (float): Threshold for clustering
            
        Returns:
            list: Clustered price levels
        """
        if not levels:
            return []
        
        # Sort levels
        levels = sorted(levels)
        
        # Cluster levels
        clustered_levels = []
        current_cluster = [levels[0]]
        
        for i in range(1, len(levels)):
            # If the level is close to the current cluster, add it to the cluster
            if (levels[i] - current_cluster[-1]) / current_cluster[-1] < threshold:
                current_cluster.append(levels[i])
            else:
                # Otherwise, add the average of the current cluster to the result
                clustered_levels.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [levels[i]]
        
        # Add the last cluster
        if current_cluster:
            clustered_levels.append(sum(current_cluster) / len(current_cluster))
        
        return clustered_levels
    
    def create_chart(self, df, support_levels=None, resistance_levels=None):
        """
        Create and display a chart with support and resistance levels.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            support_levels (list): List of support levels (default: None)
            resistance_levels (list): List of resistance levels (default: None)
        """
        # Create chart
        chart = Chart(volume_enabled=True)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Set chart data
        chart.set(df_formatted)
        
        # Add support levels
        if support_levels:
            for level in support_levels:
                chart.create_horizontal_line(level, color='green', line_style='dashed', line_width=1)
        
        # Add resistance levels
        if resistance_levels:
            for level in resistance_levels:
                chart.create_horizontal_line(level, color='red', line_style='dashed', line_width=1)
        
        # Set chart title
        chart.watermark(f"{self.symbol} - {self.timeframe} (Support/Resistance)")
        
        # Show chart
        chart.show(block=True)

class MultiTimeframeChart:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframes=None):
        """
        Initialize the MultiTimeframeChart class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframes (list): List of timeframes (default: ['5m', '15m', '1h', '4h'])
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframes = timeframes or ['5m', '15m', '1h', '4h']
        self.exchange = self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, timeframe, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            timeframe (str): The chart timeframe
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def find_support_resistance(self, df, window=10, threshold=0.02):
        """
        Find support and resistance levels.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            window (int): Window size for finding local extrema (default: 10)
            threshold (float): Threshold for clustering levels (default: 0.02)
            
        Returns:
            tuple: (support_levels, resistance_levels)
        """
        # Find local minima (support)
        df['min'] = df['low'].rolling(window=window, center=True).min()
        df['is_support'] = (df['low'] == df['min']) & (df['low'].shift(window//2) > df['low']) & (df['low'].shift(-window//2) > df['low'])
        
        # Find local maxima (resistance)
        df['max'] = df['high'].rolling(window=window, center=True).max()
        df['is_resistance'] = (df['high'] == df['max']) & (df['high'].shift(window//2) < df['high']) & (df['high'].shift(-window//2) < df['high'])
        
        # Get support and resistance levels
        support_levels = df[df['is_support']]['low'].tolist()
        resistance_levels = df[df['is_resistance']]['high'].tolist()
        
        # Cluster levels
        support_levels = self._cluster_levels(support_levels, threshold)
        resistance_levels = self._cluster_levels(resistance_levels, threshold)
        
        return support_levels, resistance_levels
    
    def _cluster_levels(self, levels, threshold):
        """
        Cluster similar price levels.
        
        Args:
            levels (list): List of price levels
            threshold (float): Threshold for clustering
            
        Returns:
            list: Clustered price levels
        """
        if not levels:
            return []
        
        # Sort levels
        levels = sorted(levels)
        
        # Cluster levels
        clustered_levels = []
        current_cluster = [levels[0]]
        
        for i in range(1, len(levels)):
            # If the level is close to the current cluster, add it to the cluster
            if (levels[i] - current_cluster[-1]) / current_cluster[-1] < threshold:
                current_cluster.append(levels[i])
            else:
                # Otherwise, add the average of the current cluster to the result
                clustered_levels.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [levels[i]]
        
        # Add the last cluster
        if current_cluster:
            clustered_levels.append(sum(current_cluster) / len(current_cluster))
        
        return clustered_levels
    
    def create_multi_timeframe_chart(self):
        """Create and display a multi-timeframe chart with support and resistance levels."""
        # Create main chart
        chart = Chart(inner_width=0.5, inner_height=0.5)
        charts = [chart]
        
        # Create subcharts
        for i in range(1, len(self.timeframes)):
            if i == 1:
                subchart = chart.create_subchart(position='right', width=0.5, height=0.5)
            elif i % 2 == 0:
                subchart = charts[i-2].create_subchart(position='bottom', width=0.5, height=0.5)
            else:
                subchart = charts[i-2].create_subchart(position='right', width=0.5, height=0.5)
            charts.append(subchart)
        
        # Fetch data and set charts
        for i, timeframe in enumerate(self.timeframes):
            df = self.fetch_ohlcv(timeframe)
            if df is not None:
                # Find support and resistance levels
                support_levels, resistance_levels = self.find_support_resistance(df)
                
                # Format DataFrame for lightweight-charts
                df_formatted = df.copy()
                df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                
                # Set chart data
                charts[i].set(df_formatted)
                
                # Add support levels
                for level in support_levels:
                    charts[i].create_horizontal_line(level, color='green', line_style='dashed', line_width=1)
                
                # Add resistance levels
                for level in resistance_levels:
                    charts[i].create_horizontal_line(level, color='red', line_style='dashed', line_width=1)
                
                # Set chart title
                charts[i].watermark(f"{self.symbol} - {timeframe}")
        
        # Show chart
        chart.show(block=True)

def main():
    """Main function to demonstrate the SupportResistanceChart and MultiTimeframeChart classes."""
    # Single timeframe chart with support and resistance levels
    sr_chart = SupportResistanceChart(exchange_id='binance', symbol='BTC/USDT', timeframe='1h')
    df = sr_chart.fetch_ohlcv(limit=500)
    if df is not None:
        support_levels, resistance_levels = sr_chart.find_support_resistance(df)
        sr_chart.create_chart(df, support_levels, resistance_levels)
    
    # Multi-timeframe chart with support and resistance levels
    mtf_chart = MultiTimeframeChart(exchange_id='binance', symbol='BTC/USDT', timeframes=['5m', '15m', '1h', '4h'])
    mtf_chart.create_multi_timeframe_chart()

if __name__ == '__main__':
    main()
