"""
Chart Tab for ME2 Trading Platform

This module implements a chart tab with real-time WebSocket data visualization,
trade aggregation, and custom indicators.
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
                              QLabel, QFrame, QSplitter, QPushButton, QGroupBox,
                              QGridLayout, QCheckBox, QSizePolicy)
from PySide6.QtCore import Qt, QTimer, Signal, Slot, QSize
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtGui import QColor, QPainter, QPixmap, QFont, QPen

import os
import json
import time
from datetime import datetime
import logging
import threading
import queue

# Import WebSocket worker
from workers import WebSocketWorker

class TradeAggregator:
    """
    Aggregates trade data into OHLCV candles of different timeframes.
    """
    def __init__(self):
        self.timeframes = {
            "1s": 1,
            "5s": 5,
            "15s": 15,
            "30s": 30,
            "45s": 45,
            "60s": 60
        }

        # Initialize candles for each timeframe
        self.candles = {}
        for tf in self.timeframes:
            self.candles[tf] = []

        # Current candle being built for each timeframe
        self.current_candles = {}
        for tf in self.timeframes:
            self.current_candles[tf] = None

        # Last update time
        self.last_update_time = time.time()

    def process_trade(self, trade):
        """
        Process a new trade and update candles for all timeframes.

        Args:
            trade (dict): Trade data with timestamp, price, amount, and side
        """
        timestamp = trade.get('timestamp', int(time.time() * 1000))
        price = trade.get('price', 0)
        amount = trade.get('amount', 0)
        side = trade.get('side', 'buy')  # 'buy' or 'sell'

        # Update candles for each timeframe
        for tf, seconds in self.timeframes.items():
            # Calculate candle timestamp (floor to timeframe)
            candle_timestamp = int(timestamp / (seconds * 1000)) * (seconds * 1000)

            # Check if we need to start a new candle
            if self.current_candles[tf] is None or self.current_candles[tf]['timestamp'] < candle_timestamp:
                # Close previous candle if it exists
                if self.current_candles[tf] is not None:
                    self.candles[tf].append(self.current_candles[tf])

                # Start a new candle
                self.current_candles[tf] = {
                    'timestamp': candle_timestamp,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': amount,
                    'trades': 1
                }
            else:
                # Update existing candle
                current = self.current_candles[tf]
                current['high'] = max(current['high'], price)
                current['low'] = min(current['low'], price)
                current['close'] = price
                current['volume'] += amount
                current['trades'] += 1

        self.last_update_time = time.time()

    def get_candles(self, timeframe, limit=100):
        """
        Get the latest candles for a specific timeframe.

        Args:
            timeframe (str): Timeframe key (e.g., "1s", "5s", etc.)
            limit (int): Maximum number of candles to return

        Returns:
            list: List of candle dictionaries
        """
        if timeframe not in self.timeframes:
            return []

        # Get all completed candles plus the current one being built
        all_candles = self.candles.get(timeframe, [])[:]
        if self.current_candles.get(timeframe) is not None:
            all_candles.append(self.current_candles[timeframe])

        # Return the most recent candles up to the limit
        return all_candles[-limit:]

class ChartTab(QWidget):
    """
    Chart tab with real-time WebSocket data visualization.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.symbol = "BTC/USDT"
        self.timeframe = "1s"
        self.ws_worker = None
        self.trade_aggregator = TradeAggregator()
        self.trades_queue = queue.Queue()
        self.processing_thread = None
        self.running = False

        # Initialize UI
        self.init_ui()

        # Start WebSocket connection
        self.start_websocket()

        # Start trade processing thread
        self.start_processing_thread()

        # Set up timer for chart updates
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_chart)
        self.update_timer.start(1000)  # Update every second

    def init_ui(self):
        """Initialize the user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create header for chart tab
        header_frame = QFrame()
        header_frame.setObjectName("chartHeaderFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Let the layout determine the height
        header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Left side - Logo and title
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)  # Reduced spacing

        # Create a chart icon logo
        logo_label = QLabel()
        logo_label.setFixedSize(40, 40)  # Smaller logo

        # Create a chart icon using Unicode character
        chart_icon = QPixmap(40, 40)
        chart_icon.fill(Qt.transparent)
        painter = QPainter(chart_icon)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a circular background - different color for chart tab
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor("#2196F3"))  # Blue circle for charts
        painter.drawEllipse(0, 0, 40, 40)

        # Draw the chart symbol for charts
        painter.setPen(QPen(QColor("#FFFFFF"), 2))  # White text
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(chart_icon.rect(), Qt.AlignCenter, "📊")
        painter.end()

        logo_label.setPixmap(chart_icon)
        left_layout.addWidget(logo_label)

        # Title and subtitle in vertical layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(0)  # Minimal spacing
        text_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("Real-Time Charts")
        title_label.setObjectName("chartTitleLabel")
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("Live Trading Data Visualization")
        subtitle_label.setObjectName("chartSubtitleLabel")
        text_layout.addWidget(subtitle_label)

        left_layout.addLayout(text_layout)
        header_layout.addLayout(left_layout)

        # Add spacer to push everything to the left
        header_layout.addStretch(1)

        # Add header to layout
        main_layout.addWidget(header_frame)

        # Control panel
        control_frame = QFrame()
        control_frame.setObjectName("chartControlFrame")
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(10)

        # Symbol selection
        symbol_group = QGroupBox("Symbol")
        symbol_group.setObjectName("controlGroupBox")
        symbol_layout = QVBoxLayout(symbol_group)
        symbol_layout.setContentsMargins(10, 20, 10, 10)
        symbol_layout.setSpacing(5)

        self.symbol_combo = QComboBox()
        self.symbol_combo.setObjectName("mainComboBox")
        self.symbol_combo.addItems(["BTC/USDT", "ETH/USDT", "SOL/USDT", "XRP/USDT", "ADA/USDT", "DOGE/USDT"])
        self.symbol_combo.setCurrentText(self.symbol)
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        symbol_layout.addWidget(self.symbol_combo)

        control_layout.addWidget(symbol_group)

        # Timeframe selection
        timeframe_group = QGroupBox("Timeframe")
        timeframe_group.setObjectName("controlGroupBox")
        timeframe_layout = QVBoxLayout(timeframe_group)
        timeframe_layout.setContentsMargins(10, 20, 10, 10)
        timeframe_layout.setSpacing(5)

        self.timeframe_combo = QComboBox()
        self.timeframe_combo.setObjectName("mainComboBox")
        self.timeframe_combo.addItems(["1s", "5s", "15s", "30s", "45s", "60s"])
        self.timeframe_combo.setCurrentText(self.timeframe)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        timeframe_layout.addWidget(self.timeframe_combo)

        control_layout.addWidget(timeframe_group)

        # Chart type selection
        chart_type_group = QGroupBox("Chart Type")
        chart_type_group.setObjectName("controlGroupBox")
        chart_type_layout = QVBoxLayout(chart_type_group)
        chart_type_layout.setContentsMargins(10, 20, 10, 10)
        chart_type_layout.setSpacing(5)

        self.chart_type_combo = QComboBox()
        self.chart_type_combo.setObjectName("mainComboBox")
        self.chart_type_combo.addItems(["Candlestick", "Line", "Area", "Tick"])
        self.chart_type_combo.setCurrentText("Candlestick")
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        chart_type_layout.addWidget(self.chart_type_combo)

        control_layout.addWidget(chart_type_group)

        # Indicators selection
        indicators_group = QGroupBox("Indicators")
        indicators_group.setObjectName("controlGroupBox")
        indicators_layout = QGridLayout(indicators_group)
        indicators_layout.setContentsMargins(10, 20, 10, 10)
        indicators_layout.setSpacing(5)

        # Create indicator checkboxes
        self.ema_check = QCheckBox("EMA")
        self.ema_check.setChecked(True)
        self.ema_check.stateChanged.connect(self.update_indicators)
        indicators_layout.addWidget(self.ema_check, 0, 0)

        self.ema_bands_check = QCheckBox("EMA Bands")
        self.ema_bands_check.setChecked(True)
        self.ema_bands_check.stateChanged.connect(self.update_indicators)
        indicators_layout.addWidget(self.ema_bands_check, 0, 1)

        self.rsi_check = QCheckBox("RSI")
        self.rsi_check.setChecked(False)
        self.rsi_check.stateChanged.connect(self.update_indicators)
        indicators_layout.addWidget(self.rsi_check, 1, 0)

        self.macd_check = QCheckBox("MACD")
        self.macd_check.setChecked(False)
        self.macd_check.stateChanged.connect(self.update_indicators)
        indicators_layout.addWidget(self.macd_check, 1, 1)

        control_layout.addWidget(indicators_group)

        main_layout.addWidget(control_frame)

        # Chart container
        self.chart_container = QWebEngineView()
        self.chart_container.setMinimumHeight(500)
        main_layout.addWidget(self.chart_container, 1)  # Give it a stretch factor of 1

        # Load initial chart
        self.load_chart_html()

    def load_chart_html(self):
        """Load the HTML content with the chart"""
        # Get the path to the lightweight-charts library
        charts_js_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../scanner/lightweight-charts/dist/lightweight-charts.standalone.production.js'))

        try:
            with open(charts_js_path, 'r', encoding='utf-8') as f:
                charts_js_content = f.read()
        except Exception as e:
            print(f"Error loading charts library: {e}")
            # Fallback to CDN
            charts_js_content = None

        # Create the HTML content with the chart
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width,initial-scale=1.0">
            {f'<script>{charts_js_content}</script>' if charts_js_content else '<script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>'}
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                    overflow: hidden;
                    background-color: #131722;
                }}
                #chart-container {{
                    position: absolute;
                    width: 100%;
                    height: 100%;
                }}
            </style>
        </head>
        <body>
            <div id="chart-container"></div>
            <script>
                // Initialize chart
                const chartContainer = document.getElementById('chart-container');

                // Create the chart
                const chart = LightweightCharts.createChart(chartContainer, {{
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                    layout: {{
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                    }},
                    grid: {{
                        vertLines: {{
                            color: '#1e2029',
                        }},
                        horzLines: {{
                            color: '#1e2029',
                        }},
                    }},
                    crosshair: {{
                        mode: LightweightCharts.CrosshairMode.Normal,
                    }},
                    timeScale: {{
                        borderColor: '#2b2b43',
                        timeVisible: true,
                        secondsVisible: true,
                    }},
                }});

                // Create candlestick series - use proper API method
                let candleSeries;
                try {{
                    // Try the new API first
                    candleSeries = chart.addCandlestickSeries({{
                        upColor: '#26a69a',
                        downColor: '#ef5350',
                        borderUpColor: '#26a69a',
                        borderDownColor: '#ef5350',
                        wickUpColor: '#26a69a',
                        wickDownColor: '#ef5350',
                    }});
                }} catch (e) {{
                    // Fallback to older API if needed
                    try {{
                        candleSeries = chart.addSeries(LightweightCharts.CandlestickSeries, {{
                            upColor: '#26a69a',
                            downColor: '#ef5350',
                            borderUpColor: '#26a69a',
                            borderDownColor: '#ef5350',
                            wickUpColor: '#26a69a',
                            wickDownColor: '#ef5350',
                        }});
                    }} catch (e2) {{
                        console.error('Failed to create candlestick series:', e2);
                        // Create a basic line series as fallback
                        candleSeries = chart.addLineSeries({{
                            color: '#26a69a',
                            lineWidth: 2,
                        }});
                    }}
                }}

                // Create line series for EMA with error handling
                let emaSeries;
                try {{
                    emaSeries = chart.addLineSeries({{
                        color: '#2962FF',
                        lineWidth: 2,
                        priceLineVisible: false,
                        lastValueVisible: false,
                    }});
                }} catch (e) {{
                    console.error('Failed to create EMA line series:', e);
                    // Create a basic series as fallback
                    emaSeries = null;
                }}

                // Create line series for EMA bands with error handling
                let emaBandUpper, emaBandLower, buyMarkers, sellMarkers;

                try {{
                    emaBandUpper = chart.addLineSeries({{
                        color: 'rgba(76, 175, 80, 0.5)',
                        lineWidth: 1,
                        priceLineVisible: false,
                        lastValueVisible: false,
                    }});
                }} catch (e) {{
                    console.error('Failed to create EMA band upper series:', e);
                    emaBandUpper = null;
                }}

                try {{
                    emaBandLower = chart.addLineSeries({{
                        color: 'rgba(239, 83, 80, 0.5)',
                        lineWidth: 1,
                        priceLineVisible: false,
                        lastValueVisible: false,
                    }});
                }} catch (e) {{
                    console.error('Failed to create EMA band lower series:', e);
                    emaBandLower = null;
                }}

                // Create marker series for trades with error handling
                try {{
                    buyMarkers = chart.addLineSeries({{
                        color: 'rgba(0, 0, 0, 0)',
                        lineWidth: 0,
                        priceLineVisible: false,
                        lastValueVisible: false,
                    }});
                }} catch (e) {{
                    console.error('Failed to create buy markers series:', e);
                    buyMarkers = null;
                }}

                try {{
                    sellMarkers = chart.addLineSeries({{
                        color: 'rgba(0, 0, 0, 0)',
                        lineWidth: 0,
                        priceLineVisible: false,
                        lastValueVisible: false,
                    }});
                }} catch (e) {{
                    console.error('Failed to create sell markers series:', e);
                    sellMarkers = null;
                }}

                // Function to update chart data with null checks
                window.updateChartData = function(candles, emaData, emaBandUpperData, emaBandLowerData, buyData, sellData) {{
                    try {{
                        if (candleSeries) {{
                            candleSeries.setData(candles);
                        }}

                        if (emaSeries && emaData && emaData.length > 0) {{
                            emaSeries.setData(emaData);
                        }}

                        if (emaBandUpper && emaBandUpperData && emaBandUpperData.length > 0) {{
                            emaBandUpper.setData(emaBandUpperData);
                        }}

                        if (emaBandLower && emaBandLowerData && emaBandLowerData.length > 0) {{
                            emaBandLower.setData(emaBandLowerData);
                        }}

                        if (buyMarkers && buyData && buyData.length > 0) {{
                            buyMarkers.setMarkers(buyData);
                        }}

                        if (sellMarkers && sellData && sellData.length > 0) {{
                            sellMarkers.setMarkers(sellData);
                        }}

                        // Fit content to view
                        if (chart && chart.timeScale) {{
                            chart.timeScale().fitContent();
                        }}
                    }} catch (e) {{
                        console.error('Error updating chart data:', e);
                    }}
                }};

                // Handle window resize
                window.addEventListener('resize', function() {{
                    chart.resize(
                        chartContainer.clientWidth,
                        chartContainer.clientHeight
                    );
                }});

                // Signal that JavaScript is ready
                window.jsReady = true;
            </script>
        </body>
        </html>
        """

        # Load the HTML content
        self.chart_container.setHtml(html_content)

    def start_websocket(self):
        """Start the WebSocket connection"""
        if self.ws_worker is not None:
            self.ws_worker.stop()

        self.ws_worker = WebSocketWorker(self.symbol)
        self.ws_worker.trades_updated.connect(self.on_trades_updated)
        self.ws_worker.start()

    def start_processing_thread(self):
        """Start the trade processing thread"""
        self.running = True
        self.processing_thread = threading.Thread(target=self.process_trades)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def process_trades(self):
        """Process trades from the queue"""
        while self.running:
            try:
                # Get trades from the queue with a timeout
                trades = []
                try:
                    while True:
                        trade = self.trades_queue.get(timeout=0.1)
                        trades.append(trade)
                        self.trades_queue.task_done()
                except queue.Empty:
                    pass

                # Process each trade
                for trade in trades:
                    self.trade_aggregator.process_trade(trade)

                # Sleep a bit to avoid high CPU usage
                time.sleep(0.01)
            except Exception as e:
                logging.error(f"Error processing trades: {e}")
                time.sleep(1)  # Sleep longer after an error

    def on_trades_updated(self, trades):
        """Handle new trades from WebSocket"""
        for trade in trades:
            # Add trade to the queue for processing
            self.trades_queue.put(trade)

    def on_symbol_changed(self, symbol):
        """Handle symbol change"""
        self.symbol = symbol

        # Restart WebSocket with new symbol
        self.start_websocket()

        # Clear existing data
        self.trade_aggregator = TradeAggregator()

        # Update chart
        self.update_chart()

    def on_timeframe_changed(self, timeframe):
        """Handle timeframe change"""
        self.timeframe = timeframe

        # Update chart
        self.update_chart()

    def on_chart_type_changed(self, chart_type):
        """Handle chart type change"""
        # Reload chart with new type
        self.load_chart_html()

        # Update chart
        self.update_chart()

    def update_indicators(self):
        """Update indicators based on checkbox states"""
        # Update chart
        self.update_chart()

    def calculate_ema(self, candles, period=20):
        """Calculate EMA for the given candles"""
        if not candles or len(candles) < period:
            return []

        # Extract close prices
        prices = [candle['close'] for candle in candles]

        # Calculate EMA
        multiplier = 2 / (period + 1)
        ema = [prices[0]]  # Start with first price

        for i in range(1, len(prices)):
            ema.append((prices[i] * multiplier) + (ema[i-1] * (1 - multiplier)))

        # Format for chart
        ema_data = []
        for i in range(len(candles)):
            if i >= period - 1:
                ema_data.append({
                    'time': candles[i]['timestamp'] / 1000,
                    'value': ema[i]
                })

        return ema_data

    def calculate_ema_bands(self, candles, ema_data, deviation=2.0):
        """Calculate EMA bands for the given candles and EMA"""
        if not candles or not ema_data or len(candles) < 20:
            return [], []

        # Calculate standard deviation of prices from EMA
        ema_values = {item['time']: item['value'] for item in ema_data}

        # Calculate squared differences from EMA
        squared_diffs = []
        for candle in candles:
            time = candle['timestamp'] / 1000
            if time in ema_values:
                diff = candle['close'] - ema_values[time]
                squared_diffs.append(diff ** 2)

        if not squared_diffs:
            return [], []

        # Calculate standard deviation
        std_dev = (sum(squared_diffs) / len(squared_diffs)) ** 0.5

        # Create upper and lower bands
        upper_band = []
        lower_band = []

        for item in ema_data:
            upper_band.append({
                'time': item['time'],
                'value': item['value'] + (std_dev * deviation)
            })

            lower_band.append({
                'time': item['time'],
                'value': item['value'] - (std_dev * deviation)
            })

        return upper_band, lower_band

    def format_candles_for_chart(self, candles):
        """Format candles for the chart"""
        formatted_candles = []

        for candle in candles:
            formatted_candles.append({
                'time': candle['timestamp'] / 1000,
                'open': candle['open'],
                'high': candle['high'],
                'low': candle['low'],
                'close': candle['close']
            })

        return formatted_candles

    def format_trades_for_markers(self, trades):
        """Format trades as markers for the chart"""
        buy_markers = []
        sell_markers = []

        for trade in trades:
            marker = {
                'time': trade['timestamp'] / 1000,
                'position': 'aboveBar',
                'color': '#26a69a' if trade['side'] == 'buy' else '#ef5350',
                'shape': 'circle',
                'size': 1
            }

            if trade['side'] == 'buy':
                buy_markers.append(marker)
            else:
                sell_markers.append(marker)

        return buy_markers, sell_markers

    def update_chart(self):
        """Update the chart with the latest data"""
        try:
            # Get candles for the selected timeframe
            candles = self.trade_aggregator.get_candles(self.timeframe)

            if not candles:
                return

            # Format candles for the chart
            formatted_candles = self.format_candles_for_chart(candles)

            # Calculate indicators
            ema_data = []
            upper_band = []
            lower_band = []

            if self.ema_check.isChecked():
                ema_data = self.calculate_ema(candles)

                if self.ema_bands_check.isChecked():
                    upper_band, lower_band = self.calculate_ema_bands(candles, ema_data)

            # Get recent trades for markers
            recent_trades = []
            for tf in self.trade_aggregator.timeframes:
                for candle in self.trade_aggregator.candles.get(tf, []):
                    if candle.get('trades', 0) > 0:
                        # Create synthetic trades for demonstration
                        recent_trades.append({
                            'timestamp': candle['timestamp'],
                            'price': candle['close'],
                            'amount': candle['volume'] / candle['trades'],
                            'side': 'buy' if candle['close'] > candle['open'] else 'sell'
                        })

            # Format trades as markers
            buy_markers, sell_markers = self.format_trades_for_markers(recent_trades)

            # Update the chart
            script = f"""
            window.updateChartData(
                {json.dumps(formatted_candles)},
                {json.dumps(ema_data)},
                {json.dumps(upper_band)},
                {json.dumps(lower_band)},
                {json.dumps(buy_markers)},
                {json.dumps(sell_markers)}
            );
            """

            self.chart_container.page().runJavaScript(script)

        except Exception as e:
            logging.error(f"Error updating chart: {e}")

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop the WebSocket connection
        if self.ws_worker is not None:
            self.ws_worker.stop()

        # Stop the processing thread
        self.running = False
        if self.processing_thread is not None:
            self.processing_thread.join(timeout=1.0)

        # Accept the close event
        event.accept()
