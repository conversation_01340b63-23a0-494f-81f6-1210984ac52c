#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Real-time Crypto Charts with CCXT and Lightweight Charts (Fixed Version)
-------------------------------------------------------
A script to display real-time cryptocurrency charts with technical indicators using CCXT and lightweight-charts.
This version doesn't rely on pandas_ta to avoid the numpy.NaN import error.
"""

import asyncio
import pandas as pd
import numpy as np
import ccxt
import time
from datetime import datetime
from lightweight_charts import Chart

class RealtimeCryptoChart:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1m'):
        """
        Initialize the RealtimeCryptoChart class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1m')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        self.chart = None
        self.running = False
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=100):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 100)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def calculate_sma(self, df, period=20):
        """Calculate Simple Moving Average."""
        sma = df['close'].rolling(window=period).mean()
        sma_df = pd.DataFrame({'time': df['time'], 'value': sma})
        return sma_df.dropna()
    
    def calculate_ema(self, df, period=50):
        """Calculate Exponential Moving Average."""
        ema = df['close'].ewm(span=period, adjust=False).mean()
        ema_df = pd.DataFrame({'time': df['time'], 'value': ema})
        return ema_df.dropna()
    
    def calculate_rsi(self, df, period=14):
        """Calculate Relative Strength Index."""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # For periods after the initial period
        for i in range(period, len(df)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (period-1) + gain.iloc[i]) / period
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (period-1) + loss.iloc[i]) / period
        
        rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))
        
        rsi_df = pd.DataFrame({'time': df['time'], 'value': rsi})
        return rsi_df.dropna()
    
    def calculate_indicators(self, df):
        """
        Calculate technical indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            
        Returns:
            dict: Dictionary with indicator DataFrames
        """
        indicators = {}
        
        # Calculate SMA
        sma20 = self.calculate_sma(df, period=20)
        indicators['sma20'] = sma20
        
        # Calculate EMA
        ema50 = self.calculate_ema(df, period=50)
        indicators['ema50'] = ema50
        
        # Calculate RSI
        rsi = self.calculate_rsi(df, period=14)
        indicators['rsi'] = rsi
        
        return indicators
    
    def start_chart(self):
        """Start the real-time chart."""
        # Fetch initial data
        df = self.fetch_ohlcv(limit=100)
        if df is None:
            print("Failed to fetch initial data.")
            return
        
        # Calculate indicators
        indicators = self.calculate_indicators(df)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Create chart
        self.chart = Chart(volume_enabled=True)
        
        # Set chart data
        self.chart.set(df_formatted)
        
        # Add SMA line
        if 'sma20' in indicators:
            sma_formatted = indicators['sma20'].copy()
            sma_formatted['time'] = sma_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            sma_line = self.chart.create_line(color='blue', price_line=True, price_label=True)
            sma_line.set(sma_formatted)
            self.sma_line = sma_line
        
        # Add EMA line
        if 'ema50' in indicators:
            ema_formatted = indicators['ema50'].copy()
            ema_formatted['time'] = ema_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            ema_line = self.chart.create_line(color='red', price_line=True, price_label=True)
            ema_line.set(ema_formatted)
            self.ema_line = ema_line
        
        # Add RSI in a subchart
        if 'rsi' in indicators:
            rsi_formatted = indicators['rsi'].copy()
            rsi_formatted['time'] = rsi_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            rsi_chart = self.chart.create_subchart(height=0.2, position='bottom')
            rsi_line = rsi_chart.create_line(color='purple')
            rsi_line.set(rsi_formatted)
            
            # Add overbought/oversold lines
            rsi_chart.create_horizontal_line(70, color='red')
            rsi_chart.create_horizontal_line(30, color='green')
            rsi_chart.watermark('RSI')
            
            self.rsi_chart = rsi_chart
            self.rsi_line = rsi_line
        
        # Set chart title
        self.chart.watermark(f"{self.symbol} - {self.timeframe} (Real-time)")
        
        # Start update loop
        self.running = True
        self.update_loop()
        
        # Show chart
        self.chart.show(block=True)
    
    def update_loop(self):
        """Update the chart in a loop."""
        if not self.running:
            return
        
        # Schedule the next update
        self.chart.on_timer(self.update_chart, interval=10000)  # Update every 10 seconds
    
    def update_chart(self):
        """Update the chart with new data."""
        if not self.running:
            return
        
        try:
            # Fetch latest data
            latest_data = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=1)
            
            if not latest_data:
                print("No new data received.")
                return
            
            # Convert to DataFrame
            latest_df = pd.DataFrame(latest_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            latest_df['time'] = pd.to_datetime(latest_df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            latest_df = latest_df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            # Format time for chart
            latest_time = latest_df['time'].iloc[0].strftime('%Y-%m-%d %H:%M:%S')
            
            # Create update series
            update_series = {
                'time': latest_time,
                'open': float(latest_df['open'].iloc[0]),
                'high': float(latest_df['high'].iloc[0]),
                'low': float(latest_df['low'].iloc[0]),
                'close': float(latest_df['close'].iloc[0]),
                'volume': float(latest_df['volume'].iloc[0])
            }
            
            # Update chart
            self.chart.update(update_series)
            
            print(f"Updated chart with data for {latest_time}")
            
        except Exception as e:
            print(f"Error updating chart: {e}")
    
    def stop(self):
        """Stop the real-time updates."""
        self.running = False

def main():
    """Main function to demonstrate the RealtimeCryptoChart class."""
    # Create a real-time chart for BTC/USDT on Binance with 1-minute timeframe
    realtime_chart = RealtimeCryptoChart(exchange_id='binance', symbol='BTC/USDT', timeframe='1m')
    
    # Start the chart
    try:
        realtime_chart.start_chart()
    except KeyboardInterrupt:
        print("Stopping real-time updates...")
        realtime_chart.stop()

if __name__ == '__main__':
    main()
