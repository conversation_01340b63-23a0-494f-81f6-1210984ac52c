@echo off
echo Starting ME2 Trading Application...
echo Current directory: %CD%
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if PySide6 is available
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PySide6 is not installed
    echo Installing PySide6...
    pip install PySide6
    if errorlevel 1 (
        echo ERROR: Failed to install PySide6
        pause
        exit /b 1
    )
)

REM Check if other dependencies are available
echo Checking dependencies...
python -c "import ccxt, pyqtgraph, yaml, numpy, pandas" >nul 2>&1
if errorlevel 1 (
    echo Installing missing dependencies...
    pip install ccxt pyqtgraph pyyaml numpy pandas websocket-client requests
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if me2_stable.py exists
if not exist "me2_stable.py" (
    echo ERROR: me2_stable.py not found in current directory
    echo Current directory: %CD%
    echo Please run this script from the me2 application directory
    pause
    exit /b 1
)

REM Check if credentials.yaml exists
if not exist "credentials.yaml" (
    echo WARNING: credentials.yaml not found
    echo The application may not work properly without API credentials
    echo.
)

echo All dependencies are available
echo Starting ME2 Trading Application...
echo.

REM Run the application
python me2_stable.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error
    pause
)
