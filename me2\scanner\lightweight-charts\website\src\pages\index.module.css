:root {
	font-family: system-ui, -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>,
		Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

	--tv-cold-gray-50: #f8f9fd;
	--tv-cold-gray-100: #f0f3fa;
	--tv-cold-gray-150: #e0e3eb;
	--tv-cold-gray-300: #b2b5be;
	--tv-cold-gray-500: #787b86;
	--tv-cold-gray-550: #6a6d78;
	--tv-cold-gray-700: #434651;
	--tv-cold-gray-750: #363a45;
	--tv-cold-gray-800: #2a2e39;
	--tv-cold-gray-850: #1e222d;
	--tv-cold-gray-900: #131722;

	--tv-blue-50: #e3effd;
	--tv-blue-100: #bbd9fb;
	--tv-blue-200: #90bff9;
	--tv-blue-400: #3179f5;
	--tv-blue-500: #2962ff;
	--tv-blue-600: #1e53e5;
	--tv-blue-700: #1848cc;

	--tv-blue-a-800: #142e61;

	--container-padding: 100px;
	--container-offset-right-chart: var(--container-padding);
	--vertical-container-padding: 120px;
	--hero-margin-bottom: 80px;

	--card-corner-radius: 12px;
	--card-inner-inline-padding: 32px;
	--card-inner-block-padding: 32px;
	--card-gap: 16px;
	--card-grid-spacing: 16px;

	--card-grid-columns: 4;

	--card-background: var(--tv-cold-gray-100);
	--card-title-color: var(--tv-cold-gray-900);
	--card-content-color: var(--tv-cold-gray-550);

	--card-background-hover: var(--tv-cold-gray-150);
	--card-title-color-hover: var(--card-title-color);
	--card-content-color-hover: var(--card-content-color);

	--card-background-active: var(--tv-blue-50);
	--card-title-color-active: var(--tv-blue-500);
	--card-content-color-active: var(--tv-blue-400);

	--card-focus-ring-color: var(--tv-blue-500);

	--card-title-font-size: 28px;
	--card-title-line-height: 36px;
	--card-content-font-size: 20px;
	--card-content-line-height: 30px;

	--hero-title-color: var(--tv-cold-gray-900);
	--hero-content-color: var(--tv-cold-gray-550);

	--hero-title-font-size: 56px;
	--hero-title-line-height: 64px;

	--hero-content-font-size: 24px;
	--hero-content-line-height: 36px;

	--hero-content-gap-top: 16px;
	--hero-content-gap-bottom: 40px;

	--hero-main-width: 620px;
	--hero-flex-direction: row;

	--cta-button-secondary-background: transparent;
	--cta-button-secondary-background-hover: var(--tv-cold-gray-100);
	--cta-button-secondary-background-active: var(--tv-cold-gray-150);
	--cta-button-border-color: var(--tv-cold-gray-150);

	--cta-button-primary-background: var(--tv-blue-500);
	--cta-button-primary-background-hover: var(--tv-blue-600);
	--cta-button-primary-background-active: var(--tv-blue-700);

	--cta-button-text-color: #000;
	--cta-svg-color: #000;

	--hero-chart-padding-left: 46px;
	--hero-gap: 48px;
	--chart-right-edge-spacing: var(--container-padding);

	--hero-container-bottom-padding-adjustment: 0px;
	--right-code-block-position: -20px;
	--chart-glass-border-width: 8px;
	--hero-container-bottom-padding: 28px;
	--hero-chart-height: 604px;
	--hero-chart-aspect-ratio: initial;
	--hero-container-margin-bottom: 80px;
	--main-code-block-left-adjustment: 0px;
	--chart-border-radius: 16px;
	--import-code-block-top-position: 177px;
}

:root[data-theme='dark'] {
	--card-background: var(--tv-cold-gray-900);
	--card-title-color: #fff;
	--card-content-color: var(--tv-cold-gray-300);

	--card-background-hover: var(--tv-cold-gray-850);

	--card-background-active: var(--tv-blue-a-800);
	--card-title-color-active: var(--tv-blue-100);
	--card-content-color-active: var(--tv-blue-200);

	--card-focus-ring-color: var(--tv-blue-500);

	--hero-title-color: #fff;
	--hero-content-color: var(--tv-cold-gray-300);

	--cta-button-secondary-background-hover: var(--tv-cold-gray-800);
	--cta-button-secondary-background-active: var(--tv-cold-gray-750);
	--cta-button-border-color: var(--tv-cold-gray-700);
	--cta-button-text-color: var(--tv-cold-gray-200);
	--cta-svg-color: #fff; /* To match the design which has different icon color to the text when in dark mode */
}

/* Desktop-Medium */
@media (max-width: 1919px) {
	:root {
		--container-padding: 40px;
		--chart-right-edge-spacing: 0px;
	}
}

/* Laptop */
@media (max-width: 1439px) {
	:root {
		--card-grid-columns: 2;
		--container-padding: 32px;
		--vertical-container-padding: 100px;
		--hero-margin-bottom: 64px;
	}
}

/* Tablet-Landscape */
@media (max-width: 1279px) {
	:root {
		--vertical-container-padding: 80px;
		--hero-main-width: 611px;
		--hero-title-font-size: 48px;
		--hero-title-line-height: 52px;
		--hero-content-font-size: 20px;
		--hero-content-line-height: 30px;
		--hero-flex-direction: column;
		--hero-gap: 80px;
		--hero-chart-padding-left: 42px;
		--chart-right-edge-spacing: var(--container-padding);
		--right-code-block-position: -12px;
		--hero-chart-height: initial;
		--hero-chart-aspect-ratio: 2 / 1;
		--hero-margin-bottom: 48px;
		--main-code-block-left-adjustment: 6px;
		--import-code-block-top-position: 150px;
	}
}

/* Tablet-Vertical */
@media (max-width: 1023px) {
	:root {
		--container-padding: 20px;
		--hero-chart-padding-left: 32px;
		--hero-container-bottom-padding-adjustment: -6px;
		--right-code-block-position: -20px;
		--container-offset-right-chart: 38px;
		--chart-border-radius: 12px;
		--import-code-block-top-position: 115px;
	}
}

/* Phone-Landscape */
@media (max-width: 767px) {
	:root {
		--card-grid-columns: 1;
		--card-gap: 8px;
		--card-inner-inline-padding: 20px;
		--card-inner-block-padding: 24px;
		--card-title-font-size: 24px;
		--card-title-line-height: 28px;
		--card-content-font-size: 18px;
		--card-content-line-height: 28px;
		--vertical-container-padding: 56px;
		--hero-main-width: 100%;
		--hero-title-font-size: 32px;
		--hero-title-line-height: 36px;
		--hero-content-font-size: 18px;
		--hero-content-line-height: 28px;
		--hero-chart-padding-left: 24px;
		--hero-container-bottom-padding-adjustment: -14px;
		/* --right-code-block-position: -8px; */
		--right-code-block-position: -18px;
		--container-offset-right-chart: 34px;
		--hero-chart-aspect-ratio: 1.75;
		--main-code-block-left-adjustment: 2px;
		--chart-border-radius: 8px;
		--import-code-block-top-position: 85px;
	}
}

/* Phone-Vertical */
@media (max-width: 567px) {
	:root {
		--hero-content-gap-bottom: 32px;
		--hero-gap: 48px;
		--right-code-block-position: -8px;
		--hero-container-bottom-padding: 0px;
		--hero-container-bottom-padding-adjustment: -16px;
		--hero-margin-bottom: 38px;
		--chart-border-radius: 24px;
		--container-offset-right-chart: 16px;
	}
}

.RootContainer {
	font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
		Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	display: flex;
	flex-direction: column;
	margin: 0px;
	max-width: 1920px;

	/* Don't use this because we want to have the chart reach the viewport edge for certain breakpoints */
	/* padding-inline: var(--container-padding); */

	/* padding-block: var(--vertical-container-padding); */
	/* ! Fix for older versions of Safari which had a bug with css variables being used for -inline / -block */
	padding: var(--vertical-container-padding) 0px;
}

@media (min-width: 1920px) {
	.RootContainer {
		margin-left: auto;
		margin-right: auto;
	}
}
