# Crypto Charts with CCXT and Lightweight Charts (Fixed Version)

This is a fixed version of the crypto charts scripts that doesn't rely on pandas_ta to avoid the numpy.NaN import error.

## The Issue

The original scripts used pandas_ta for calculating technical indicators, but there's a known issue with certain versions of pandas_ta and numpy where pandas_ta tries to import `NaN` from numpy with uppercase 'N', but in numpy it's defined as `nan` (lowercase).

The error looks like this:
```
ImportError: cannot import name 'NaN' from 'numpy' (C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\numpy\__init__.py)
```

## The Solution

This fixed version implements the technical indicators directly using pandas and numpy, without relying on pandas_ta. The following indicators are implemented:

- Simple Moving Average (SMA)
- Exponential Moving Average (EMA)
- Bollinger Bands
- Relative Strength Index (RSI)
- Moving Average Convergence Divergence (MACD)

## Files

- `crypto_charts_fixed.py`: The main script for creating charts with indicators and grid layouts
- `crypto_charts_realtime_fixed.py`: A script for creating real-time updating charts

## Installation

1. Install the required packages:

```bash
pip install ccxt pandas numpy lightweight-charts asyncio
```

## Usage

### Single Chart with Indicators

```python
from crypto_charts_fixed import CryptoCharts

# Create a chart for BTC/USDT on Binance with 1-hour timeframe
crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='1h')

# Fetch OHLCV data
df = crypto_chart.fetch_ohlcv(limit=500)

# Calculate indicators
indicators = crypto_chart.calculate_indicators(df)

# Create and display the chart
crypto_chart.create_chart(df, indicators)
```

### Grid of Multiple Charts

```python
from crypto_charts_fixed import CryptoChartsGrid

# Create a grid of charts for multiple symbols
symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT']
grid = CryptoChartsGrid(symbols, exchange_id='binance', timeframe='1h')

# Create and display the grid
grid.create_grid()
```

### Real-time Chart

```python
from crypto_charts_realtime_fixed import RealtimeCryptoChart

# Create a real-time chart for BTC/USDT on Binance with 1-minute timeframe
realtime_chart = RealtimeCryptoChart(exchange_id='binance', symbol='BTC/USDT', timeframe='1m')

# Start the chart
realtime_chart.start_chart()
```

## Customization

### Changing the Exchange

You can use any exchange supported by CCXT:

```python
crypto_chart = CryptoCharts(exchange_id='kucoin', symbol='BTC/USDT', timeframe='1h')
```

### Changing the Timeframe

Supported timeframes depend on the exchange, but common ones include:
- '1m' (1 minute)
- '5m' (5 minutes)
- '15m' (15 minutes)
- '1h' (1 hour)
- '4h' (4 hours)
- '1d' (1 day)

```python
crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='15m')
```

## Notes

- The script uses futures markets by default. To use spot markets, change the `defaultType` option in the `_initialize_exchange` method.
- Some exchanges may require API keys for data fetching. In that case, you'll need to provide them when initializing the exchange.
- The lightweight-charts library is a wrapper around TradingView's Lightweight Charts library, which provides a high-performance financial charting library.

## Alternative Solutions

If you still want to use pandas_ta, you can try one of these solutions:

1. **Downgrade numpy**: Some users have reported that downgrading numpy to version 1.20.3 resolves the issue:
   ```bash
   pip install numpy==1.20.3
   ```

2. **Fix the pandas_ta library**: You can edit the problematic file in the pandas_ta library:
   ```python
   # Find the file: site-packages/pandas_ta/momentum/squeeze_pro.py
   # Change this line:
   from numpy import NaN as npNaN
   # To:
   from numpy import nan as npNaN
   ```

3. **Use a different technical analysis library**: There are other libraries like `ta` or `ta-lib` that provide similar functionality.

## Running the Examples

To run the examples directly:

```bash
python crypto_charts_fixed.py
python crypto_charts_realtime_fixed.py
```
