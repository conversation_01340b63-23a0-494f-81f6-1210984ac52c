# Crypto Charts with CCXT and Lightweight Charts

This script provides a way to display cryptocurrency charts with technical indicators using CCXT for data fetching and the lightweight-charts library for visualization.

## Features

- Fetch cryptocurrency data from various exchanges using CCXT
- Display candlestick charts with volume
- Add technical indicators:
  - Simple Moving Average (SMA)
  - Exponential Moving Average (EMA)
  - Bollinger Bands
  - Relative Strength Index (RSI)
  - Moving Average Convergence Divergence (MACD)
- Create grid layouts with multiple charts
- Support for different timeframes

## Installation

1. Install the required packages:

```bash
pip install -r requirements.txt
```

2. Make sure you have the following packages installed:
   - lightweight-charts
   - ccxt
   - pandas
   - pandas_ta
   - asyncio

## Usage

### Single Chart with Indicators

```python
from crypto_charts import CryptoCharts

# Create a chart for BTC/USDT on Binance with 1-hour timeframe
crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='1h')

# Fetch OHLCV data
df = crypto_chart.fetch_ohlcv(limit=500)

# Calculate indicators
indicators = crypto_chart.calculate_indicators(df)

# Create and display the chart
crypto_chart.create_chart(df, indicators)
```

### Grid of Multiple Charts

```python
from crypto_charts import CryptoChartsGrid

# Create a grid of charts for multiple symbols
symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'XRP/USDT']
grid = CryptoChartsGrid(symbols, exchange_id='binance', timeframe='1h')

# Create and display the grid
grid.create_grid()
```

## Customization

### Changing the Exchange

You can use any exchange supported by CCXT:

```python
crypto_chart = CryptoCharts(exchange_id='kucoin', symbol='BTC/USDT', timeframe='1h')
```

### Changing the Timeframe

Supported timeframes depend on the exchange, but common ones include:
- '1m' (1 minute)
- '5m' (5 minutes)
- '15m' (15 minutes)
- '1h' (1 hour)
- '4h' (4 hours)
- '1d' (1 day)

```python
crypto_chart = CryptoCharts(exchange_id='binance', symbol='BTC/USDT', timeframe='15m')
```

### Adding Custom Indicators

You can extend the `calculate_indicators` method to add your own indicators:

```python
def calculate_custom_indicators(self, df):
    indicators = self.calculate_indicators(df)
    
    # Add your custom indicator
    custom_indicator = df.ta.some_indicator().to_frame()
    custom_indicator = custom_indicator.reset_index()
    custom_indicator = custom_indicator.rename(columns={"index": "time", "INDICATOR": "value"})
    custom_indicator = custom_indicator.dropna()
    indicators['custom'] = custom_indicator
    
    return indicators
```

## Example

Running the script directly will demonstrate both a single chart with indicators and a grid of multiple charts:

```bash
python crypto_charts.py
```

## Notes

- The script uses futures markets by default. To use spot markets, change the `defaultType` option in the `_initialize_exchange` method.
- Some exchanges may require API keys for data fetching. In that case, you'll need to provide them when initializing the exchange.
- The lightweight-charts library is a wrapper around TradingView's Lightweight Charts library, which provides a high-performance financial charting library.
