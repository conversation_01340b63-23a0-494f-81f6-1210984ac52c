{"docsSidebar": ["intro", "series-types", "price-scale", "time-scale", "time-zones", {"Migrations": [{"type": "autogenerated", "dirName": "migrations"}]}, {"type": "doc", "id": "ios", "label": "iOS"}, {"type": "doc", "id": "android", "label": "Android"}, "release-notes"], "typedocSidebar": [{"type": "doc", "id": "api/index", "label": "Exports"}, {"type": "category", "label": "Enumerations", "items": [{"type": "doc", "id": "api/enumerations/ColorType", "label": "ColorType"}, {"type": "doc", "id": "api/enumerations/CrosshairMode", "label": "CrosshairMode"}, {"type": "doc", "id": "api/enumerations/LastPriceAnimationMode", "label": "LastPriceAnimationMode"}, {"type": "doc", "id": "api/enumerations/LineStyle", "label": "LineStyle"}, {"type": "doc", "id": "api/enumerations/LineType", "label": "LineType"}, {"type": "doc", "id": "api/enumerations/PriceLineSource", "label": "PriceLineSource"}, {"type": "doc", "id": "api/enumerations/PriceScaleMode", "label": "PriceScaleMode"}, {"type": "doc", "id": "api/enumerations/TickMarkType", "label": "TickMarkType"}, {"type": "doc", "id": "api/enumerations/TrackingModeExitMode", "label": "TrackingModeExitMode"}]}, {"type": "category", "label": "Interfaces", "items": [{"type": "doc", "id": "api/interfaces/AreaStyleOptions", "label": "AreaStyleOptions"}, {"type": "doc", "id": "api/interfaces/AutoScaleMargins", "label": "AutoScaleMargins"}, {"type": "doc", "id": "api/interfaces/AutoscaleInfo", "label": "AutoscaleInfo"}, {"type": "doc", "id": "api/interfaces/AxisPressedMouseMoveOptions", "label": "AxisPressedMouseMoveOptions"}, {"type": "doc", "id": "api/interfaces/BarData", "label": "BarData"}, {"type": "doc", "id": "api/interfaces/BarPrices", "label": "BarPrices"}, {"type": "doc", "id": "api/interfaces/BarStyleOptions", "label": "BarStyleOptions"}, {"type": "doc", "id": "api/interfaces/BarsInfo", "label": "BarsInfo"}, {"type": "doc", "id": "api/interfaces/BaseValuePrice", "label": "BaseValuePrice"}, {"type": "doc", "id": "api/interfaces/BaselineStyleOptions", "label": "BaselineStyleOptions"}, {"type": "doc", "id": "api/interfaces/BusinessDay", "label": "BusinessDay"}, {"type": "doc", "id": "api/interfaces/CandlestickData", "label": "CandlestickData"}, {"type": "doc", "id": "api/interfaces/CandlestickStyleOptions", "label": "CandlestickStyleOptions"}, {"type": "doc", "id": "api/interfaces/ChartOptions", "label": "ChartOptions"}, {"type": "doc", "id": "api/interfaces/CrosshairLineOptions", "label": "CrosshairLineOptions"}, {"type": "doc", "id": "api/interfaces/CrosshairOptions", "label": "CrosshairOptions"}, {"type": "doc", "id": "api/interfaces/GridLineOptions", "label": "GridLineOptions"}, {"type": "doc", "id": "api/interfaces/GridOptions", "label": "GridOptions"}, {"type": "doc", "id": "api/interfaces/HandleScaleOptions", "label": "HandleScaleOptions"}, {"type": "doc", "id": "api/interfaces/HandleScrollOptions", "label": "HandleScrollOptions"}, {"type": "doc", "id": "api/interfaces/HistogramData", "label": "HistogramData"}, {"type": "doc", "id": "api/interfaces/HistogramStyleOptions", "label": "HistogramStyleOptions"}, {"type": "doc", "id": "api/interfaces/IChartApi", "label": "IChartApi"}, {"type": "doc", "id": "api/interfaces/IPriceFormatter", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "doc", "id": "api/interfaces/IPriceLine", "label": "IPriceLine"}, {"type": "doc", "id": "api/interfaces/IPriceScaleApi", "label": "IPriceScaleApi"}, {"type": "doc", "id": "api/interfaces/ISeriesApi", "label": "ISeriesApi"}, {"type": "doc", "id": "api/interfaces/ITimeScaleApi", "label": "ITimeScaleApi"}, {"type": "doc", "id": "api/interfaces/KineticScrollOptions", "label": "KineticScrollOptions"}, {"type": "doc", "id": "api/interfaces/LayoutOptions", "label": "LayoutOptions"}, {"type": "doc", "id": "api/interfaces/LineData", "label": "LineData"}, {"type": "doc", "id": "api/interfaces/LineStyleOptions", "label": "LineStyleOptions"}, {"type": "doc", "id": "api/interfaces/LocalizationOptions", "label": "LocalizationOptions"}, {"type": "doc", "id": "api/interfaces/MouseEventParams", "label": "MouseEventParams"}, {"type": "doc", "id": "api/interfaces/OhlcData", "label": "OhlcData"}, {"type": "doc", "id": "api/interfaces/Point", "label": "Point"}, {"type": "doc", "id": "api/interfaces/PriceFormatBuiltIn", "label": "PriceFormatBuiltIn"}, {"type": "doc", "id": "api/interfaces/PriceFormatCustom", "label": "PriceFormatCustom"}, {"type": "doc", "id": "api/interfaces/PriceLineOptions", "label": "PriceLineOptions"}, {"type": "doc", "id": "api/interfaces/PriceRange", "label": "PriceRange"}, {"type": "doc", "id": "api/interfaces/PriceScaleMargins", "label": "PriceScaleMargins"}, {"type": "doc", "id": "api/interfaces/PriceScaleOptions", "label": "PriceScaleOptions"}, {"type": "doc", "id": "api/interfaces/Range", "label": "Range"}, {"type": "doc", "id": "api/interfaces/SeriesDataItemTypeMap", "label": "SeriesDataItemTypeMap"}, {"type": "doc", "id": "api/interfaces/SeriesMarker", "label": "SeriesMarker"}, {"type": "doc", "id": "api/interfaces/SeriesOptionsCommon", "label": "SeriesOptionsCommon"}, {"type": "doc", "id": "api/interfaces/SeriesOptionsMap", "label": "SeriesOptionsMap"}, {"type": "doc", "id": "api/interfaces/SeriesPartialOptionsMap", "label": "SeriesPartialOptionsMap"}, {"type": "doc", "id": "api/interfaces/SingleValueData", "label": "SingleValueData"}, {"type": "doc", "id": "api/interfaces/SolidColor", "label": "SolidColor"}, {"type": "doc", "id": "api/interfaces/TimeScaleOptions", "label": "TimeScaleOptions"}, {"type": "doc", "id": "api/interfaces/TrackingModeOptions", "label": "TrackingModeOptions"}, {"type": "doc", "id": "api/interfaces/VerticalGradientColor", "label": "VerticalGradientColor"}, {"type": "doc", "id": "api/interfaces/WatermarkOptions", "label": "WatermarkOptions"}, {"type": "doc", "id": "api/interfaces/WhitespaceData", "label": "WhitespaceData"}]}, {"type": "category", "label": "Type Aliases", "items": [{"type": "doc", "id": "api/type-aliases/AreaSeriesOptions", "label": "AreaSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/AreaSeriesPartialOptions", "label": "AreaSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/AutoscaleInfoProvider", "label": "AutoscaleInfoProvider"}, {"type": "doc", "id": "api/type-aliases/Background", "label": "Background"}, {"type": "doc", "id": "api/type-aliases/BarPrice", "label": "BarPrice"}, {"type": "doc", "id": "api/type-aliases/BarSeriesOptions", "label": "BarSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/BarSeriesPartialOptions", "label": "BarSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/BaseValueType", "label": "BaseValueType"}, {"type": "doc", "id": "api/type-aliases/BaselineSeriesOptions", "label": "BaselineSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/BaselineSeriesPartialOptions", "label": "BaselineSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/CandlestickSeriesOptions", "label": "CandlestickSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/CandlestickSeriesPartialOptions", "label": "CandlestickSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/Coordinate", "label": "Coordinate"}, {"type": "doc", "id": "api/type-aliases/DeepPartial", "label": "DeepPartial"}, {"type": "doc", "id": "api/type-aliases/HistogramSeriesOptions", "label": "HistogramSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/HistogramSeriesPartialOptions", "label": "HistogramSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/Horz<PERSON>lign", "label": "HorzAlign"}, {"type": "doc", "id": "api/type-aliases/LineSeriesOptions", "label": "LineSeriesOptions"}, {"type": "doc", "id": "api/type-aliases/LineSeriesPartialOptions", "label": "LineSeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/LineWidth", "label": "LineWidth"}, {"type": "doc", "id": "api/type-aliases/Logical", "label": "Logical"}, {"type": "doc", "id": "api/type-aliases/LogicalRange", "label": "LogicalRange"}, {"type": "doc", "id": "api/type-aliases/LogicalRangeChangeEventHandler", "label": "LogicalRangeChangeEventHandler"}, {"type": "doc", "id": "api/type-aliases/MouseEventHandler", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "doc", "id": "api/type-aliases/Nominal", "label": "Nominal"}, {"type": "doc", "id": "api/type-aliases/OverlayPriceScaleOptions", "label": "OverlayPriceScaleOptions"}, {"type": "doc", "id": "api/type-aliases/PriceFormat", "label": "PriceFormat"}, {"type": "doc", "id": "api/type-aliases/PriceFormatterFn", "label": "PriceFormatterFn"}, {"type": "doc", "id": "api/type-aliases/SeriesMarkerPosition", "label": "SeriesMarkerPosition"}, {"type": "doc", "id": "api/type-aliases/SeriesMarkerShape", "label": "SeriesMarkerShape"}, {"type": "doc", "id": "api/type-aliases/SeriesOptions", "label": "SeriesOptions"}, {"type": "doc", "id": "api/type-aliases/SeriesPartialOptions", "label": "SeriesPartialOptions"}, {"type": "doc", "id": "api/type-aliases/SeriesType", "label": "SeriesType"}, {"type": "doc", "id": "api/type-aliases/SizeChangeEventHandler", "label": "SizeChangeEventHandler"}, {"type": "doc", "id": "api/type-aliases/TickMarkFormatter", "label": "TickMarkFormatter"}, {"type": "doc", "id": "api/type-aliases/Time", "label": "Time"}, {"type": "doc", "id": "api/type-aliases/TimeFormatterFn", "label": "TimeFormatterFn"}, {"type": "doc", "id": "api/type-aliases/TimeRange", "label": "TimeRange"}, {"type": "doc", "id": "api/type-aliases/TimeRangeChangeEventHandler", "label": "TimeRangeChangeEventHandler"}, {"type": "doc", "id": "api/type-aliases/UTCTimestamp", "label": "UTCTimestamp"}, {"type": "doc", "id": "api/type-aliases/VertAlign", "label": "VertAlign"}, {"type": "doc", "id": "api/type-aliases/VisiblePriceScaleOptions", "label": "VisiblePriceScaleOptions"}]}, {"type": "category", "label": "Functions", "items": [{"type": "doc", "id": "api/functions/createChart", "label": "createChart"}, {"type": "doc", "id": "api/functions/isBusinessDay", "label": "isBusinessDay"}, {"type": "doc", "id": "api/functions/isUTCTimestamp", "label": "isUTCTimestamp"}, {"type": "doc", "id": "api/functions/version", "label": "version"}]}]}