---
sidebar_position: 4
title: Conclusion
pagination_title: Conclusion
sidebar_label: Conclusion
description: Conclusion to the accessibility tutorial
keywords:
  - a11y
  - accessibility
  - screenreaders
  - keyboard
  - assistive
pagination_prev: a11y/readability
pagination_next: null
---

# Conclusion

This tutorial demonstrated a wide array of accessibility improvements that can
be introduced to line charts, making them usable for a broader spectrum of
users. The considerations and methods presented illustrate the importance of
creating inclusive web applications.

Among the discussed topics were:

1. **Keyboard navigation:** The implementation of
keyboard-only navigation on our chart, allowing for interaction exclusively
through keystrokes.
2. **ARIA roles and descriptive content:** The additions of ARIA
labels, live regions, and the hiding of non-consequential elements to enhance
the usefulness of assistive technologies and to communicate context better to
all users.
3. **High contrast and scalable font size:** The detection of the user's
preference for higher contrast and the inclusion of customizable text sizes for
better readability.

The example provided has even more improvements not directly discussed during the
the tutorial. One such enhancement is an informative tooltip that lists available
commands, adding another helpful layer of user instruction.

:::tip

The complete example code provides further insights, and reviewing it is
recommended for a comprehensive understanding of the accessibility improvements
discussed in this tutorial.

:::

This tutorial aimed to foster a better understanding of these practices,
promoting an inclusive web where applications are made with everyone in mind.
Thank you for following along and continue putting accessibility at the
forefront of your web development endeavors.

## Complete example

You can view the example in a new window, or download the source file below:

<ul>
	<li>
		<a
			href={require('!!file-loader!./assets/a11y-chart.html').default}
			target="\_blank"
		>
			View in a new window
		</a>
	</li>
	<li>
		<a
			href={require('!!file-loader!./assets/a11y-chart.html').default}
			download="a11y-chart.html"
			target="\_blank"
		>
			Download the file
		</a>
	</li>
</ul>
