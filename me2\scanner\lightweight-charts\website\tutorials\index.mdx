---
sidebar_position: 0
pagination_next: null
---

import <PERSON><PERSON><PERSON>List from "@site/src/components/CardLinkList";
import Shapes from "@site/src/img/shapes.svg";
import ReactLogo from "@site/src/img/react.svg";
import Vuejs<PERSON>ogo from "@site/src/img/vuejs.svg";
import WebComponentsLogo from "@site/src/img/webcomponents.svg";
import A11y from "@site/src/img/a11y.svg";

# Tutorials

import VersionWarningAdmonition from "@site/src/components/VersionWarningAdmonition";

{/*
  Show warning when not on the latest published version
  Tutorials section isn't versioned yet, hence the need for the warning message
*/}

<VersionWarningAdmonition
	notCurrent="These tutorials are for the latest published version of Lightweight Charts."
	type="caution"
	displayVersionMessage
/>

## Guides

<CardLinkList
	items={[
		{
			href: "/tutorials/customization/intro",
			title: "Customization",
			image: <Shapes />,
			description: "Customizing appearance & features",
		},
		{
			href: "/tutorials/a11y/intro",
			title: "Accessibility",
			image: <A11y />,
			description: "How to improve A11y support",
		},
	]}
/>

## Framework integrations

This section contains some tutorials how to use Lightweight Charts™ with some popular frameworks.

<CardLinkList
	items={[
		{
			href: "/tutorials/react/simple",
			title: "React",
			image: <ReactLogo />,
			description: "Integration guide for React",
		},
		{
			href: "/tutorials/vuejs/wrapper",
			title: "Vue.js",
			image: <VuejsLogo />,
			description: "Integration guide for Vue.js",
		},
		{
			href: "/tutorials/webcomponents/custom-element",
			title: "Web Components",
			image: <WebComponentsLogo />,
			description: "Web components custom element",
		},
	]}
/>

:::info

If you think that a tutorial is missing feel free to ask [in the discussions](https://github.com/tradingview/lightweight-charts/discussions)
or submit your own.

:::

## How To

A collection of code examples showcasing the various capabilities of the library, and how to implement common additional features.

import { useDocsSidebar } from '@docusaurus/plugin-content-docs/client';
export const HowToList = () => {
	const examplesCategory = useDocsSidebar().items.find(
		item => item.type === "category" && item.label === "How To"
	);
	const examples = examplesCategory.items.filter(doc => doc.type === "link");
	return (
		<ul>
			{examples.map(docLink => (
				<li key={docLink.docId}>
					<a href={docLink.href}>{docLink.label}</a>
				</li>
			))}
		</ul>
	);
};

<HowToList />

## Examples / Demos

A collection of demos showcasing the various capabilities of the library.

export const ExamplesList = () => {
	const examplesCategory = useDocsSidebar().items.find(
		item => item.type === "category" && item.label === "Examples / Demos"
	);
	const examples = examplesCategory.items.filter(doc => doc.type === "link");
	return (
		<ul>
			{examples.map(docLink => (
				<li key={docLink.docId}>
					<a href={docLink.href}>{docLink.label}</a>
				</li>
			))}
		</ul>
	);
};

<ExamplesList />
