#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Real-time Crypto Charts with CCXT and Lightweight Charts
-------------------------------------------------------
A script to display real-time cryptocurrency charts with technical indicators using CCXT and lightweight-charts.
"""

import asyncio
import pandas as pd
import pandas_ta as ta
import ccxt
import time
from datetime import datetime
from lightweight_charts import Chart

class RealtimeCryptoChart:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1m'):
        """
        Initialize the RealtimeCryptoChart class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1m')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.exchange = self._initialize_exchange()
        self.chart = None
        self.running = False
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Use 'spot' for spot markets
                }
            })
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=100):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 100)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df.rename(columns={'time': 'time'})
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def calculate_indicators(self, df):
        """
        Calculate technical indicators.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            
        Returns:
            dict: Dictionary with indicator DataFrames
        """
        indicators = {}
        
        # Calculate SMA
        sma20 = df.ta.sma(length=20).to_frame()
        sma20 = sma20.reset_index()
        sma20 = sma20.rename(columns={"index": "time", "SMA_20": "value"})
        sma20 = sma20.dropna()
        indicators['sma20'] = sma20
        
        # Calculate EMA
        ema50 = df.ta.ema(length=50).to_frame()
        ema50 = ema50.reset_index()
        ema50 = ema50.rename(columns={"index": "time", "EMA_50": "value"})
        ema50 = ema50.dropna()
        indicators['ema50'] = ema50
        
        # Calculate RSI
        rsi = df.ta.rsi(length=14).to_frame()
        rsi = rsi.reset_index()
        rsi = rsi.rename(columns={"index": "time", "RSI_14": "value"})
        rsi = rsi.dropna()
        indicators['rsi'] = rsi
        
        return indicators
    
    def start_chart(self):
        """Start the real-time chart."""
        # Fetch initial data
        df = self.fetch_ohlcv(limit=100)
        if df is None:
            print("Failed to fetch initial data.")
            return
        
        # Calculate indicators
        indicators = self.calculate_indicators(df)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Create chart
        self.chart = Chart(volume_enabled=True)
        
        # Set chart data
        self.chart.set(df_formatted)
        
        # Add SMA line
        if 'sma20' in indicators:
            sma_line = self.chart.create_line(color='blue', price_line=True, price_label=True)
            sma_line.set(indicators['sma20'])
            self.sma_line = sma_line
        
        # Add EMA line
        if 'ema50' in indicators:
            ema_line = self.chart.create_line(color='red', price_line=True, price_label=True)
            ema_line.set(indicators['ema50'])
            self.ema_line = ema_line
        
        # Add RSI in a subchart
        if 'rsi' in indicators:
            rsi_chart = self.chart.create_subchart(height=0.2, position='bottom')
            rsi_line = rsi_chart.create_line(color='purple')
            rsi_line.set(indicators['rsi'])
            
            # Add overbought/oversold lines
            rsi_chart.create_horizontal_line(70, color='red')
            rsi_chart.create_horizontal_line(30, color='green')
            rsi_chart.watermark('RSI')
            
            self.rsi_chart = rsi_chart
            self.rsi_line = rsi_line
        
        # Set chart title
        self.chart.watermark(f"{self.symbol} - {self.timeframe} (Real-time)")
        
        # Start update loop
        self.running = True
        self.update_loop()
        
        # Show chart
        self.chart.show(block=True)
    
    def update_loop(self):
        """Update the chart in a loop."""
        if not self.running:
            return
        
        # Schedule the next update
        self.chart.on_timer(self.update_chart, interval=10000)  # Update every 10 seconds
    
    def update_chart(self):
        """Update the chart with new data."""
        if not self.running:
            return
        
        try:
            # Fetch latest data
            latest_data = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=1)
            
            if not latest_data:
                print("No new data received.")
                return
            
            # Convert to DataFrame
            latest_df = pd.DataFrame(latest_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            latest_df['time'] = pd.to_datetime(latest_df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            latest_df = latest_df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            # Format time for chart
            latest_time = latest_df['time'].iloc[0].strftime('%Y-%m-%d %H:%M:%S')
            
            # Create update series
            update_series = {
                'time': latest_time,
                'open': float(latest_df['open'].iloc[0]),
                'high': float(latest_df['high'].iloc[0]),
                'low': float(latest_df['low'].iloc[0]),
                'close': float(latest_df['close'].iloc[0]),
                'volume': float(latest_df['volume'].iloc[0])
            }
            
            # Update chart
            self.chart.update(update_series)
            
            print(f"Updated chart with data for {latest_time}")
            
        except Exception as e:
            print(f"Error updating chart: {e}")
    
    def stop(self):
        """Stop the real-time updates."""
        self.running = False

def main():
    """Main function to demonstrate the RealtimeCryptoChart class."""
    # Create a real-time chart for BTC/USDT on Binance with 1-minute timeframe
    realtime_chart = RealtimeCryptoChart(exchange_id='binance', symbol='BTC/USDT', timeframe='1m')
    
    # Start the chart
    try:
        realtime_chart.start_chart()
    except KeyboardInterrupt:
        print("Stopping real-time updates...")
        realtime_chart.stop()

if __name__ == '__main__':
    main()
