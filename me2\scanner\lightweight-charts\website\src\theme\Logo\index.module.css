.Logo {
	filter: invert(6%) sepia(10%) saturate(2860%) hue-rotate(186deg)
		brightness(94%) contrast(93%);
	background-repeat: no-repeat;
	background-image: var(--navbar-logo-url-desktop-laptop-tablet);
	width: 189px;
	height: 32px;

	/* Needed for safari to allow the color change to work properly when toggling the theme */
	transform: translate3d(0, 0, 0);
}

.Logo:focus-visible {
	outline-offset: 12px 16px;
	outline-style: solid;
	outline-color: var(--color-tv-blue-500);
	outline-width: 2px;
	border-radius: 6px; /* Design specified 12px but the outline is limited by the available viewport space, so 12px looks incorrect */
}

html[data-theme='dark'] .Logo {
	/* Makes the black logo -> white */
	filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(125deg)
		brightness(103%) contrast(103%);
}

/*
Small logo on tiny screen
*/
@media (max-width: 567px) {
	.<PERSON>go {
		background-image: var(--navbar-logo-url-mobile);
		width: 36px;
		height: 32px;
	}
}
