#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Comprehensive Crypto Charts Example
----------------------------------
A comprehensive example demonstrating various features of the crypto charts scripts.
"""

import argparse
import pandas as pd
import pandas_ta as ta
import ccxt
from lightweight_charts import Chart

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Crypto Charts Example')
    parser.add_argument('--exchange', type=str, default='binance', help='Exchange ID (default: binance)')
    parser.add_argument('--symbol', type=str, default='BTC/USDT', help='Trading pair symbol (default: BTC/USDT)')
    parser.add_argument('--timeframe', type=str, default='1h', help='Chart timeframe (default: 1h)')
    parser.add_argument('--mode', type=str, default='single', choices=['single', 'grid', 'multi', 'sr'], 
                        help='Chart mode (default: single)')
    parser.add_argument('--limit', type=int, default=500, help='Number of candles to fetch (default: 500)')
    return parser.parse_args()

def initialize_exchange(exchange_id):
    """Initialize the exchange."""
    try:
        exchange_class = getattr(ccxt, exchange_id)
        exchange = exchange_class({
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',  # Use 'spot' for spot markets
            }
        })
        return exchange
    except Exception as e:
        print(f"Error initializing exchange: {e}")
        return None

def fetch_ohlcv(exchange, symbol, timeframe, limit=500):
    """
    Fetch OHLCV data from the exchange.
    
    Args:
        exchange: CCXT exchange instance
        symbol (str): The trading pair symbol
        timeframe (str): The chart timeframe
        limit (int): Number of candles to fetch (default: 500)
        
    Returns:
        pd.DataFrame: DataFrame with OHLCV data
    """
    try:
        # Fetch OHLCV data
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        
        # Convert to DataFrame
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        # Convert timestamp to datetime
        df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Format for lightweight-charts
        df = df.rename(columns={'time': 'time'})
        df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
        
        return df
    except Exception as e:
        print(f"Error fetching OHLCV data: {e}")
        return None

def calculate_indicators(df):
    """
    Calculate technical indicators.
    
    Args:
        df (pd.DataFrame): DataFrame with OHLCV data
        
    Returns:
        dict: Dictionary with indicator DataFrames
    """
    indicators = {}
    
    # Calculate SMA
    sma20 = df.ta.sma(length=20).to_frame()
    sma20 = sma20.reset_index()
    sma20 = sma20.rename(columns={"index": "time", "SMA_20": "value"})
    sma20 = sma20.dropna()
    indicators['sma20'] = sma20
    
    # Calculate EMA
    ema50 = df.ta.ema(length=50).to_frame()
    ema50 = ema50.reset_index()
    ema50 = ema50.rename(columns={"index": "time", "EMA_50": "value"})
    ema50 = ema50.dropna()
    indicators['ema50'] = ema50
    
    # Calculate Bollinger Bands
    bbands = df.ta.bbands(length=20, std=2).reset_index()
    bbands = bbands.rename(columns={
        "index": "time", 
        "BBL_20_2.0": "lower",
        "BBM_20_2.0": "middle",
        "BBU_20_2.0": "upper"
    })
    bbands = bbands.dropna()
    indicators['bbands'] = bbands
    
    # Calculate RSI
    rsi = df.ta.rsi(length=14).to_frame()
    rsi = rsi.reset_index()
    rsi = rsi.rename(columns={"index": "time", "RSI_14": "value"})
    rsi = rsi.dropna()
    indicators['rsi'] = rsi
    
    # Calculate MACD
    macd = df.ta.macd(fast=12, slow=26, signal=9)
    macd = macd.reset_index()
    macd = macd.rename(columns={
        "index": "time",
        "MACD_12_26_9": "macd",
        "MACDs_12_26_9": "signal",
        "MACDh_12_26_9": "histogram"
    })
    macd = macd.dropna()
    indicators['macd'] = macd
    
    # Calculate ATR
    atr = df.ta.atr(length=14).to_frame()
    atr = atr.reset_index()
    atr = atr.rename(columns={"index": "time", "ATR_14": "value"})
    atr = atr.dropna()
    indicators['atr'] = atr
    
    return indicators

def find_support_resistance(df, window=10, threshold=0.02):
    """
    Find support and resistance levels.
    
    Args:
        df (pd.DataFrame): DataFrame with OHLCV data
        window (int): Window size for finding local extrema (default: 10)
        threshold (float): Threshold for clustering levels (default: 0.02)
        
    Returns:
        tuple: (support_levels, resistance_levels)
    """
    # Find local minima (support)
    df['min'] = df['low'].rolling(window=window, center=True).min()
    df['is_support'] = (df['low'] == df['min']) & (df['low'].shift(window//2) > df['low']) & (df['low'].shift(-window//2) > df['low'])
    
    # Find local maxima (resistance)
    df['max'] = df['high'].rolling(window=window, center=True).max()
    df['is_resistance'] = (df['high'] == df['max']) & (df['high'].shift(window//2) < df['high']) & (df['high'].shift(-window//2) < df['high'])
    
    # Get support and resistance levels
    support_levels = df[df['is_support']]['low'].tolist()
    resistance_levels = df[df['is_resistance']]['high'].tolist()
    
    # Cluster levels
    support_levels = cluster_levels(support_levels, threshold)
    resistance_levels = cluster_levels(resistance_levels, threshold)
    
    return support_levels, resistance_levels

def cluster_levels(levels, threshold):
    """
    Cluster similar price levels.
    
    Args:
        levels (list): List of price levels
        threshold (float): Threshold for clustering
        
    Returns:
        list: Clustered price levels
    """
    if not levels:
        return []
    
    # Sort levels
    levels = sorted(levels)
    
    # Cluster levels
    clustered_levels = []
    current_cluster = [levels[0]]
    
    for i in range(1, len(levels)):
        # If the level is close to the current cluster, add it to the cluster
        if (levels[i] - current_cluster[-1]) / current_cluster[-1] < threshold:
            current_cluster.append(levels[i])
        else:
            # Otherwise, add the average of the current cluster to the result
            clustered_levels.append(sum(current_cluster) / len(current_cluster))
            current_cluster = [levels[i]]
    
    # Add the last cluster
    if current_cluster:
        clustered_levels.append(sum(current_cluster) / len(current_cluster))
    
    return clustered_levels

def create_single_chart(exchange, symbol, timeframe, limit=500):
    """Create a single chart with indicators."""
    # Fetch OHLCV data
    df = fetch_ohlcv(exchange, symbol, timeframe, limit)
    if df is None:
        print("Failed to fetch data.")
        return
    
    # Calculate indicators
    indicators = calculate_indicators(df)
    
    # Find support and resistance levels
    support_levels, resistance_levels = find_support_resistance(df)
    
    # Format DataFrame for lightweight-charts
    df_formatted = df.copy()
    df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Create chart
    chart = Chart(volume_enabled=True)
    
    # Set chart data
    chart.set(df_formatted)
    
    # Add SMA line
    if 'sma20' in indicators:
        sma_line = chart.create_line(color='blue', price_line=True, price_label=True)
        sma_line.set(indicators['sma20'])
    
    # Add EMA line
    if 'ema50' in indicators:
        ema_line = chart.create_line(color='red', price_line=True, price_label=True)
        ema_line.set(indicators['ema50'])
    
    # Add Bollinger Bands
    if 'bbands' in indicators:
        bb_upper = chart.create_line(color='green', price_line=False)
        bb_middle = chart.create_line(color='orange', price_line=False)
        bb_lower = chart.create_line(color='green', price_line=False)
        
        bb_upper.set(indicators['bbands'][['time', 'upper']].rename(columns={'upper': 'value'}))
        bb_middle.set(indicators['bbands'][['time', 'middle']].rename(columns={'middle': 'value'}))
        bb_lower.set(indicators['bbands'][['time', 'lower']].rename(columns={'lower': 'value'}))
    
    # Add support and resistance levels
    for level in support_levels:
        chart.create_horizontal_line(level, color='green', line_style='dashed', line_width=1)
    
    for level in resistance_levels:
        chart.create_horizontal_line(level, color='red', line_style='dashed', line_width=1)
    
    # Add RSI in a subchart
    if 'rsi' in indicators:
        rsi_chart = chart.create_subchart(height=0.2, position='bottom')
        rsi_line = rsi_chart.create_line(color='purple')
        rsi_line.set(indicators['rsi'])
        
        # Add overbought/oversold lines
        rsi_chart.create_horizontal_line(70, color='red')
        rsi_chart.create_horizontal_line(30, color='green')
        rsi_chart.watermark('RSI')
    
    # Add MACD in a subchart
    if 'macd' in indicators:
        macd_chart = chart.create_subchart(height=0.2, position='bottom')
        
        # MACD line
        macd_line = macd_chart.create_line(color='blue')
        macd_line.set(indicators['macd'][['time', 'macd']].rename(columns={'macd': 'value'}))
        
        # Signal line
        signal_line = macd_chart.create_line(color='red')
        signal_line.set(indicators['macd'][['time', 'signal']].rename(columns={'signal': 'value'}))
        
        # Histogram
        histogram_data = indicators['macd'][['time', 'histogram']].rename(columns={'histogram': 'value'})
        histogram = macd_chart.create_histogram(color_up='green', color_down='red')
        histogram.set(histogram_data)
        
        macd_chart.watermark('MACD')
    
    # Set chart title
    chart.watermark(f"{symbol} - {timeframe}")
    
    # Show chart
    chart.show(block=True)

def create_grid_chart(exchange, symbols, timeframe, limit=500):
    """Create a grid of charts."""
    # Determine grid dimensions
    num_symbols = len(symbols)
    if num_symbols <= 2:
        cols = num_symbols
        rows = 1
    else:
        cols = 2
        rows = (num_symbols + 1) // 2
    
    # Create main chart
    chart = Chart(inner_width=1/cols, inner_height=1/rows)
    charts = [chart]
    
    # Create subcharts
    for i in range(1, num_symbols):
        if i == 1:
            subchart = chart.create_subchart(position='right', width=1/cols, height=1/rows)
        elif i % 2 == 0:
            subchart = charts[i-2].create_subchart(position='bottom', width=1/cols, height=1/rows)
        else:
            subchart = charts[i-2].create_subchart(position='right', width=1/cols, height=1/rows)
        charts.append(subchart)
    
    # Fetch data and set charts
    for i, symbol in enumerate(symbols):
        df = fetch_ohlcv(exchange, symbol, timeframe, limit)
        if df is not None:
            # Format DataFrame for lightweight-charts
            df_formatted = df.copy()
            df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Set chart data
            charts[i].set(df_formatted)
            charts[i].watermark(f"{symbol} - {timeframe}")
    
    # Show grid
    chart.show(block=True)

def create_multi_timeframe_chart(exchange, symbol, timeframes, limit=500):
    """Create a multi-timeframe chart."""
    # Create main chart
    chart = Chart(inner_width=0.5, inner_height=0.5)
    charts = [chart]
    
    # Create subcharts
    for i in range(1, len(timeframes)):
        if i == 1:
            subchart = chart.create_subchart(position='right', width=0.5, height=0.5)
        elif i % 2 == 0:
            subchart = charts[i-2].create_subchart(position='bottom', width=0.5, height=0.5)
        else:
            subchart = charts[i-2].create_subchart(position='right', width=0.5, height=0.5)
        charts.append(subchart)
    
    # Fetch data and set charts
    for i, timeframe in enumerate(timeframes):
        df = fetch_ohlcv(exchange, symbol, timeframe, limit)
        if df is not None:
            # Format DataFrame for lightweight-charts
            df_formatted = df.copy()
            df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Set chart data
            charts[i].set(df_formatted)
            charts[i].watermark(f"{symbol} - {timeframe}")
    
    # Show chart
    chart.show(block=True)

def create_support_resistance_chart(exchange, symbol, timeframe, limit=500):
    """Create a chart with support and resistance levels."""
    # Fetch OHLCV data
    df = fetch_ohlcv(exchange, symbol, timeframe, limit)
    if df is None:
        print("Failed to fetch data.")
        return
    
    # Find support and resistance levels
    support_levels, resistance_levels = find_support_resistance(df)
    
    # Format DataFrame for lightweight-charts
    df_formatted = df.copy()
    df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Create chart
    chart = Chart(volume_enabled=True)
    
    # Set chart data
    chart.set(df_formatted)
    
    # Add support levels
    for level in support_levels:
        chart.create_horizontal_line(level, color='green', line_style='dashed', line_width=1)
    
    # Add resistance levels
    for level in resistance_levels:
        chart.create_horizontal_line(level, color='red', line_style='dashed', line_width=1)
    
    # Set chart title
    chart.watermark(f"{symbol} - {timeframe} (Support/Resistance)")
    
    # Show chart
    chart.show(block=True)

def main():
    """Main function."""
    # Parse command line arguments
    args = parse_args()
    
    # Initialize exchange
    exchange = initialize_exchange(args.exchange)
    if exchange is None:
        return
    
    # Create chart based on mode
    if args.mode == 'single':
        create_single_chart(exchange, args.symbol, args.timeframe, args.limit)
    elif args.mode == 'grid':
        symbols = [args.symbol, 'ETH/USDT', 'SOL/USDT', 'XRP/USDT']
        create_grid_chart(exchange, symbols, args.timeframe, args.limit)
    elif args.mode == 'multi':
        timeframes = ['5m', '15m', '1h', '4h']
        create_multi_timeframe_chart(exchange, args.symbol, timeframes, args.limit)
    elif args.mode == 'sr':
        create_support_resistance_chart(exchange, args.symbol, args.timeframe, args.limit)

if __name__ == '__main__':
    main()
