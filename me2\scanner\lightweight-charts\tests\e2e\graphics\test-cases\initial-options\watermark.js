function generateData() {
	const res = [];
	const time = new Date(Date.UTC(2018, 0, 1, 0, 0, 0, 0));
	for (let i = 0; i < 500; ++i) {
		res.push({
			time: time.getTime() / 1000,
			value: i,
		});

		time.setUTCDate(time.getUTCDate() + 1);
	}
	return res;
}

function runTestCase(container) {
	const chart = (window.chart = LightweightCharts.createChart(container, {
		layout: { attributionLogo: false },
	}));

	const mainSeries = chart.addSeries(LightweightCharts.AreaSeries);
	mainSeries.setData(generateData());

	LightweightCharts.createTextWatermark(chart.panes()[0], {
		visible: true,
		lines: [
			{
				color: 'red',
				text: 'TradingView Watermark Example',
				fontSize: 24,
				fontFamily: 'Roboto',
				fontStyle: 'italic',
			},
		],
	});
}
