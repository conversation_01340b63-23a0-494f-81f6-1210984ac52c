#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Performance Metrics Dashboard
----------------------------
A dashboard for monitoring trading performance metrics in real-time.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import os
import json
import pyqtgraph as pg
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QComboBox, QTabWidget, QSplitter, QFrame,
    QDateEdit, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt, QTimer, Signal, Slot
from PySide6.QtGui import QColor, QPen, QBrush

# Set up logging
logger = logging.getLogger('performance_dashboard')

class PerformanceDashboard(QWidget):
    """
    Performance Metrics Dashboard widget.

    This widget displays trading performance metrics including:
    - Key metrics (win rate, profit factor, drawdown)
    - Equity curve
    - Trade history
    - Performance by timeframe, symbol, etc.
    """

    # Signal to update the dashboard from a background thread
    update_signal = Signal(dict)

    def __init__(self, parent=None):
        """Initialize the Performance Dashboard widget."""
        super().__init__(parent)

        # Initialize data structures
        self.trades = []  # List of trade dictionaries
        self.equity_curve = []  # List of equity points
        self.metrics = {
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'avg_trade': 0.0,
            'expectancy': 0.0,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'recovery_factor': 0.0,
            'profit_to_drawdown': 0.0,
            'win_streak': 0,
            'lose_streak': 0,
            'max_win_streak': 0,
            'max_lose_streak': 0,
            'avg_time_in_trade': 0.0,
            'total_pnl': 0.0,
            'total_fees': 0.0,
            'net_pnl': 0.0
        }

        # Initialize UI
        self.init_ui()

        # Connect signals
        self.update_signal.connect(self.update_dashboard)

        # Set up update timer (update every 30 seconds)
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.refresh_dashboard)
        self.update_timer.start(30000)  # 30 seconds

    def init_ui(self):
        """Initialize the dashboard UI."""
        # Main layout
        main_layout = QVBoxLayout(self)

        # Create tabs for different dashboard views
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabBar::tab { background: #222; padding: 8px; color: #ddd; }
            QTabBar::tab:selected { background: #444; }
        """)

        # Overview tab
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)

        # Top section: Key metrics
        metrics_panel = self._create_metrics_panel()
        overview_layout.addWidget(metrics_panel)

        # Middle section: Equity curve
        equity_panel = self._create_equity_panel()
        overview_layout.addWidget(equity_panel)

        # Bottom section: Recent trades
        trades_panel = self._create_trades_panel()
        overview_layout.addWidget(trades_panel)

        # Add overview tab
        self.tabs.addTab(overview_tab, "Overview")

        # Analysis tab
        analysis_tab = self._create_analysis_tab()
        self.tabs.addTab(analysis_tab, "Analysis")

        # Add tabs to main layout
        main_layout.addWidget(self.tabs)

        # Add refresh button at the bottom
        refresh_btn = QPushButton("Refresh Dashboard")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #00ff44;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_dashboard)
        main_layout.addWidget(refresh_btn)

    def _create_metrics_panel(self):
        """Create the key metrics panel."""
        metrics_group = QGroupBox("Key Performance Metrics")
        metrics_layout = QGridLayout(metrics_group)

        # Create metric labels
        self.win_rate_label = QLabel("Win Rate: 0.0%")
        self.profit_factor_label = QLabel("Profit Factor: 0.0")
        self.drawdown_label = QLabel("Max Drawdown: 0.0%")
        self.total_trades_label = QLabel("Total Trades: 0")
        self.expectancy_label = QLabel("Expectancy: $0.0")
        self.sharpe_label = QLabel("Sharpe Ratio: 0.0")
        self.total_pnl_label = QLabel("Total P&L: $0.0")
        self.avg_trade_label = QLabel("Avg Trade: $0.0")

        # Style the labels
        metric_style = "color: #00ff44; font-weight: bold; font-size: 12px;"
        self.win_rate_label.setStyleSheet(metric_style)
        self.profit_factor_label.setStyleSheet(metric_style)
        self.drawdown_label.setStyleSheet(metric_style)
        self.total_trades_label.setStyleSheet(metric_style)
        self.expectancy_label.setStyleSheet(metric_style)
        self.sharpe_label.setStyleSheet(metric_style)
        self.total_pnl_label.setStyleSheet(metric_style)
        self.avg_trade_label.setStyleSheet(metric_style)

        # Add labels to grid layout
        metrics_layout.addWidget(self.win_rate_label, 0, 0)
        metrics_layout.addWidget(self.profit_factor_label, 0, 1)
        metrics_layout.addWidget(self.drawdown_label, 0, 2)
        metrics_layout.addWidget(self.total_trades_label, 0, 3)
        metrics_layout.addWidget(self.expectancy_label, 1, 0)
        metrics_layout.addWidget(self.sharpe_label, 1, 1)
        metrics_layout.addWidget(self.total_pnl_label, 1, 2)
        metrics_layout.addWidget(self.avg_trade_label, 1, 3)

        return metrics_group

    def _create_equity_panel(self):
        """Create the equity curve panel."""
        equity_group = QGroupBox("Equity Curve")
        equity_layout = QVBoxLayout(equity_group)

        # Create pyqtgraph plot widget
        self.equity_plot = pg.PlotWidget(
            background='#1a1a1a',
            axisItems={'bottom': pg.DateAxisItem(orientation='bottom')}
        )
        self.equity_plot.showGrid(x=True, y=True, alpha=0.3)
        self.equity_plot.setLabel('left', 'Equity ($)')
        self.equity_plot.setLabel('bottom', 'Date/Time')

        # Add plot to layout
        equity_layout.addWidget(self.equity_plot)

        return equity_group

    def _create_trades_panel(self):
        """Create the recent trades panel."""
        trades_group = QGroupBox("Recent Trades")
        trades_layout = QVBoxLayout(trades_group)

        # Create table for trades
        self.trades_table = QTableWidget()
        self.trades_table.setColumnCount(8)
        self.trades_table.setHorizontalHeaderLabels([
            "Date/Time", "Symbol", "Direction", "Entry", "Exit", "Size", "P&L", "P&L %"
        ])

        # Set table properties
        self.trades_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.trades_table.setAlternatingRowColors(True)
        self.trades_table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                color: #eef;
                gridline-color: #333;
                alternate-background-color: #252525;
            }
            QHeaderView::section {
                background-color: #222;
                color: #ddd;
                font-weight: bold;
                padding: 4px;
            }
        """)

        # Add table to layout
        trades_layout.addWidget(self.trades_table)

        return trades_group

    def _create_analysis_tab(self):
        """Create the analysis tab with detailed metrics."""
        analysis_tab = QWidget()
        analysis_layout = QVBoxLayout(analysis_tab)

        # Create filter controls
        filter_group = QGroupBox("Filters")
        filter_layout = QHBoxLayout(filter_group)

        # Date range filter
        filter_layout.addWidget(QLabel("From:"))
        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(True)
        self.date_from.setDate(datetime.now().date() - timedelta(days=30))
        filter_layout.addWidget(self.date_from)

        filter_layout.addWidget(QLabel("To:"))
        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(True)
        self.date_to.setDate(datetime.now().date())
        filter_layout.addWidget(self.date_to)

        # Symbol filter
        filter_layout.addWidget(QLabel("Symbol:"))
        self.symbol_filter = QComboBox()
        self.symbol_filter.addItem("All Symbols")
        filter_layout.addWidget(self.symbol_filter)

        # Direction filter
        filter_layout.addWidget(QLabel("Direction:"))
        self.direction_filter = QComboBox()
        self.direction_filter.addItems(["All", "Long", "Short"])
        filter_layout.addWidget(self.direction_filter)

        # Apply filter button
        self.apply_filter_btn = QPushButton("Apply Filters")
        self.apply_filter_btn.clicked.connect(self.apply_filters)
        filter_layout.addWidget(self.apply_filter_btn)

        # Add filter group to layout
        analysis_layout.addWidget(filter_group)

        # Create detailed metrics tables
        metrics_tabs = QTabWidget()

        # By symbol
        self.symbol_table = QTableWidget()
        self.symbol_table.setColumnCount(7)
        self.symbol_table.setHorizontalHeaderLabels([
            "Symbol", "Trades", "Win Rate", "Profit Factor", "Avg P&L", "Total P&L", "Drawdown"
        ])
        self.symbol_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        metrics_tabs.addTab(self.symbol_table, "By Symbol")

        # By timeframe
        self.timeframe_table = QTableWidget()
        self.timeframe_table.setColumnCount(7)
        self.timeframe_table.setHorizontalHeaderLabels([
            "Timeframe", "Trades", "Win Rate", "Profit Factor", "Avg P&L", "Total P&L", "Drawdown"
        ])
        self.timeframe_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        metrics_tabs.addTab(self.timeframe_table, "By Timeframe")

        # By day of week
        self.dow_table = QTableWidget()
        self.dow_table.setColumnCount(7)
        self.dow_table.setHorizontalHeaderLabels([
            "Day", "Trades", "Win Rate", "Profit Factor", "Avg P&L", "Total P&L", "Drawdown"
        ])
        self.dow_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        metrics_tabs.addTab(self.dow_table, "By Day of Week")

        # By hour of day
        self.hour_table = QTableWidget()
        self.hour_table.setColumnCount(7)
        self.hour_table.setHorizontalHeaderLabels([
            "Hour", "Trades", "Win Rate", "Profit Factor", "Avg P&L", "Total P&L", "Drawdown"
        ])
        self.hour_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        metrics_tabs.addTab(self.hour_table, "By Hour")

        # Add metrics tabs to layout
        analysis_layout.addWidget(metrics_tabs)

        return analysis_tab

    @Slot(dict)
    def update_dashboard(self, data):
        """
        Update the dashboard with new data.

        Args:
            data (dict): Dictionary containing trades, equity curve, and metrics.
        """
        # Update data
        if 'trades' in data:
            self.trades = data['trades']
        if 'equity_curve' in data:
            self.equity_curve = data['equity_curve']
        if 'metrics' in data:
            self.metrics.update(data['metrics'])

        # Update UI
        self._update_metrics_panel()
        self._update_equity_curve()
        self._update_trades_table()
        self._update_analysis_tables()

    def _update_metrics_panel(self):
        """Update the metrics panel with current metrics."""
        # Update metric labels
        self.win_rate_label.setText(f"Win Rate: {self.metrics['win_rate']:.2f}%")
        self.profit_factor_label.setText(f"Profit Factor: {self.metrics['profit_factor']:.2f}")
        self.drawdown_label.setText(f"Max Drawdown: {self.metrics['max_drawdown']:.2f}%")
        self.total_trades_label.setText(f"Total Trades: {self.metrics['total_trades']}")
        self.expectancy_label.setText(f"Expectancy: ${self.metrics['expectancy']:.2f}")
        self.sharpe_label.setText(f"Sharpe Ratio: {self.metrics['sharpe_ratio']:.2f}")
        self.total_pnl_label.setText(f"Total P&L: ${self.metrics['net_pnl']:.2f}")
        self.avg_trade_label.setText(f"Avg Trade: ${self.metrics['avg_trade']:.2f}")

        # Color code based on performance
        if self.metrics['win_rate'] >= 50:
            self.win_rate_label.setStyleSheet("color: #00ff44; font-weight: bold; font-size: 12px;")
        else:
            self.win_rate_label.setStyleSheet("color: #ff4444; font-weight: bold; font-size: 12px;")

        if self.metrics['profit_factor'] >= 1.5:
            self.profit_factor_label.setStyleSheet("color: #00ff44; font-weight: bold; font-size: 12px;")
        elif self.metrics['profit_factor'] >= 1.0:
            self.profit_factor_label.setStyleSheet("color: #ffff44; font-weight: bold; font-size: 12px;")
        else:
            self.profit_factor_label.setStyleSheet("color: #ff4444; font-weight: bold; font-size: 12px;")

        if self.metrics['net_pnl'] > 0:
            self.total_pnl_label.setStyleSheet("color: #00ff44; font-weight: bold; font-size: 12px;")
        else:
            self.total_pnl_label.setStyleSheet("color: #ff4444; font-weight: bold; font-size: 12px;")

    def _update_equity_curve(self):
        """Update the equity curve chart."""
        # Clear the plot
        self.equity_plot.clear()

        if not self.equity_curve:
            return

        # Extract timestamps and equity values
        timestamps = [point['timestamp'] for point in self.equity_curve]
        equity = [point['equity'] for point in self.equity_curve]

        # Convert timestamps to seconds since epoch
        if isinstance(timestamps[0], str):
            # Convert string timestamps to datetime objects
            timestamps = [datetime.fromisoformat(ts) for ts in timestamps]

        # Convert datetime objects to seconds since epoch
        timestamps = [ts.timestamp() for ts in timestamps]

        # Create the equity curve plot
        pen = pg.mkPen(color='#00ff44', width=2)
        self.equity_plot.plot(timestamps, equity, pen=pen)

        # Add drawdown overlay if we have drawdown data
        if 'drawdown' in self.equity_curve[0]:
            drawdown = [point['drawdown'] for point in self.equity_curve]
            # Scale drawdown to match equity scale (invert and scale)
            max_equity = max(equity)
            scaled_drawdown = [max_equity * (1 - dd/100) for dd in drawdown]

            # Plot drawdown with red pen
            dd_pen = pg.mkPen(color='#ff4444', width=1, style=Qt.DashLine)
            self.equity_plot.plot(timestamps, scaled_drawdown, pen=dd_pen)

        # Auto-range the view
        self.equity_plot.autoRange()

    def _update_trades_table(self):
        """Update the trades table with recent trades."""
        # Clear the table
        self.trades_table.setRowCount(0)

        if not self.trades:
            return

        # Sort trades by timestamp (newest first) with proper timestamp normalization
        def get_timestamp_for_sorting(trade):
            """Extract and normalize timestamp for sorting"""
            timestamp = trade.get('timestamp', '')

            # Handle different timestamp formats
            if isinstance(timestamp, datetime):
                return timestamp
            elif isinstance(timestamp, str):
                if timestamp:
                    try:
                        # Try to parse ISO format string
                        return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        try:
                            # Try to parse as timestamp string
                            return datetime.fromtimestamp(float(timestamp))
                        except (ValueError, TypeError):
                            # Return a very old date for invalid timestamps
                            return datetime(1970, 1, 1)
                else:
                    # Empty string, return very old date
                    return datetime(1970, 1, 1)
            elif isinstance(timestamp, (int, float)):
                try:
                    # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                    if timestamp > 1e10:
                        return datetime.fromtimestamp(timestamp / 1000)
                    else:
                        return datetime.fromtimestamp(timestamp)
                except (ValueError, OSError):
                    return datetime(1970, 1, 1)
            else:
                # Unknown type, return very old date
                return datetime(1970, 1, 1)

        try:
            sorted_trades = sorted(self.trades, key=get_timestamp_for_sorting, reverse=True)
        except Exception as e:
            print(f"Error sorting trades by timestamp: {e}")
            # Fallback: use trades as-is without sorting
            sorted_trades = self.trades

        # Limit to most recent 100 trades
        recent_trades = sorted_trades[:100]

        # Set row count
        self.trades_table.setRowCount(len(recent_trades))

        # Populate table
        for i, trade in enumerate(recent_trades):
            # Extract trade data
            timestamp = trade.get('timestamp', '')
            if isinstance(timestamp, str):
                try:
                    timestamp = datetime.fromisoformat(timestamp).strftime('%Y-%m-%d %H:%M')
                except:
                    pass

            symbol = trade.get('symbol', '')
            direction = trade.get('direction', '')
            entry_price = trade.get('entry_price', 0)
            exit_price = trade.get('exit_price', 0)
            size = trade.get('size', 0)
            pnl = trade.get('pnl', 0)
            pnl_percent = trade.get('pnl_percent', 0)

            # Create table items
            timestamp_item = QTableWidgetItem(str(timestamp))
            symbol_item = QTableWidgetItem(symbol)
            direction_item = QTableWidgetItem(direction)
            entry_item = QTableWidgetItem(f"{entry_price:.6f}")
            exit_item = QTableWidgetItem(f"{exit_price:.6f}")
            size_item = QTableWidgetItem(f"{size:.4f}")
            pnl_item = QTableWidgetItem(f"${pnl:.2f}")
            pnl_percent_item = QTableWidgetItem(f"{pnl_percent:.2f}%")

            # Set alignment
            for item in [entry_item, exit_item, size_item, pnl_item, pnl_percent_item]:
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # Color code P&L
            if pnl > 0:
                pnl_item.setForeground(QColor('#00ff44'))
                pnl_percent_item.setForeground(QColor('#00ff44'))
            elif pnl < 0:
                pnl_item.setForeground(QColor('#ff4444'))
                pnl_percent_item.setForeground(QColor('#ff4444'))

            # Color code direction
            if direction.lower() == 'long':
                direction_item.setForeground(QColor('#00ff44'))
            elif direction.lower() == 'short':
                direction_item.setForeground(QColor('#ff4444'))

            # Add items to table
            self.trades_table.setItem(i, 0, timestamp_item)
            self.trades_table.setItem(i, 1, symbol_item)
            self.trades_table.setItem(i, 2, direction_item)
            self.trades_table.setItem(i, 3, entry_item)
            self.trades_table.setItem(i, 4, exit_item)
            self.trades_table.setItem(i, 5, size_item)
            self.trades_table.setItem(i, 6, pnl_item)
            self.trades_table.setItem(i, 7, pnl_percent_item)

    def _update_analysis_tables(self):
        """Update the analysis tables with filtered data."""
        if not self.trades:
            return

        # Apply current filters
        filtered_trades = self._apply_current_filters()

        # Update symbol table
        self._update_symbol_table(filtered_trades)

        # Update timeframe table
        self._update_timeframe_table(filtered_trades)

        # Update day of week table
        self._update_dow_table(filtered_trades)

        # Update hour table
        self._update_hour_table(filtered_trades)

    def _apply_current_filters(self):
        """Apply current filters to trades data."""
        filtered_trades = self.trades.copy()

        # Apply date filter
        from_date = self.date_from.date().toPython()
        to_date = self.date_to.date().toPython() + timedelta(days=1)  # Include the end date

        filtered_trades = [
            trade for trade in filtered_trades
            if self._get_trade_date(trade) >= from_date and self._get_trade_date(trade) < to_date
        ]

        # Apply symbol filter
        symbol_filter = self.symbol_filter.currentText()
        if symbol_filter != "All Symbols":
            filtered_trades = [
                trade for trade in filtered_trades
                if trade.get('symbol', '') == symbol_filter
            ]

        # Apply direction filter
        direction_filter = self.direction_filter.currentText()
        if direction_filter != "All":
            filtered_trades = [
                trade for trade in filtered_trades
                if trade.get('direction', '').lower() == direction_filter.lower()
            ]

        return filtered_trades

    def _get_trade_date(self, trade):
        """Extract date from trade timestamp with robust handling."""
        timestamp = trade.get('timestamp', '')

        # Handle different timestamp formats
        if isinstance(timestamp, datetime):
            return timestamp.date()
        elif isinstance(timestamp, str):
            if timestamp:
                try:
                    # Try to parse ISO format string
                    return datetime.fromisoformat(timestamp.replace('Z', '+00:00')).date()
                except (ValueError, AttributeError):
                    try:
                        # Try to parse as timestamp string
                        return datetime.fromtimestamp(float(timestamp)).date()
                    except (ValueError, TypeError):
                        return datetime.now().date()
            else:
                return datetime.now().date()
        elif isinstance(timestamp, (int, float)):
            try:
                # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                if timestamp > 1e10:
                    return datetime.fromtimestamp(timestamp / 1000).date()
                else:
                    return datetime.fromtimestamp(timestamp).date()
            except (ValueError, OSError):
                return datetime.now().date()
        else:
            return datetime.now().date()

    def _update_symbol_table(self, filtered_trades):
        """Update the symbol analysis table."""
        # Group trades by symbol
        symbols = {}
        for trade in filtered_trades:
            symbol = trade.get('symbol', '')
            if symbol not in symbols:
                symbols[symbol] = []
            symbols[symbol].append(trade)

        # Calculate metrics for each symbol
        symbol_metrics = []
        for symbol, trades in symbols.items():
            metrics = self._calculate_metrics(trades)
            symbol_metrics.append({
                'symbol': symbol,
                'trades': len(trades),
                'win_rate': metrics['win_rate'],
                'profit_factor': metrics['profit_factor'],
                'avg_pnl': metrics['avg_trade'],
                'total_pnl': metrics['net_pnl'],
                'drawdown': metrics['max_drawdown']
            })

        # Sort by total P&L
        symbol_metrics.sort(key=lambda x: x['total_pnl'], reverse=True)

        # Update table
        self.symbol_table.setRowCount(len(symbol_metrics))
        for i, metrics in enumerate(symbol_metrics):
            self.symbol_table.setItem(i, 0, QTableWidgetItem(metrics['symbol']))
            self.symbol_table.setItem(i, 1, QTableWidgetItem(str(metrics['trades'])))
            self.symbol_table.setItem(i, 2, QTableWidgetItem(f"{metrics['win_rate']:.2f}%"))
            self.symbol_table.setItem(i, 3, QTableWidgetItem(f"{metrics['profit_factor']:.2f}"))
            self.symbol_table.setItem(i, 4, QTableWidgetItem(f"${metrics['avg_pnl']:.2f}"))

            pnl_item = QTableWidgetItem(f"${metrics['total_pnl']:.2f}")
            if metrics['total_pnl'] > 0:
                pnl_item.setForeground(QColor('#00ff44'))
            elif metrics['total_pnl'] < 0:
                pnl_item.setForeground(QColor('#ff4444'))
            self.symbol_table.setItem(i, 5, pnl_item)

            self.symbol_table.setItem(i, 6, QTableWidgetItem(f"{metrics['drawdown']:.2f}%"))

    def _update_timeframe_table(self, filtered_trades):
        """Update the timeframe analysis table."""
        # Group trades by timeframe
        timeframes = {}
        for trade in filtered_trades:
            tf = trade.get('timeframe', 'unknown')
            if tf not in timeframes:
                timeframes[tf] = []
            timeframes[tf].append(trade)

        # Calculate metrics for each timeframe
        tf_metrics = []
        for tf, trades in timeframes.items():
            metrics = self._calculate_metrics(trades)
            tf_metrics.append({
                'timeframe': tf,
                'trades': len(trades),
                'win_rate': metrics['win_rate'],
                'profit_factor': metrics['profit_factor'],
                'avg_pnl': metrics['avg_trade'],
                'total_pnl': metrics['net_pnl'],
                'drawdown': metrics['max_drawdown']
            })

        # Sort by total P&L
        tf_metrics.sort(key=lambda x: x['total_pnl'], reverse=True)

        # Update table
        self.timeframe_table.setRowCount(len(tf_metrics))
        for i, metrics in enumerate(tf_metrics):
            self.timeframe_table.setItem(i, 0, QTableWidgetItem(metrics['timeframe']))
            self.timeframe_table.setItem(i, 1, QTableWidgetItem(str(metrics['trades'])))
            self.timeframe_table.setItem(i, 2, QTableWidgetItem(f"{metrics['win_rate']:.2f}%"))
            self.timeframe_table.setItem(i, 3, QTableWidgetItem(f"{metrics['profit_factor']:.2f}"))
            self.timeframe_table.setItem(i, 4, QTableWidgetItem(f"${metrics['avg_pnl']:.2f}"))

            pnl_item = QTableWidgetItem(f"${metrics['total_pnl']:.2f}")
            if metrics['total_pnl'] > 0:
                pnl_item.setForeground(QColor('#00ff44'))
            elif metrics['total_pnl'] < 0:
                pnl_item.setForeground(QColor('#ff4444'))
            self.timeframe_table.setItem(i, 5, pnl_item)

            self.timeframe_table.setItem(i, 6, QTableWidgetItem(f"{metrics['drawdown']:.2f}%"))

    def _update_dow_table(self, filtered_trades):
        """Update the day of week analysis table."""
        # Group trades by day of week
        days = {0: 'Monday', 1: 'Tuesday', 2: 'Wednesday', 3: 'Thursday', 4: 'Friday', 5: 'Saturday', 6: 'Sunday'}
        dow_trades = {day: [] for day in range(7)}

        for trade in filtered_trades:
            timestamp = trade.get('timestamp', '')

            # Use the same robust timestamp parsing as other methods
            try:
                if isinstance(timestamp, datetime):
                    day = timestamp.weekday()
                elif isinstance(timestamp, str):
                    if timestamp:
                        try:
                            # Try to parse ISO format string
                            day = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).weekday()
                        except (ValueError, AttributeError):
                            try:
                                # Try to parse as timestamp string
                                day = datetime.fromtimestamp(float(timestamp)).weekday()
                            except (ValueError, TypeError):
                                continue
                    else:
                        continue
                elif isinstance(timestamp, (int, float)):
                    # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                    if timestamp > 1e10:
                        day = datetime.fromtimestamp(timestamp / 1000).weekday()
                    else:
                        day = datetime.fromtimestamp(timestamp).weekday()
                else:
                    continue

                dow_trades[day].append(trade)
            except (ValueError, OSError, TypeError):
                # Skip trades with invalid timestamps
                continue

        # Calculate metrics for each day
        dow_metrics = []
        for day, trades in dow_trades.items():
            if not trades:
                continue

            metrics = self._calculate_metrics(trades)
            dow_metrics.append({
                'day': days[day],
                'day_num': day,
                'trades': len(trades),
                'win_rate': metrics['win_rate'],
                'profit_factor': metrics['profit_factor'],
                'avg_pnl': metrics['avg_trade'],
                'total_pnl': metrics['net_pnl'],
                'drawdown': metrics['max_drawdown']
            })

        # Sort by day of week
        dow_metrics.sort(key=lambda x: x['day_num'])

        # Update table
        self.dow_table.setRowCount(len(dow_metrics))
        for i, metrics in enumerate(dow_metrics):
            self.dow_table.setItem(i, 0, QTableWidgetItem(metrics['day']))
            self.dow_table.setItem(i, 1, QTableWidgetItem(str(metrics['trades'])))
            self.dow_table.setItem(i, 2, QTableWidgetItem(f"{metrics['win_rate']:.2f}%"))
            self.dow_table.setItem(i, 3, QTableWidgetItem(f"{metrics['profit_factor']:.2f}"))
            self.dow_table.setItem(i, 4, QTableWidgetItem(f"${metrics['avg_pnl']:.2f}"))

            pnl_item = QTableWidgetItem(f"${metrics['total_pnl']:.2f}")
            if metrics['total_pnl'] > 0:
                pnl_item.setForeground(QColor('#00ff44'))
            elif metrics['total_pnl'] < 0:
                pnl_item.setForeground(QColor('#ff4444'))
            self.dow_table.setItem(i, 5, pnl_item)

            self.dow_table.setItem(i, 6, QTableWidgetItem(f"{metrics['drawdown']:.2f}%"))

    def _update_hour_table(self, filtered_trades):
        """Update the hour of day analysis table."""
        # Group trades by hour of day
        hour_trades = {hour: [] for hour in range(24)}

        for trade in filtered_trades:
            timestamp = trade.get('timestamp', '')

            # Use the same robust timestamp parsing as other methods
            try:
                if isinstance(timestamp, datetime):
                    hour = timestamp.hour
                elif isinstance(timestamp, str):
                    if timestamp:
                        try:
                            # Try to parse ISO format string
                            hour = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).hour
                        except (ValueError, AttributeError):
                            try:
                                # Try to parse as timestamp string
                                hour = datetime.fromtimestamp(float(timestamp)).hour
                            except (ValueError, TypeError):
                                continue
                    else:
                        continue
                elif isinstance(timestamp, (int, float)):
                    # Handle numeric timestamps (assume milliseconds if > 1e10, else seconds)
                    if timestamp > 1e10:
                        hour = datetime.fromtimestamp(timestamp / 1000).hour
                    else:
                        hour = datetime.fromtimestamp(timestamp).hour
                else:
                    continue

                hour_trades[hour].append(trade)
            except (ValueError, OSError, TypeError):
                # Skip trades with invalid timestamps
                continue

        # Calculate metrics for each hour
        hour_metrics = []
        for hour, trades in hour_trades.items():
            if not trades:
                continue

            metrics = self._calculate_metrics(trades)
            hour_metrics.append({
                'hour': f"{hour:02d}:00",
                'hour_num': hour,
                'trades': len(trades),
                'win_rate': metrics['win_rate'],
                'profit_factor': metrics['profit_factor'],
                'avg_pnl': metrics['avg_trade'],
                'total_pnl': metrics['net_pnl'],
                'drawdown': metrics['max_drawdown']
            })

        # Sort by hour
        hour_metrics.sort(key=lambda x: x['hour_num'])

        # Update table
        self.hour_table.setRowCount(len(hour_metrics))
        for i, metrics in enumerate(hour_metrics):
            self.hour_table.setItem(i, 0, QTableWidgetItem(metrics['hour']))
            self.hour_table.setItem(i, 1, QTableWidgetItem(str(metrics['trades'])))
            self.hour_table.setItem(i, 2, QTableWidgetItem(f"{metrics['win_rate']:.2f}%"))
            self.hour_table.setItem(i, 3, QTableWidgetItem(f"{metrics['profit_factor']:.2f}"))
            self.hour_table.setItem(i, 4, QTableWidgetItem(f"${metrics['avg_pnl']:.2f}"))

            pnl_item = QTableWidgetItem(f"${metrics['total_pnl']:.2f}")
            if metrics['total_pnl'] > 0:
                pnl_item.setForeground(QColor('#00ff44'))
            elif metrics['total_pnl'] < 0:
                pnl_item.setForeground(QColor('#ff4444'))
            self.hour_table.setItem(i, 5, pnl_item)

            self.hour_table.setItem(i, 6, QTableWidgetItem(f"{metrics['drawdown']:.2f}%"))

    def _calculate_metrics(self, trades):
        """Calculate performance metrics for a list of trades."""
        if not trades:
            return {
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
                'avg_trade': 0.0,
                'expectancy': 0.0,
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'calmar_ratio': 0.0,
                'recovery_factor': 0.0,
                'profit_to_drawdown': 0.0,
                'win_streak': 0,
                'lose_streak': 0,
                'max_win_streak': 0,
                'max_lose_streak': 0,
                'avg_time_in_trade': 0.0,
                'total_pnl': 0.0,
                'total_fees': 0.0,
                'net_pnl': 0.0
            }

        # Extract PnL values
        pnl_values = [trade.get('pnl', 0) for trade in trades]

        # Calculate basic metrics
        total_pnl = sum(pnl_values)
        total_trades = len(trades)
        winning_trades = sum(1 for pnl in pnl_values if pnl > 0)
        losing_trades = sum(1 for pnl in pnl_values if pnl < 0)

        # Win rate
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        # Average win/loss
        winning_pnl = [pnl for pnl in pnl_values if pnl > 0]
        losing_pnl = [pnl for pnl in pnl_values if pnl < 0]

        avg_win = sum(winning_pnl) / len(winning_pnl) if winning_pnl else 0
        avg_loss = sum(losing_pnl) / len(losing_pnl) if losing_pnl else 0

        # Largest win/loss
        largest_win = max(winning_pnl) if winning_pnl else 0
        largest_loss = min(losing_pnl) if losing_pnl else 0

        # Average trade
        avg_trade = total_pnl / total_trades if total_trades > 0 else 0

        # Profit factor
        gross_profit = sum(winning_pnl)
        gross_loss = abs(sum(losing_pnl))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # Expectancy
        win_probability = winning_trades / total_trades if total_trades > 0 else 0
        loss_probability = losing_trades / total_trades if total_trades > 0 else 0
        expectancy = (win_probability * avg_win) - (loss_probability * abs(avg_loss))

        # Calculate drawdown
        equity_curve = []
        current_equity = 0
        peak_equity = 0
        max_drawdown = 0

        for pnl in pnl_values:
            current_equity += pnl
            peak_equity = max(peak_equity, current_equity)
            drawdown = peak_equity - current_equity
            max_drawdown = max(max_drawdown, drawdown)
            equity_curve.append(current_equity)

        # Calculate drawdown as percentage
        max_drawdown_pct = (max_drawdown / peak_equity * 100) if peak_equity > 0 else 0

        # Calculate streaks
        current_win_streak = 0
        current_lose_streak = 0
        max_win_streak = 0
        max_lose_streak = 0

        for pnl in pnl_values:
            if pnl > 0:
                current_win_streak += 1
                current_lose_streak = 0
                max_win_streak = max(max_win_streak, current_win_streak)
            elif pnl < 0:
                current_lose_streak += 1
                current_win_streak = 0
                max_lose_streak = max(max_lose_streak, current_lose_streak)
            else:
                # Flat trade, reset both streaks
                current_win_streak = 0
                current_lose_streak = 0

        # Calculate fees
        total_fees = sum(trade.get('fee', 0) for trade in trades)

        # Calculate net PnL
        net_pnl = total_pnl - total_fees

        # Return metrics
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown_pct,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'avg_trade': avg_trade,
            'expectancy': expectancy,
            'sharpe_ratio': 0.0,  # Would need daily returns to calculate
            'sortino_ratio': 0.0,  # Would need daily returns to calculate
            'calmar_ratio': 0.0,  # Would need annual return to calculate
            'recovery_factor': 0.0,  # Would need more data to calculate
            'profit_to_drawdown': total_pnl / max_drawdown if max_drawdown > 0 else float('inf'),
            'win_streak': current_win_streak,
            'lose_streak': current_lose_streak,
            'max_win_streak': max_win_streak,
            'max_lose_streak': max_lose_streak,
            'avg_time_in_trade': 0.0,  # Would need entry/exit timestamps to calculate
            'total_pnl': total_pnl,
            'total_fees': total_fees,
            'net_pnl': net_pnl
        }

    def apply_filters(self):
        """Apply filters and update the analysis tables."""
        self._update_analysis_tables()

    def refresh_dashboard(self):
        """Refresh the dashboard with latest data."""
        try:
            # Import global variables fresh each time
            from me2_stable import demo_mode, exchange

            # Debug: Print current state
            print(f"Performance Dashboard: Refresh called - demo_mode={demo_mode}, exchange={'available' if exchange else 'None'}")
            if exchange:
                print(f"Performance Dashboard: Exchange type: {type(exchange).__name__}")
                print(f"Performance Dashboard: Exchange has API key: {hasattr(exchange, 'apiKey') and bool(exchange.apiKey)}")

            # Check if we already have a worker running
            if hasattr(self, '_trade_history_worker') and self._trade_history_worker.isRunning():
                print("Performance Dashboard: Trade history worker already running, skipping refresh")
                return

            # Add a flag to prevent excessive refresh attempts
            if not hasattr(self, '_last_refresh_attempt'):
                self._last_refresh_attempt = 0
                self._refresh_failures = 0

            # Limit refresh attempts if we've had recent failures
            current_time = datetime.now().timestamp()
            if self._refresh_failures > 3 and (current_time - self._last_refresh_attempt) < 60:
                print("Performance Dashboard: Too many recent refresh failures, waiting before retry")
                return

            self._last_refresh_attempt = current_time

            # Create a worker to fetch trade history in the background
            # Try to use real API even if demo_mode is True but we have valid credentials
            should_try_api = exchange is not None and hasattr(exchange, 'apiKey') and exchange.apiKey

            if should_try_api:
                try:
                    print("Performance Dashboard: Attempting to use real API for trade history")
                    # Reset API key validity flag to allow retry
                    from workers import TradeHistoryWorker
                    TradeHistoryWorker.reset_api_key_validity()

                    # Clean up any existing worker before creating a new one
                    self._cleanup_existing_worker()

                    # Create and start the worker thread
                    self._trade_history_worker = TradeHistoryWorker()

                    # Connect signals with error handling
                    self._trade_history_worker.fetched.connect(self._on_trade_history_fetched)
                    self._trade_history_worker.error.connect(self._on_trade_history_error)
                    self._trade_history_worker.finished.connect(self._on_worker_finished)

                    # Start the worker
                    self._trade_history_worker.start()
                    print("Performance Dashboard: Started trade history worker with real API")

                except Exception as e:
                    print(f"Performance Dashboard: Error creating trade history worker: {e}")
                    self._refresh_failures += 1
                    self._use_demo_data()
            else:
                print(f"Performance Dashboard: Using demo data - demo_mode={demo_mode}, exchange={'available' if exchange else 'None'}, has_api_key={hasattr(exchange, 'apiKey') and bool(exchange.apiKey) if exchange else False}")
                self._use_demo_data()

        except Exception as e:
            print(f"Performance Dashboard: Error in refresh_dashboard: {e}")
            self._refresh_failures += 1
            self._use_demo_data()

    def _cleanup_existing_worker(self):
        """Clean up any existing worker thread before creating a new one."""
        if hasattr(self, '_trade_history_worker') and self._trade_history_worker is not None:
            print("Performance Dashboard: Cleaning up existing worker...")

            # Disconnect signals first
            try:
                self._trade_history_worker.fetched.disconnect()
                self._trade_history_worker.error.disconnect()
                self._trade_history_worker.finished.disconnect()
            except Exception as e:
                print(f"Performance Dashboard: Error disconnecting existing worker signals: {e}")

            # Stop the worker if it's running
            if self._trade_history_worker.isRunning():
                print("Performance Dashboard: Stopping existing worker...")
                self._trade_history_worker.quit()
                if not self._trade_history_worker.wait(1000):  # Wait up to 1 second
                    print("Performance Dashboard: Force terminating existing worker...")
                    self._trade_history_worker.terminate()
                    self._trade_history_worker.wait(500)  # Wait up to 0.5 seconds for termination

            # Delete the worker
            try:
                self._trade_history_worker.deleteLater()
            except Exception as e:
                print(f"Performance Dashboard: Error deleting existing worker: {e}")

            self._trade_history_worker = None
            print("Performance Dashboard: Existing worker cleanup completed")

    def _on_trade_history_fetched(self, trades):
        """Handle successful trade history fetch."""
        try:
            print(f"Performance Dashboard: Received {len(trades)} trades")

            # Update the trades data
            self.trades = trades

            # Calculate metrics
            self.metrics = self._calculate_metrics(trades)

            # Generate equity curve
            self._generate_equity_curve()

            # Update the UI
            self._update_dashboard_ui()

            # Reset failure count on success
            self._refresh_failures = 0

        except Exception as e:
            print(f"Performance Dashboard: Error processing trade history: {e}")
            self._refresh_failures += 1

    def _on_trade_history_error(self, error_msg):
        """Handle errors from the trade history worker."""
        print(f"Performance Dashboard: Trade history error: {error_msg}")
        self._refresh_failures += 1
        # Continue with existing data or use demo data
        if not self.trades:
            self._use_demo_data()

    def _on_worker_finished(self):
        """Handle worker thread completion."""
        print("Performance Dashboard: Trade history worker finished")

        # Clean up the worker reference with proper thread cleanup
        if hasattr(self, '_trade_history_worker') and self._trade_history_worker is not None:
            try:
                # Ensure the thread has actually finished
                if self._trade_history_worker.isRunning():
                    print("Performance Dashboard: Worker still running, waiting for completion...")
                    # Wait for the thread to finish naturally (up to 3 seconds)
                    if not self._trade_history_worker.wait(3000):
                        print("Performance Dashboard: Worker didn't finish in time, forcing quit...")
                        self._trade_history_worker.quit()
                        if not self._trade_history_worker.wait(2000):
                            print("Performance Dashboard: Worker didn't quit, terminating...")
                            self._trade_history_worker.terminate()
                            self._trade_history_worker.wait(1000)

                # Disconnect all signals to prevent further callbacks
                try:
                    self._trade_history_worker.fetched.disconnect()
                    self._trade_history_worker.error.disconnect()
                    self._trade_history_worker.finished.disconnect()
                    print("Performance Dashboard: Worker signals disconnected")
                except Exception as e:
                    print(f"Performance Dashboard: Error disconnecting signals: {e}")

                # Now it's safe to delete the worker
                self._trade_history_worker.deleteLater()
                self._trade_history_worker = None
                print("Performance Dashboard: Worker cleanup completed")

            except Exception as e:
                print(f"Performance Dashboard: Error during worker cleanup: {e}")
                # Force cleanup even if there was an error
                try:
                    self._trade_history_worker.deleteLater()
                except:
                    pass
                self._trade_history_worker = None

    def closeEvent(self, event):
        """Handle widget close event to clean up threads."""
        try:
            # Stop any running worker threads
            if hasattr(self, '_trade_history_worker') and self._trade_history_worker is not None:
                print("Performance Dashboard: Cleaning up worker thread on close...")

                # Disconnect signals first to prevent callbacks during cleanup
                try:
                    self._trade_history_worker.fetched.disconnect()
                    self._trade_history_worker.error.disconnect()
                    self._trade_history_worker.finished.disconnect()
                except Exception as e:
                    print(f"Performance Dashboard: Error disconnecting signals on close: {e}")

                if self._trade_history_worker.isRunning():
                    print("Performance Dashboard: Stopping trade history worker...")
                    # Try to quit gracefully first
                    self._trade_history_worker.quit()
                    if not self._trade_history_worker.wait(2000):  # Wait up to 2 seconds
                        print("Performance Dashboard: Force terminating worker...")
                        self._trade_history_worker.terminate()
                        self._trade_history_worker.wait(1000)  # Wait up to 1 second for termination

                # Clean up the worker
                try:
                    self._trade_history_worker.deleteLater()
                    print("Performance Dashboard: Worker deleted on close")
                except Exception as e:
                    print(f"Performance Dashboard: Error deleting worker on close: {e}")

                self._trade_history_worker = None

        except Exception as e:
            print(f"Performance Dashboard: Error during close cleanup: {e}")

        # Accept the close event
        try:
            if hasattr(event, 'accept') and event is not None:
                event.accept()
            else:
                # Handle case where event might be None
                super().closeEvent(event) if event else None
        except Exception as e:
            print(f"Performance Dashboard: Error accepting close event: {e}")

    def __del__(self):
        """Destructor to ensure proper cleanup."""
        try:
            if hasattr(self, '_trade_history_worker') and self._trade_history_worker is not None:
                if self._trade_history_worker.isRunning():
                    self._trade_history_worker.terminate()
                    self._trade_history_worker.wait(1000)
        except:
            pass  # Ignore errors during destruction

    def _use_demo_data(self):
        """Use demo data when real data is not available."""
        try:
            # Generate some demo trades for display
            demo_trades = self._generate_demo_trades()
            self.trades = demo_trades
            self.metrics = self._calculate_metrics(demo_trades)
            self._generate_equity_curve()
            self._update_dashboard_ui()
        except Exception as e:
            print(f"Performance Dashboard: Error using demo data: {e}")

    def _generate_demo_trades(self):
        """Generate demo trades for testing."""
        demo_trades = []
        symbols = ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT"]

        for i in range(10):
            trade = {
                'timestamp': datetime.now() - timedelta(days=i),
                'symbol': symbols[i % len(symbols)],
                'direction': 'long' if i % 2 == 0 else 'short',
                'entry_price': 50000 + i * 100,
                'exit_price': 50000 + i * 100 + (50 if i % 2 == 0 else -50),
                'size': 0.1,
                'pnl': 50 if i % 2 == 0 else -50,
                'pnl_percent': 1.0 if i % 2 == 0 else -1.0,
                'fee': 5.0,
                'timeframe': '1h'
            }
            demo_trades.append(trade)

        return demo_trades

    def _process_trade_history(self, trades):
        """Process trade history data from the worker"""
        if trades:
            self.trades = trades
            print(f"Loaded {len(trades)} trades from exchange")

            # Generate a simple equity curve based on trade timestamps
            self._generate_equity_curve()

        # Update the UI with the new data
        self._update_dashboard_ui()

    def _handle_trade_history_error(self, error_msg):
        """Handle errors from the trade history worker"""
        print(f"Trade history worker error: {error_msg}")
        # Update the UI with existing data
        self._update_dashboard_ui()

    def _update_dashboard_ui(self):
        """Update all UI components with current data"""
        # Update the UI with current data
        self._update_metrics_panel()
        self._update_equity_curve()
        self._update_trades_table()
        self._update_analysis_tables()

        # Update the symbol filter dropdown with available symbols
        self._update_symbol_filter()

    def _generate_equity_curve(self):
        """Generate an equity curve from trades."""
        if not self.trades:
            return

        # Sort trades by timestamp
        sorted_trades = sorted(self.trades, key=lambda x: x.get('timestamp', datetime.now()))

        # Generate equity curve
        equity_curve = []
        current_equity = 10000  # Starting equity

        for trade in sorted_trades:
            pnl = trade.get('pnl', 0)
            timestamp = trade.get('timestamp', datetime.now())

            current_equity += pnl

            equity_point = {
                'timestamp': timestamp,
                'equity': current_equity
            }

            equity_curve.append(equity_point)

        self.equity_curve = equity_curve

    def _update_symbol_filter(self):
        """Update the symbol filter dropdown with available symbols."""
        if not self.trades:
            return

        # Get current selection
        current_selection = self.symbol_filter.currentText()

        # Get unique symbols
        symbols = sorted(set(trade.get('symbol', '') for trade in self.trades if trade.get('symbol')))

        # Clear and repopulate
        self.symbol_filter.clear()
        self.symbol_filter.addItem("All Symbols")
        self.symbol_filter.addItems(symbols)

        # Restore selection if possible
        index = self.symbol_filter.findText(current_selection)
        if index >= 0:
            self.symbol_filter.setCurrentIndex(index)

    def load_trades_from_file(self, file_path):
        """Load trades from a JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            if isinstance(data, list):
                self.trades = data
            elif isinstance(data, dict) and 'trades' in data:
                self.trades = data['trades']
            else:
                logger.error(f"Invalid trade data format in {file_path}")
                return False

            # Refresh the dashboard
            self.refresh_dashboard()
            return True

        except Exception as e:
            logger.error(f"Error loading trades from {file_path}: {e}")
            return False

    def save_trades_to_file(self, file_path):
        """Save trades to a JSON file."""
        try:
            with open(file_path, 'w') as f:
                json.dump(self.trades, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving trades to {file_path}: {e}")
            return False

    def add_trade(self, trade):
        """Add a new trade to the dashboard."""
        self.trades.append(trade)
        self.refresh_dashboard()

    def clear_trades(self):
        """Clear all trades from the dashboard."""
        self.trades = []
        self.equity_curve = []
        self.refresh_dashboard()
