---
title: Watermark
sidebar_label: Watermark
description: Examples of how to add a watermark to your chart.
pagination_prev: null
pagination_next: null
keywords:
  - watermark
  - example
---

Lightweight Charts™ has a built-in feature for displaying simple text watermarks on your chart.
This example shows how to configure and add this simple text watermark to your chart.
If you are looking to add a more complex watermark then have a look at the [image watermark example](#image-watermark-example)
included below.

## Short answer

A simple text watermark can be configured and added by using the [`createTextWatermark`](/docs/next/api/functions/createTextWatermark) function exported
from the library as follows:

```js
import { createTextWatermark } from 'lightweight-charts';

const firstPane = chart.panes()[0];
const textWatermark = createTextWatermark(firstPane, {
    horzAlign: 'center',
    vertAlign: 'center',
    lines: [
        {
            text: 'Watermark Example',
            color: 'rgba(171, 71, 188, 0.5)',
            fontSize: 24,
        },
    ],
});

```

The options available for the watermark are: [TextWatermark Options](/docs/next/api/interfaces/TextWatermarkOptions).

You can see full [working examples](#examples) below.

## Resources

- [`createTextWatermark` function](/docs/next/api/functions/createTextWatermark).
- [TextWatermark Options](/docs/next/api/interfaces/TextWatermarkOptions)

## Examples

import UsageGuidePartial from "../_usage-guide-partial.mdx";

<UsageGuidePartial />

import CodeBlock from "@theme/CodeBlock";

### Simple Watermark Example

import codeSimple from "!!raw-loader!./watermark-simple.js";

<CodeBlock replaceThemeConstants chart className="language-js" hideableCode chartOnTop>
	{codeSimple}
</CodeBlock>

### Image Watermark Example

If a simple text watermark doesn't meet your requirements then you can use the Image watermark via [`createImageWatermark`](/docs/next/api/functions/createImageWatermark) function exported
from the library as follows:

```js
import { createImageWatermark } from 'lightweight-charts';

const firstPane = chart.panes()[0];
const imageWatermark = createImageWatermark(firstPane, '/images/my-image.png', {
    alpha: 0.5,
    padding: 20,
});
```

The options available for the watermark are: [ImageWatermark Options](/docs/next/api/interfaces/ImageWatermarkOptions).

You can see full [working examples](#examples) below.

## Resources

- [`createImageWatermark` pane primitive](/docs/next/api/functions/createTextWatermark).
- [ImageWatermark Options](/docs/next/api/interfaces/ImageWatermarkOptions)

:::tip

Since the watermark image is black content with a transparent background, it may not be visible when
viewing the documentation site in dark mode.

:::

import codeAdvanced from "!!raw-loader!./watermark-advanced.js";

<CodeBlock replaceThemeConstants chart className="language-js" hideableCode chartOnTop>
	{codeAdvanced}
</CodeBlock>
