// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
	docsSidebar: [
		'intro',
		'series-types',
		'chart-types',
		'price-scale',
		'time-scale',
		'panes',
		'time-zones',
		{
			Plugins: [
				{
					type: 'autogenerated',
					dirName: 'plugins',
				},
			],
		},
		{
			Migrations: [
				{
					type: 'autogenerated',
					dirName: 'migrations',
				},
			],
		},
		{
			type: 'doc',
			id: 'ios',
			label: 'iOS',
		},
		{
			type: 'doc',
			id: 'android',
			label: 'Android',
		},
		'release-notes',
	],
	typedocSidebar: [
		{
			type: 'doc',
			id: 'api/index',
			label: 'Exports',
		},
		...require('./docs/api/typedoc-sidebar.cjs'),
	],
};

module.exports = sidebars;
