#!/usr/bin/env python3
"""
Crash-resistant launcher for ME2 Trading Application.
This script will help identify and fix crash causes.
"""

import sys
import os
import traceback
import subprocess
import importlib.util

def check_and_install_dependencies():
    """Check and install required dependencies."""
    dependencies = [
        ("PySide6", "PySide6"),
        ("ccxt", "ccxt"),
        ("pyqtgraph", "pyqtgraph"),
        ("yaml", "pyyaml"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("websocket", "websocket-client"),
        ("requests", "requests")
    ]
    
    print("🔍 Checking dependencies...")
    missing = []
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError:
            print(f"❌ {module_name} (missing)")
            missing.append(package_name)
    
    if missing:
        print(f"\n📦 Installing missing dependencies: {', '.join(missing)}")
        for package in missing:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} installed")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {package}: {e}")
                return False
    
    return True

def check_file_structure():
    """Check if required files exist."""
    required_files = [
        "me2_stable.py",
        "workers.py",
        "chart_tab.py",
        "performance_dashboard.py",
        "checkbox_functions.py"
    ]
    
    print("\n📁 Checking file structure...")
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} (missing)")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {', '.join(missing_files)}")
        return False
    
    return True

def check_credentials():
    """Check credentials file."""
    print("\n🔐 Checking credentials...")
    
    if os.path.exists("credentials.yaml"):
        print("✅ credentials.yaml found")
        try:
            import yaml
            with open("credentials.yaml", 'r') as f:
                creds = yaml.safe_load(f)
            
            if 'accounts' in creds and len(creds['accounts']) > 0:
                account = creds['accounts'][0]
                if 'api_key' in account and 'secret_key' in account:
                    print("✅ API credentials found")
                    return True
                else:
                    print("⚠️ API credentials incomplete")
            else:
                print("⚠️ No accounts found in credentials")
        except Exception as e:
            print(f"⚠️ Error reading credentials: {e}")
    else:
        print("⚠️ credentials.yaml not found")
    
    return False

def run_basic_import_test():
    """Test basic imports that might cause crashes."""
    print("\n🧪 Testing critical imports...")
    
    try:
        print("Testing PySide6...")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ PySide6 imports OK")
    except Exception as e:
        print(f"❌ PySide6 import failed: {e}")
        return False
    
    try:
        print("Testing pyqtgraph...")
        import pyqtgraph as pg
        print("✅ pyqtgraph import OK")
    except Exception as e:
        print(f"❌ pyqtgraph import failed: {e}")
        return False
    
    try:
        print("Testing ccxt...")
        import ccxt
        print("✅ ccxt import OK")
    except Exception as e:
        print(f"❌ ccxt import failed: {e}")
        return False
    
    return True

def run_minimal_app_test():
    """Test minimal QApplication creation."""
    print("\n🚀 Testing minimal QApplication...")
    
    try:
        from PySide6.QtWidgets import QApplication, QLabel
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test creating a simple widget
        label = QLabel("Test")
        print("✅ QApplication and widget creation OK")
        
        # Clean up
        label.deleteLater()
        return True
        
    except Exception as e:
        print(f"❌ QApplication test failed: {e}")
        traceback.print_exc()
        return False

def run_exchange_test():
    """Test exchange initialization."""
    print("\n💱 Testing exchange initialization...")
    
    try:
        import ccxt
        
        # Test basic exchange creation
        exchange = ccxt.huobi({
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
            }
        })
        print("✅ Exchange creation OK")
        
        # Test market loading
        try:
            exchange.load_markets()
            print("✅ Market loading OK")
        except Exception as e:
            print(f"⚠️ Market loading failed (this is OK in demo mode): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exchange test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main crash diagnosis function."""
    print("=" * 60)
    print("ME2 Trading Application - Crash Diagnosis")
    print("=" * 60)
    
    # Step 1: Check dependencies
    if not check_and_install_dependencies():
        print("\n❌ Dependency check failed")
        input("Press Enter to exit...")
        return 1
    
    # Step 2: Check file structure
    if not check_file_structure():
        print("\n❌ File structure check failed")
        input("Press Enter to exit...")
        return 1
    
    # Step 3: Check credentials
    has_credentials = check_credentials()
    
    # Step 4: Test basic imports
    if not run_basic_import_test():
        print("\n❌ Import test failed")
        input("Press Enter to exit...")
        return 1
    
    # Step 5: Test minimal app
    if not run_minimal_app_test():
        print("\n❌ Minimal app test failed")
        input("Press Enter to exit...")
        return 1
    
    # Step 6: Test exchange
    if not run_exchange_test():
        print("\n❌ Exchange test failed")
        input("Press Enter to exit...")
        return 1
    
    # All tests passed, try to run the main application
    print("\n" + "=" * 60)
    print("🎉 All diagnostic tests passed!")
    print("🚀 Attempting to launch ME2 Trading Application...")
    print("=" * 60)
    
    try:
        # Change to script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Try to import and run the main application
        import me2_stable
        return 0
        
    except Exception as e:
        print(f"\n❌ Application launch failed: {e}")
        print("\nFull error traceback:")
        traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("🔧 CRASH ANALYSIS")
        print("=" * 60)
        
        error_str = str(e).lower()
        
        if "pyside6" in error_str or "qt" in error_str:
            print("🔍 This appears to be a PySide6/Qt related error.")
            print("💡 Try: pip install --upgrade PySide6")
            
        elif "ccxt" in error_str:
            print("🔍 This appears to be a CCXT related error.")
            print("💡 Try: pip install --upgrade ccxt")
            
        elif "pyqtgraph" in error_str:
            print("🔍 This appears to be a PyQtGraph related error.")
            print("💡 Try: pip install --upgrade pyqtgraph")
            
        elif "credentials" in error_str or "yaml" in error_str:
            print("🔍 This appears to be a credentials/YAML related error.")
            print("💡 Check your credentials.yaml file format")
            
        elif "opengl" in error_str or "gpu" in error_str:
            print("🔍 This appears to be a graphics/OpenGL related error.")
            print("💡 The application will try to use CPU mode instead")
            
        else:
            print("🔍 This appears to be an unknown error.")
            print("💡 Please check the full traceback above for more details")
        
        input("\nPress Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
