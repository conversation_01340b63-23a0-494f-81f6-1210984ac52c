/*!
 * @license
 * TradingView Lightweight Charts™ v5.0.6
 * Copyright (c) 2025 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */
!function(){"use strict";const t={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};var i,s;function n(t,i){const s={0:[],1:[t.lineWidth,t.lineWidth],2:[2*t.lineWidth,2*t.lineWidth],3:[6*t.lineWidth,6*t.lineWidth],4:[t.lineWidth,4*t.lineWidth]}[i];t.setLineDash(s)}function e(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(s,i+e),t.lineTo(n,i+e),t.stroke()}function r(t,i){if(!t)throw new Error("Assertion failed"+(i?": "+i:""))}function h(t){if(void 0===t)throw new Error("Value is undefined");return t}function a(t){if(null===t)throw new Error("Value is null");return t}function l(t){return a(h(t))}!function(t){t[t.Simple=0]="Simple",t[t.WithSteps=1]="WithSteps",t[t.Curved=2]="Curved"}(i||(i={})),function(t){t[t.Solid=0]="Solid",t[t.Dotted=1]="Dotted",t[t.Dashed=2]="Dashed",t[t.LargeDashed=3]="LargeDashed",t[t.SparseDotted=4]="SparseDotted"}(s||(s={}));class o{constructor(){this.t=[]}i(t,i,s){const n={h:t,l:i,o:!0===s};this.t.push(n)}_(t){const i=this.t.findIndex((i=>t===i.h));i>-1&&this.t.splice(i,1)}u(t){this.t=this.t.filter((i=>i.l!==t))}p(t,i,s){const n=[...this.t];this.t=this.t.filter((t=>!t.o)),n.forEach((n=>n.h(t,i,s)))}v(){return this.t.length>0}m(){this.t=[]}}function _(t,...i){for(const s of i)for(const i in s)void 0!==s[i]&&Object.prototype.hasOwnProperty.call(s,i)&&!["__proto__","constructor","prototype"].includes(i)&&("object"!=typeof s[i]||void 0===t[i]||Array.isArray(s[i])?t[i]=s[i]:_(t[i],s[i]));return t}function u(t){return"number"==typeof t&&isFinite(t)}function c(t){return"number"==typeof t&&t%1==0}function d(t){return"string"==typeof t}function f(t){return"boolean"==typeof t}function p(t){const i=t;if(!i||"object"!=typeof i)return i;let s,n,e;for(n in s=Array.isArray(i)?[]:{},i)i.hasOwnProperty(n)&&(e=i[n],s[n]=e&&"object"==typeof e?p(e):e);return s}function v(t){return null!==t}function m(t){return null===t?void 0:t}const w="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function g(t,i,s){return void 0===i&&(i=w),`${s=void 0!==s?`${s} `:""}${t}px ${i}`}class M{constructor(t){this.M={S:1,C:5,P:NaN,k:"",T:"",R:"",D:"",B:0,I:0,V:0,A:0,O:0},this.L=t}N(){const t=this.M,i=this.W(),s=this.F();return t.P===i&&t.T===s||(t.P=i,t.T=s,t.k=g(i,s),t.A=2.5/12*i,t.B=t.A,t.I=i/12*t.C,t.V=i/12*t.C,t.O=0),t.R=this.H(),t.D=this.U(),this.M}H(){return this.L.N().layout.textColor}U(){return this.L.$()}W(){return this.L.N().layout.fontSize}F(){return this.L.N().layout.fontFamily}}function b(t){return t<0?0:t>255?255:Math.round(t)||0}function x(t){return.199*t[0]+.687*t[1]+.114*t[2]}class S{constructor(t,i){this.j=new Map,this.q=t,i&&(this.j=i)}Y(t,i){if("transparent"===t)return t;const s=this.K(t),n=s[3];return`rgba(${s[0]}, ${s[1]}, ${s[2]}, ${i*n})`}X(t){const i=this.K(t);return{Z:`rgb(${i[0]}, ${i[1]}, ${i[2]})`,G:x(i)>160?"black":"white"}}J(t){return x(this.K(t))}tt(t,i,s){const[n,e,r,h]=this.K(t),[a,l,o,_]=this.K(i),u=[b(n+s*(a-n)),b(e+s*(l-e)),b(r+s*(o-r)),(c=h+s*(_-h),c<=0||c>1?Math.min(Math.max(c,0),1):Math.round(1e4*c)/1e4)];var c;return`rgba(${u[0]}, ${u[1]}, ${u[2]}, ${u[3]})`}K(t){const i=this.j.get(t);if(i)return i;const s=function(t){const i=document.createElement("div");i.style.display="none",document.body.appendChild(i),i.style.color=t;const s=window.getComputedStyle(i).color;return document.body.removeChild(i),s}(t),n=s.match(/^rgba?\s*\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/);if(!n){if(this.q.length)for(const i of this.q){const s=i(t);if(s)return this.j.set(t,s),s}throw new Error(`Failed to parse color: ${t}`)}const e=[parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3],10),n[4]?parseFloat(n[4]):1];return this.j.set(t,e),e}}class C{constructor(){this.it=[]}st(t){this.it=t}nt(t,i,s){this.it.forEach((n=>{n.nt(t,i,s)}))}}class y{nt(t,i,s){t.useBitmapCoordinateSpace((t=>this.et(t,i,s)))}}class P extends y{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.rt||null===this.rt.lt)return;const n=this.rt.lt,e=this.rt,r=Math.max(1,Math.floor(i))%2/2,h=h=>{t.beginPath();for(let a=n.to-1;a>=n.from;--a){const n=e.ot[a],l=Math.round(n._t*i)+r,o=n.ut*s,_=h*s+r;t.moveTo(l,o),t.arc(l,o,_,0,2*Math.PI)}t.fill()};e.ct>0&&(t.fillStyle=e.dt,h(e.ft+e.ct)),t.fillStyle=e.vt,h(e.ft)}}function k(){return{ot:[{_t:0,ut:0,wt:0,gt:0}],vt:"",dt:"",ft:0,ct:0,lt:null}}const T={from:0,to:1};class R{constructor(t,i,s){this.Mt=new C,this.bt=[],this.xt=[],this.St=!0,this.L=t,this.Ct=i,this.yt=s,this.Mt.st(this.bt)}Pt(t){this.kt(),this.St=!0}Tt(){return this.St&&(this.Rt(),this.St=!1),this.Mt}kt(){const t=this.yt.Dt();t.length!==this.bt.length&&(this.xt=t.map(k),this.bt=this.xt.map((t=>{const i=new P;return i.ht(t),i})),this.Mt.st(this.bt))}Rt(){const t=2===this.Ct.N().mode||!this.Ct.Bt(),i=this.yt.Et(),s=this.Ct.It(),n=this.L.Vt();this.kt(),i.forEach(((i,e)=>{const r=this.xt[e],h=i.At(s),a=i.zt();!t&&null!==h&&i.Bt()&&null!==a?(r.vt=h.Ot,r.ft=h.ft,r.ct=h.Lt,r.ot[0].gt=h.gt,r.ot[0].ut=i.Wt().Nt(h.gt,a.Ft),r.dt=h.Ht??this.L.Ut(r.ot[0].ut/i.Wt().$t()),r.ot[0].wt=s,r.ot[0]._t=n.jt(s),r.lt=T):r.lt=null}))}}class D extends y{constructor(t){super(),this.qt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:r}){if(null===this.qt)return;const h=this.qt.Yt.Bt,a=this.qt.Kt.Bt;if(!h&&!a)return;const l=Math.round(this.qt._t*s),o=Math.round(this.qt.ut*r);t.lineCap="butt",h&&l>=0&&(t.lineWidth=Math.floor(this.qt.Yt.ct*s),t.strokeStyle=this.qt.Yt.R,t.fillStyle=this.qt.Yt.R,n(t,this.qt.Yt.Xt),function(t,i,s,n){t.beginPath();const e=t.lineWidth%2?.5:0;t.moveTo(i+e,s),t.lineTo(i+e,n),t.stroke()}(t,l,0,i.height)),a&&o>=0&&(t.lineWidth=Math.floor(this.qt.Kt.ct*r),t.strokeStyle=this.qt.Kt.R,t.fillStyle=this.qt.Kt.R,n(t,this.qt.Kt.Xt),e(t,o,0,i.width))}}class B{constructor(t,i){this.St=!0,this.Zt={Yt:{ct:1,Xt:0,R:"",Bt:!1},Kt:{ct:1,Xt:0,R:"",Bt:!1},_t:0,ut:0},this.Gt=new D(this.Zt),this.Jt=t,this.yt=i}Pt(){this.St=!0}Tt(t){return this.St&&(this.Rt(),this.St=!1),this.Gt}Rt(){const t=this.Jt.Bt(),i=this.yt.Qt().N().crosshair,s=this.Zt;if(2===i.mode)return s.Kt.Bt=!1,void(s.Yt.Bt=!1);s.Kt.Bt=t&&this.Jt.ti(this.yt),s.Yt.Bt=t&&this.Jt.ii(),s.Kt.ct=i.horzLine.width,s.Kt.Xt=i.horzLine.style,s.Kt.R=i.horzLine.color,s.Yt.ct=i.vertLine.width,s.Yt.Xt=i.vertLine.style,s.Yt.R=i.vertLine.color,s._t=this.Jt.si(),s.ut=this.Jt.ni()}}function E(t,i,s,n,e,r){t.fillRect(i+r,s,n-2*r,r),t.fillRect(i+r,s+e-r,n-2*r,r),t.fillRect(i,s,r,e),t.fillRect(i+n-r,s,r,e)}function I(t,i,s,n,e,r){t.save(),t.globalCompositeOperation="copy",t.fillStyle=r,t.fillRect(i,s,n,e),t.restore()}function V(t,i,s,n,e,r){t.beginPath(),t.roundRect?t.roundRect(i,s,n,e,r):(t.lineTo(i+n-r[1],s),0!==r[1]&&t.arcTo(i+n,s,i+n,s+r[1],r[1]),t.lineTo(i+n,s+e-r[2]),0!==r[2]&&t.arcTo(i+n,s+e,i+n-r[2],s+e,r[2]),t.lineTo(i+r[3],s+e),0!==r[3]&&t.arcTo(i,s+e,i,s+e-r[3],r[3]),t.lineTo(i,s+r[0]),0!==r[0]&&t.arcTo(i,s,i+r[0],s,r[0]))}function A(t,i,s,n,e,r,h=0,a=[0,0,0,0],l=""){if(t.save(),!h||!l||l===r)return V(t,i,s,n,e,a),t.fillStyle=r,t.fill(),void t.restore();const o=h/2;var _;V(t,i+o,s+o,n-h,e-h,(_=-o,a.map((t=>0===t?t:t+_)))),"transparent"!==r&&(t.fillStyle=r,t.fill()),"transparent"!==l&&(t.lineWidth=h,t.strokeStyle=l,t.closePath(),t.stroke()),t.restore()}function z(t,i,s,n,e,r,h){t.save(),t.globalCompositeOperation="copy";const a=t.createLinearGradient(0,0,0,e);a.addColorStop(0,r),a.addColorStop(1,h),t.fillStyle=a,t.fillRect(i,s,n,e),t.restore()}class O{constructor(t,i){this.ht(t,i)}ht(t,i){this.qt=t,this.ei=i}$t(t,i){return this.qt.Bt?t.P+t.A+t.B:0}nt(t,i,s,n){if(!this.qt.Bt||0===this.qt.ri.length)return;const e=this.qt.R,r=this.ei.Z,h=t.useBitmapCoordinateSpace((t=>{const h=t.context;h.font=i.k;const a=this.hi(t,i,s,n),l=a.ai;return a.li?A(h,l.oi,l._i,l.ui,l.ci,r,l.di,[l.ft,0,0,l.ft],r):A(h,l.fi,l._i,l.ui,l.ci,r,l.di,[0,l.ft,l.ft,0],r),this.qt.pi&&(h.fillStyle=e,h.fillRect(l.fi,l.mi,l.wi-l.fi,l.gi)),this.qt.Mi&&(h.fillStyle=i.D,h.fillRect(a.li?l.bi-l.di:0,l._i,l.di,l.xi-l._i)),a}));t.useMediaCoordinateSpace((({context:t})=>{const s=h.Si;t.font=i.k,t.textAlign=h.li?"right":"left",t.textBaseline="middle",t.fillStyle=e,t.fillText(this.qt.ri,s.Ci,(s._i+s.xi)/2+s.yi)}))}hi(t,i,s,n){const{context:e,bitmapSize:r,mediaSize:h,horizontalPixelRatio:a,verticalPixelRatio:l}=t,o=this.qt.pi||!this.qt.Pi?i.C:0,_=this.qt.ki?i.S:0,u=i.A+this.ei.Ti,c=i.B+this.ei.Ri,d=i.I,f=i.V,p=this.qt.ri,v=i.P,m=s.Di(e,p),w=Math.ceil(s.Bi(e,p)),g=v+u+c,M=i.S+d+f+w+o,b=Math.max(1,Math.floor(l));let x=Math.round(g*l);x%2!=b%2&&(x+=1);const S=_>0?Math.max(1,Math.floor(_*a)):0,C=Math.round(M*a),y=Math.round(o*a),P=this.ei.Ei??this.ei.Ii,k=Math.round(P*l)-Math.floor(.5*l),T=Math.floor(k+b/2-x/2),R=T+x,D="right"===n,B=D?h.width-_:_,E=D?r.width-S:S;let I,V,A;return D?(I=E-C,V=E-y,A=B-o-d-_):(I=E+C,V=E+y,A=B+o+d),{li:D,ai:{_i:T,mi:k,xi:R,ui:C,ci:x,ft:2*a,di:S,oi:I,fi:E,wi:V,gi:b,bi:r.width},Si:{_i:T/l,xi:R/l,Ci:A,yi:m}}}}class L{constructor(t){this.Vi={Ii:0,Z:"#000",Ri:0,Ti:0},this.Ai={ri:"",Bt:!1,pi:!0,Pi:!1,Ht:"",R:"#FFF",Mi:!1,ki:!1},this.zi={ri:"",Bt:!1,pi:!1,Pi:!0,Ht:"",R:"#FFF",Mi:!0,ki:!0},this.St=!0,this.Oi=new(t||O)(this.Ai,this.Vi),this.Li=new(t||O)(this.zi,this.Vi)}ri(){return this.Ni(),this.Ai.ri}Ii(){return this.Ni(),this.Vi.Ii}Pt(){this.St=!0}$t(t,i=!1){return Math.max(this.Oi.$t(t,i),this.Li.$t(t,i))}Wi(){return this.Vi.Ei||0}Fi(t){this.Vi.Ei=t}Hi(){return this.Ni(),this.Ai.Bt||this.zi.Bt}Ui(){return this.Ni(),this.Ai.Bt}Tt(t){return this.Ni(),this.Ai.pi=this.Ai.pi&&t.N().ticksVisible,this.zi.pi=this.zi.pi&&t.N().ticksVisible,this.Oi.ht(this.Ai,this.Vi),this.Li.ht(this.zi,this.Vi),this.Oi}$i(){return this.Ni(),this.Oi.ht(this.Ai,this.Vi),this.Li.ht(this.zi,this.Vi),this.Li}Ni(){this.St&&(this.Ai.pi=!0,this.zi.pi=!1,this.ji(this.Ai,this.zi,this.Vi))}}class N extends L{constructor(t,i,s){super(),this.Jt=t,this.qi=i,this.Yi=s}ji(t,i,s){if(t.Bt=!1,2===this.Jt.N().mode)return;const n=this.Jt.N().horzLine;if(!n.labelVisible)return;const e=this.qi.zt();if(!this.Jt.Bt()||this.qi.Ki()||null===e)return;const r=this.qi.Xi().X(n.labelBackgroundColor);s.Z=r.Z,t.R=r.G;const h=2/12*this.qi.P();s.Ti=h,s.Ri=h;const a=this.Yi(this.qi);s.Ii=a.Ii,t.ri=this.qi.Zi(a.gt,e),t.Bt=!0}}const W=/[1-9]/g;class F{constructor(){this.qt=null}ht(t){this.qt=t}nt(t,i){if(null===this.qt||!1===this.qt.Bt||0===this.qt.ri.length)return;const s=t.useMediaCoordinateSpace((({context:t})=>(t.font=i.k,Math.round(i.Gi.Bi(t,a(this.qt).ri,W)))));if(s<=0)return;const n=i.Ji,e=s+2*n,r=e/2,h=this.qt.Qi;let l=this.qt.Ii,o=Math.floor(l-r)+.5;o<0?(l+=Math.abs(0-o),o=Math.floor(l-r)+.5):o+e>h&&(l-=Math.abs(h-(o+e)),o=Math.floor(l-r)+.5);const _=o+e,u=Math.ceil(0+i.S+i.C+i.A+i.P+i.B);t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:s,verticalPixelRatio:n})=>{const e=a(this.qt);t.fillStyle=e.Z;const r=Math.round(o*s),h=Math.round(0*n),l=Math.round(_*s),c=Math.round(u*n),d=Math.round(2*s);if(t.beginPath(),t.moveTo(r,h),t.lineTo(r,c-d),t.arcTo(r,c,r+d,c,d),t.lineTo(l-d,c),t.arcTo(l,c,l,c-d,d),t.lineTo(l,h),t.fill(),e.pi){const r=Math.round(e.Ii*s),a=h,l=Math.round((a+i.C)*n);t.fillStyle=e.R;const o=Math.max(1,Math.floor(s)),_=Math.floor(.5*s);t.fillRect(r-_,a,o,l-a)}})),t.useMediaCoordinateSpace((({context:t})=>{const s=a(this.qt),e=0+i.S+i.C+i.A+i.P/2;t.font=i.k,t.textAlign="left",t.textBaseline="middle",t.fillStyle=s.R;const r=i.Gi.Di(t,"Apr0");t.translate(o+n,e+r),t.fillText(s.ri,0,0)}))}}class H{constructor(t,i,s){this.St=!0,this.Gt=new F,this.Zt={Bt:!1,Z:"#4c525e",R:"white",ri:"",Qi:0,Ii:NaN,pi:!0},this.Ct=t,this.ts=i,this.Yi=s}Pt(){this.St=!0}Tt(){return this.St&&(this.Rt(),this.St=!1),this.Gt.ht(this.Zt),this.Gt}Rt(){const t=this.Zt;if(t.Bt=!1,2===this.Ct.N().mode)return;const i=this.Ct.N().vertLine;if(!i.labelVisible)return;const s=this.ts.Vt();if(s.Ki())return;t.Qi=s.Qi();const n=this.Yi();if(null===n)return;t.Ii=n.Ii;const e=s.ss(this.Ct.It());t.ri=s.ns(a(e)),t.Bt=!0;const r=this.ts.Xi().X(i.labelBackgroundColor);t.Z=r.Z,t.R=r.G,t.pi=s.N().ticksVisible}}class U{constructor(){this.es=null,this.rs=0}hs(){return this.rs}ls(t){this.rs=t}Wt(){return this.es}_s(t){this.es=t}us(t){return[]}cs(){return[]}Bt(){return!0}}var $;!function(t){t[t.Normal=0]="Normal",t[t.Magnet=1]="Magnet",t[t.Hidden=2]="Hidden",t[t.MagnetOHLC=3]="MagnetOHLC"}($||($={}));class j extends U{constructor(t,i){super(),this.yt=null,this.ds=NaN,this.fs=0,this.ps=!1,this.vs=new Map,this.ws=!1,this.gs=new WeakMap,this.Ms=new WeakMap,this.bs=NaN,this.xs=NaN,this.Ss=NaN,this.Cs=NaN,this.ts=t,this.ys=i;this.Ps=((t,i)=>s=>{const n=i(),e=t();if(s===a(this.yt).ks())return{gt:e,Ii:n};{const t=a(s.zt());return{gt:s.Ts(n,t),Ii:n}}})((()=>this.ds),(()=>this.xs));const s=((t,i)=>()=>{const s=this.ts.Vt().Rs(t()),n=i();return s&&Number.isFinite(n)?{wt:s,Ii:n}:null})((()=>this.fs),(()=>this.si()));this.Ds=new H(this,t,s)}N(){return this.ys}Bs(t,i){this.Ss=t,this.Cs=i}Es(){this.Ss=NaN,this.Cs=NaN}Is(){return this.Ss}Vs(){return this.Cs}As(t,i,s){this.ws||(this.ws=!0),this.ps=!0,this.zs(t,i,s)}It(){return this.fs}si(){return this.bs}ni(){return this.xs}Bt(){return this.ps}Os(){this.ps=!1,this.Ls(),this.ds=NaN,this.bs=NaN,this.xs=NaN,this.yt=null,this.Es(),this.Ns()}Ws(t){let i=this.gs.get(t);i||(i=new B(this,t),this.gs.set(t,i));let s=this.Ms.get(t);return s||(s=new R(this.ts,this,t),this.Ms.set(t,s)),[i,s]}ti(t){return t===this.yt&&this.ys.horzLine.visible}ii(){return this.ys.vertLine.visible}Fs(t,i){this.ps&&this.yt===t||this.vs.clear();const s=[];return this.yt===t&&s.push(this.Hs(this.vs,i,this.Ps)),s}cs(){return this.ps?[this.Ds]:[]}Us(){return this.yt}Ns(){this.ts.$s().forEach((t=>{this.gs.get(t)?.Pt(),this.Ms.get(t)?.Pt()})),this.vs.forEach((t=>t.Pt())),this.Ds.Pt()}js(t){return t&&!t.ks().Ki()?t.ks():null}zs(t,i,s){this.qs(t,i,s)&&this.Ns()}qs(t,i,s){const n=this.bs,e=this.xs,r=this.ds,h=this.fs,a=this.yt,l=this.js(s);this.fs=t,this.bs=isNaN(t)?NaN:this.ts.Vt().jt(t),this.yt=s;const o=null!==l?l.zt():null;return null!==l&&null!==o?(this.ds=i,this.xs=l.Nt(i,o)):(this.ds=NaN,this.xs=NaN),n!==this.bs||e!==this.xs||h!==this.fs||r!==this.ds||a!==this.yt}Ls(){const t=this.ts.Ys().map((t=>t.Xs().Ks())).filter(v),i=0===t.length?null:Math.max(...t);this.fs=null!==i?i:NaN}Hs(t,i,s){let n=t.get(i);return void 0===n&&(n=new N(this,i,s),t.set(i,n)),n}}function q(t){return"left"===t||"right"===t}class Y{constructor(t){this.Zs=new Map,this.Gs=[],this.Js=t}Qs(t,i){const s=function(t,i){return void 0===t?i:{tn:Math.max(t.tn,i.tn),sn:t.sn||i.sn}}(this.Zs.get(t),i);this.Zs.set(t,s)}nn(){return this.Js}en(t){const i=this.Zs.get(t);return void 0===i?{tn:this.Js}:{tn:Math.max(this.Js,i.tn),sn:i.sn}}rn(){this.hn(),this.Gs=[{an:0}]}ln(t){this.hn(),this.Gs=[{an:1,Ft:t}]}_n(t){this.un(),this.Gs.push({an:5,Ft:t})}hn(){this.un(),this.Gs.push({an:6})}cn(){this.hn(),this.Gs=[{an:4}]}dn(t){this.hn(),this.Gs.push({an:2,Ft:t})}fn(t){this.hn(),this.Gs.push({an:3,Ft:t})}pn(){return this.Gs}vn(t){for(const i of t.Gs)this.mn(i);this.Js=Math.max(this.Js,t.Js),t.Zs.forEach(((t,i)=>{this.Qs(i,t)}))}static wn(){return new Y(2)}static gn(){return new Y(3)}mn(t){switch(t.an){case 0:this.rn();break;case 1:this.ln(t.Ft);break;case 2:this.dn(t.Ft);break;case 3:this.fn(t.Ft);break;case 4:this.cn();break;case 5:this._n(t.Ft);break;case 6:this.un()}}un(){const t=this.Gs.findIndex((t=>5===t.an));-1!==t&&this.Gs.splice(t,1)}}const K=".";function X(t,i){if(!u(t))return"n/a";if(!c(i))throw new TypeError("invalid length");if(i<0||i>16)throw new TypeError("invalid length");if(0===i)return t.toString();return("0000000000000000"+t.toString()).slice(-i)}class Z{constructor(t,i){if(i||(i=1),u(t)&&c(t)||(t=100),t<0)throw new TypeError("invalid base");this.qi=t,this.Mn=i,this.bn()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this.xn(t)}bn(){if(this.Sn=0,this.qi>0&&this.Mn>0){let t=this.qi;for(;t>1;)t/=10,this.Sn++}}xn(t){const i=this.qi/this.Mn;let s=Math.floor(t),n="";const e=void 0!==this.Sn?this.Sn:NaN;if(i>1){let r=+(Math.round(t*i)-s*i).toFixed(this.Sn);r>=i&&(r-=i,s+=1),n=K+X(+r.toFixed(this.Sn)*this.Mn,e)}else s=Math.round(s*i)/i,e>0&&(n=K+X(0,e));return s.toFixed(0)+n}}class G extends Z{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class J{constructor(t){this.Cn=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this.yn(t):t<999995?i+this.yn(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this.yn(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this.yn(t/1e9)+"B")}yn(t){let i;const s=Math.pow(10,this.Cn);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this.Cn).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,((t,i)=>i))}}const Q=/[2-9]/g;class tt{constructor(t=50){this.Pn=0,this.kn=1,this.Tn=1,this.Rn={},this.Dn=new Map,this.Bn=t}En(){this.Pn=0,this.Dn.clear(),this.kn=1,this.Tn=1,this.Rn={}}Bi(t,i,s){return this.In(t,i,s).width}Di(t,i,s){const n=this.In(t,i,s);return((n.actualBoundingBoxAscent||0)-(n.actualBoundingBoxDescent||0))/2}In(t,i,s){const n=s||Q,e=String(i).replace(n,"0");if(this.Dn.has(e))return h(this.Dn.get(e)).Vn;if(this.Pn===this.Bn){const t=this.Rn[this.Tn];delete this.Rn[this.Tn],this.Dn.delete(t),this.Tn++,this.Pn--}t.save(),t.textBaseline="middle";const r=t.measureText(e);return t.restore(),0===r.width&&i.length||(this.Dn.set(e,{Vn:r,An:this.kn}),this.Rn[this.kn]=e,this.Pn++,this.kn++),r}}class it{constructor(t){this.zn=null,this.M=null,this.On="right",this.Ln=t}Nn(t,i,s){this.zn=t,this.M=i,this.On=s}nt(t){null!==this.M&&null!==this.zn&&this.zn.nt(t,this.M,this.Ln,this.On)}}class st{constructor(t,i,s){this.Wn=t,this.Ln=new tt(50),this.Fn=i,this.L=s,this.W=-1,this.Gt=new it(this.Ln)}Tt(){const t=this.L.Hn(this.Fn);if(null===t)return null;const i=t.Un(this.Fn)?t.$n():this.Fn.Wt();if(null===i)return null;const s=t.jn(i);if("overlay"===s)return null;const n=this.L.qn();return n.P!==this.W&&(this.W=n.P,this.Ln.En()),this.Gt.Nn(this.Wn.$i(),n,s),this.Gt}}class nt extends y{constructor(){super(...arguments),this.qt=null}ht(t){this.qt=t}Yn(t,i){if(!this.qt?.Bt)return null;const{ut:s,ct:n,Kn:e}=this.qt;return i>=s-n-7&&i<=s+n+7?{Xn:this.qt,Kn:e}:null}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:r}){if(null===this.qt)return;if(!1===this.qt.Bt)return;const h=Math.round(this.qt.ut*r);h<0||h>i.height||(t.lineCap="butt",t.strokeStyle=this.qt.R,t.lineWidth=Math.floor(this.qt.ct*s),n(t,this.qt.Xt),e(t,h,0,i.width))}}class et{constructor(t){this.Zn={ut:0,R:"rgba(0, 0, 0, 0)",ct:1,Xt:0,Bt:!1},this.Gn=new nt,this.St=!0,this.Jn=t,this.Qn=t.Qt(),this.Gn.ht(this.Zn)}Pt(){this.St=!0}Tt(){return this.Jn.Bt()?(this.St&&(this.te(),this.St=!1),this.Gn):null}}class rt extends et{constructor(t){super(t)}te(){this.Zn.Bt=!1;const t=this.Jn.Wt(),i=t.ie().ie;if(2!==i&&3!==i)return;const s=this.Jn.N();if(!s.baseLineVisible||!this.Jn.Bt())return;const n=this.Jn.zt();null!==n&&(this.Zn.Bt=!0,this.Zn.ut=t.Nt(n.Ft,n.Ft),this.Zn.R=s.baseLineColor,this.Zn.ct=s.baseLineWidth,this.Zn.Xt=s.baseLineStyle)}}class ht extends y{constructor(){super(...arguments),this.qt=null}ht(t){this.qt=t}se(){return this.qt}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const n=this.qt;if(null===n)return;const e=Math.max(1,Math.floor(i)),r=e%2/2,h=Math.round(n.ne.x*i)+r,a=n.ne.y*s;t.fillStyle=n.ee,t.beginPath();const l=Math.max(2,1.5*n.re)*i;t.arc(h,a,l,0,2*Math.PI,!1),t.fill(),t.fillStyle=n.he,t.beginPath(),t.arc(h,a,n.ft*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=e,t.strokeStyle=n.ae,t.beginPath(),t.arc(h,a,n.ft*i+e/2,0,2*Math.PI,!1),t.stroke()}}const at=[{le:0,oe:.25,_e:4,ue:10,ce:.25,de:0,fe:.4,pe:.8},{le:.25,oe:.525,_e:10,ue:14,ce:0,de:0,fe:.8,pe:0},{le:.525,oe:1,_e:14,ue:14,ce:0,de:0,fe:0,pe:0}];class lt{constructor(t){this.Gt=new ht,this.St=!0,this.ve=!0,this.me=performance.now(),this.we=this.me-1,this.ge=t}Me(){this.we=this.me-1,this.Pt()}be(){if(this.Pt(),2===this.ge.N().lastPriceAnimation){const t=performance.now(),i=this.we-t;if(i>0)return void(i<650&&(this.we+=2600));this.me=t,this.we=t+2600}}Pt(){this.St=!0}xe(){this.ve=!0}Bt(){return 0!==this.ge.N().lastPriceAnimation}Se(){switch(this.ge.N().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.we}}Tt(){return this.St?(this.Rt(),this.St=!1,this.ve=!1):this.ve&&(this.Ce(),this.ve=!1),this.Gt}Rt(){this.Gt.ht(null);const t=this.ge.Qt().Vt(),i=t.ye(),s=this.ge.zt();if(null===i||null===s)return;const n=this.ge.Pe(!0);if(n.ke||!i.Te(n.Re))return;const e={x:t.jt(n.Re),y:this.ge.Wt().Nt(n.gt,s.Ft)},r=n.R,h=this.ge.N().lineWidth,a=this.De(this.Be(),r);this.Gt.ht({ee:r,re:h,he:a.he,ae:a.ae,ft:a.ft,ne:e})}Ce(){const t=this.Gt.se();if(null!==t){const i=this.De(this.Be(),t.ee);t.he=i.he,t.ae=i.ae,t.ft=i.ft}}Be(){return this.Se()?performance.now()-this.me:2599}Ee(t,i,s,n){const e=s+(n-s)*i;return this.ge.Qt().Xi().Y(t,e)}De(t,i){const s=t%2600/2600;let n;for(const t of at)if(s>=t.le&&s<=t.oe){n=t;break}r(void 0!==n,"Last price animation internal logic error");const e=(s-n.le)/(n.oe-n.le);return{he:this.Ee(i,e,n.ce,n.de),ae:this.Ee(i,e,n.fe,n.pe),ft:(h=e,a=n._e,l=n.ue,a+(l-a)*h)};var h,a,l}}class ot extends et{constructor(t){super(t)}te(){const t=this.Zn;t.Bt=!1;const i=this.Jn.N();if(!i.priceLineVisible||!this.Jn.Bt())return;const s=this.Jn.Pe(0===i.priceLineSource);s.ke||(t.Bt=!0,t.ut=s.Ii,t.R=this.Jn.Ie(s.R),t.ct=i.priceLineWidth,t.Xt=i.priceLineStyle)}}class _t extends L{constructor(t){super(),this.Jt=t}ji(t,i,s){t.Bt=!1,i.Bt=!1;const n=this.Jt;if(!n.Bt())return;const e=n.N(),r=e.lastValueVisible,h=""!==n.Ve(),a=0===e.seriesLastValueMode,l=n.Pe(!1);if(l.ke)return;r&&(t.ri=this.Ae(l,r,a),t.Bt=0!==t.ri.length),(h||a)&&(i.ri=this.ze(l,r,h,a),i.Bt=i.ri.length>0);const o=n.Ie(l.R),_=this.Jt.Qt().Xi().X(o);s.Z=_.Z,s.Ii=l.Ii,i.Ht=n.Qt().Ut(l.Ii/n.Wt().$t()),t.Ht=o,t.R=_.G,i.R=_.G}ze(t,i,s,n){let e="";const r=this.Jt.Ve();return s&&0!==r.length&&(e+=`${r} `),i&&n&&(e+=this.Jt.Wt().Oe()?t.Le:t.Ne),e.trim()}Ae(t,i,s){return i?s?this.Jt.Wt().Oe()?t.Ne:t.Le:t.ri:""}}function ut(t,i,s,n){const e=Number.isFinite(i),r=Number.isFinite(s);return e&&r?t(i,s):e||r?e?i:s:n}class ct{constructor(t,i){this.We=t,this.Fe=i}He(t){return null!==t&&(this.We===t.We&&this.Fe===t.Fe)}Ue(){return new ct(this.We,this.Fe)}$e(){return this.We}je(){return this.Fe}qe(){return this.Fe-this.We}Ki(){return this.Fe===this.We||Number.isNaN(this.Fe)||Number.isNaN(this.We)}vn(t){return null===t?this:new ct(ut(Math.min,this.$e(),t.$e(),-1/0),ut(Math.max,this.je(),t.je(),1/0))}Ye(t){if(!u(t))return;if(0===this.Fe-this.We)return;const i=.5*(this.Fe+this.We);let s=this.Fe-i,n=this.We-i;s*=t,n*=t,this.Fe=i+s,this.We=i+n}Ke(t){u(t)&&(this.Fe+=t,this.We+=t)}Xe(){return{minValue:this.We,maxValue:this.Fe}}static Ze(t){return null===t?null:new ct(t.minValue,t.maxValue)}}class dt{constructor(t,i){this.Ge=t,this.Je=i||null}Qe(){return this.Ge}tr(){return this.Je}Xe(){return{priceRange:null===this.Ge?null:this.Ge.Xe(),margins:this.Je||void 0}}static Ze(t){return null===t?null:new dt(ct.Ze(t.priceRange),t.margins)}}class ft extends et{constructor(t,i){super(t),this.ir=i}te(){const t=this.Zn;t.Bt=!1;const i=this.ir.N();if(!this.Jn.Bt()||!i.lineVisible)return;const s=this.ir.sr();null!==s&&(t.Bt=!0,t.ut=s,t.R=i.color,t.ct=i.lineWidth,t.Xt=i.lineStyle,t.Kn=this.ir.N().id)}}class pt extends L{constructor(t,i){super(),this.ge=t,this.ir=i}ji(t,i,s){t.Bt=!1,i.Bt=!1;const n=this.ir.N(),e=n.axisLabelVisible,r=""!==n.title,h=this.ge;if(!e||!h.Bt())return;const a=this.ir.sr();if(null===a)return;r&&(i.ri=n.title,i.Bt=!0),i.Ht=h.Qt().Ut(a/h.Wt().$t()),t.ri=this.nr(n.price),t.Bt=!0;const l=this.ge.Qt().Xi().X(n.axisLabelColor||n.color);s.Z=l.Z;const o=n.axisLabelTextColor||l.G;t.R=o,i.R=o,s.Ii=a}nr(t){const i=this.ge.zt();return null===i?"":this.ge.Wt().Zi(t,i.Ft)}}class vt{constructor(t,i){this.ge=t,this.ys=i,this.er=new ft(t,this),this.Wn=new pt(t,this),this.rr=new st(this.Wn,t,t.Qt())}hr(t){_(this.ys,t),this.Pt(),this.ge.Qt().ar()}N(){return this.ys}lr(){return this.er}_r(){return this.rr}ur(){return this.Wn}Pt(){this.er.Pt(),this.Wn.Pt()}sr(){const t=this.ge,i=t.Wt();if(t.Qt().Vt().Ki()||i.Ki())return null;const s=t.zt();return null===s?null:i.Nt(this.ys.price,s.Ft)}}class mt extends U{constructor(t){super(),this.ts=t}Qt(){return this.ts}}const wt={Bar:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=a(t(s,n)),o=l(h.Ft[0])<=l(h.Ft[3]);return{cr:h.R??(o?e:r)}},Candlestick:(t,i,s,n)=>{const e=i.upColor,r=i.downColor,h=i.borderUpColor,o=i.borderDownColor,_=i.wickUpColor,u=i.wickDownColor,c=a(t(s,n)),d=l(c.Ft[0])<=l(c.Ft[3]);return{cr:c.R??(d?e:r),dr:c.Ht??(d?h:o),pr:c.vr??(d?_:u)}},Custom:(t,i,s,n)=>({cr:a(t(s,n)).R??i.color}),Area:(t,i,s,n)=>{const e=a(t(s,n));return{cr:e.vt??i.lineColor,vt:e.vt??i.lineColor,mr:e.mr??i.topColor,wr:e.wr??i.bottomColor}},Baseline:(t,i,s,n)=>{const e=a(t(s,n));return{cr:e.Ft[3]>=i.baseValue.price?i.topLineColor:i.bottomLineColor,gr:e.gr??i.topLineColor,Mr:e.Mr??i.bottomLineColor,br:e.br??i.topFillColor1,Sr:e.Sr??i.topFillColor2,Cr:e.Cr??i.bottomFillColor1,yr:e.yr??i.bottomFillColor2}},Line:(t,i,s,n)=>{const e=a(t(s,n));return{cr:e.R??i.color,vt:e.R??i.color}},Histogram:(t,i,s,n)=>({cr:a(t(s,n)).R??i.color})};class gt{constructor(t){this.Pr=(t,i)=>void 0!==i?i.Ft:this.ge.Xs().kr(t),this.ge=t,this.Tr=wt[t.Rr()]}Dr(t,i){return this.Tr(this.Pr,this.ge.N(),t,i)}}function Mt(t,i,s,n,e=0,r=i.length){let h=r-e;for(;0<h;){const r=h>>1,a=e+r;n(i[a],s)===t?(e=a+1,h-=r+1):h=r}return e}const bt=Mt.bind(null,!0),xt=Mt.bind(null,!1);var St;!function(t){t[t.NearestLeft=-1]="NearestLeft",t[t.None=0]="None",t[t.NearestRight=1]="NearestRight"}(St||(St={}));const Ct=30;class yt{constructor(){this.Br=[],this.Er=new Map,this.Ir=new Map,this.Vr=[]}Ar(){return this.zr()>0?this.Br[this.Br.length-1]:null}Or(){return this.zr()>0?this.Lr(0):null}Ks(){return this.zr()>0?this.Lr(this.Br.length-1):null}zr(){return this.Br.length}Ki(){return 0===this.zr()}Te(t){return null!==this.Nr(t,0)}kr(t){return this.Wr(t)}Wr(t,i=0){const s=this.Nr(t,i);return null===s?null:{...this.Fr(s),Re:this.Lr(s)}}Hr(){return this.Br}Ur(t,i,s){if(this.Ki())return null;let n=null;for(const e of s){n=Pt(n,this.$r(t,i,e))}return n}ht(t){this.Ir.clear(),this.Er.clear(),this.Br=t,this.Vr=t.map((t=>t.Re))}jr(){return this.Vr}Lr(t){return this.Br[t].Re}Fr(t){return this.Br[t]}Nr(t,i){const s=this.qr(t);if(null===s&&0!==i)switch(i){case-1:return this.Yr(t);case 1:return this.Kr(t);default:throw new TypeError("Unknown search mode")}return s}Yr(t){let i=this.Xr(t);return i>0&&(i-=1),i!==this.Br.length&&this.Lr(i)<t?i:null}Kr(t){const i=this.Zr(t);return i!==this.Br.length&&t<this.Lr(i)?i:null}qr(t){const i=this.Xr(t);return i===this.Br.length||t<this.Br[i].Re?null:i}Xr(t){return bt(this.Br,t,((t,i)=>t.Re<i))}Zr(t){return xt(this.Br,t,((t,i)=>t.Re>i))}Gr(t,i,s){let n=null;for(let e=t;e<i;e++){const t=this.Br[e].Ft[s];Number.isNaN(t)||(null===n?n={Jr:t,Qr:t}:(t<n.Jr&&(n.Jr=t),t>n.Qr&&(n.Qr=t)))}return n}$r(t,i,s){if(this.Ki())return null;let n=null;const e=a(this.Or()),r=a(this.Ks()),h=Math.max(t,e),l=Math.min(i,r),o=Math.ceil(h/Ct)*Ct,_=Math.max(o,Math.floor(l/Ct)*Ct);{const t=this.Xr(h),e=this.Zr(Math.min(l,o,i));n=Pt(n,this.Gr(t,e,s))}let u=this.Er.get(s);void 0===u&&(u=new Map,this.Er.set(s,u));for(let t=Math.max(o+1,h);t<_;t+=Ct){const i=Math.floor(t/Ct);let e=u.get(i);if(void 0===e){const t=this.Xr(i*Ct),n=this.Zr((i+1)*Ct-1);e=this.Gr(t,n,s),u.set(i,e)}n=Pt(n,e)}{const t=this.Xr(_),i=this.Zr(l);n=Pt(n,this.Gr(t,i,s))}return n}}function Pt(t,i){if(null===t)return i;if(null===i)return t;return{Jr:Math.min(t.Jr,i.Jr),Qr:Math.max(t.Qr,i.Qr)}}class kt{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class Tt{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new kt(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}class Rt{constructor(t){this.hh=null,this.ah=t}oh(){return this.ah}Ns(){this.ah.updateAllViews?.()}Ws(){const t=this.ah.paneViews?.()??[];if(this.hh?.nh===t)return this.hh.eh;const i=t.map((t=>new Tt(t)));return this.hh={nh:t,eh:i},i}Yn(t,i){return this.ah.hitTest?.(t,i)??null}}let Dt=class extends Rt{us(){return[]}};class Bt{constructor(t){this.th=t}nt(t,i,s){this.th.draw(t)}ih(t,i,s){this.th.drawBackground?.(t)}}class Et{constructor(t){this.Dn=null,this.sh=t}Tt(){const t=this.sh.renderer();if(null===t)return null;if(this.Dn?.nh===t)return this.Dn.eh;const i=new Bt(t);return this.Dn={nh:t,eh:i},i}rh(){return this.sh.zOrder?.()??"normal"}}function It(t){return{ri:t.text(),Ii:t.coordinate(),Ei:t.fixedCoordinate?.(),R:t.textColor(),Z:t.backColor(),Bt:t.visible?.()??!0,pi:t.tickVisible?.()??!0}}class Vt{constructor(t,i){this.Gt=new F,this._h=t,this.uh=i}Tt(){return this.Gt.ht({Qi:this.uh.Qi(),...It(this._h)}),this.Gt}}class At extends L{constructor(t,i){super(),this._h=t,this.qi=i}ji(t,i,s){const n=It(this._h);s.Z=n.Z,t.R=n.R;const e=2/12*this.qi.P();s.Ti=e,s.Ri=e,s.Ii=n.Ii,s.Ei=n.Ei,t.ri=n.ri,t.Bt=n.Bt,t.pi=n.pi}}class zt extends Rt{constructor(t,i){super(t),this.dh=null,this.fh=null,this.ph=null,this.mh=null,this.ge=i}cs(){const t=this.ah.timeAxisViews?.()??[];if(this.dh?.nh===t)return this.dh.eh;const i=this.ge.Qt().Vt(),s=t.map((t=>new Vt(t,i)));return this.dh={nh:t,eh:s},s}Fs(){const t=this.ah.priceAxisViews?.()??[];if(this.fh?.nh===t)return this.fh.eh;const i=this.ge.Wt(),s=t.map((t=>new At(t,i)));return this.fh={nh:t,eh:s},s}wh(){const t=this.ah.priceAxisPaneViews?.()??[];if(this.ph?.nh===t)return this.ph.eh;const i=t.map((t=>new Et(t)));return this.ph={nh:t,eh:i},i}gh(){const t=this.ah.timeAxisPaneViews?.()??[];if(this.mh?.nh===t)return this.mh.eh;const i=t.map((t=>new Et(t)));return this.mh={nh:t,eh:i},i}Mh(t,i){return this.ah.autoscaleInfo?.(t,i)??null}}function Ot(t,i,s,n){t.forEach((t=>{i(t).forEach((t=>{t.rh()===s&&n.push(t)}))}))}function Lt(t){return t.Ws()}function Nt(t){return t.wh()}function Wt(t){return t.gh()}const Ft=["Area","Line","Baseline"];class Ht extends mt{constructor(t,i,s,n,e){super(t),this.qt=new yt,this.er=new ot(this),this.bh=[],this.xh=new rt(this),this.Sh=null,this.Ch=null,this.yh=null,this.Ph=[],this.ys=s,this.kh=i;const r=new _t(this);this.vs=[r],this.rr=new st(r,this,t),Ft.includes(this.kh)&&(this.Sh=new lt(this)),this.Th(),this.sh=n(this,this.Qt(),e)}m(){null!==this.yh&&clearTimeout(this.yh)}Ie(t){return this.ys.priceLineColor||t}Pe(t){const i={ke:!0},s=this.Wt();if(this.Qt().Vt().Ki()||s.Ki()||this.qt.Ki())return i;const n=this.Qt().Vt().ye(),e=this.zt();if(null===n||null===e)return i;let r,h;if(t){const t=this.qt.Ar();if(null===t)return i;r=t,h=t.Re}else{const t=this.qt.Wr(n.bi(),-1);if(null===t)return i;if(r=this.qt.kr(t.Re),null===r)return i;h=t.Re}const a=r.Ft[3],l=this.Rh().Dr(h,{Ft:r}),o=s.Nt(a,e.Ft);return{ke:!1,gt:a,ri:s.Zi(a,e.Ft),Le:s.Dh(a),Ne:s.Bh(a,e.Ft),R:l.cr,Ii:o,Re:h}}Rh(){return null!==this.Ch||(this.Ch=new gt(this)),this.Ch}N(){return this.ys}hr(t){const i=t.priceScaleId;void 0!==i&&i!==this.ys.priceScaleId&&this.Qt().Eh(this,i),_(this.ys,t),void 0!==t.priceFormat&&(this.Th(),this.Qt().Ih()),this.Qt().Vh(this),this.Qt().Ah(),this.sh.Pt("options")}ht(t,i){this.qt.ht(t),this.sh.Pt("data"),null!==this.Sh&&(i&&i.zh?this.Sh.be():0===t.length&&this.Sh.Me());const s=this.Qt().Hn(this);this.Qt().Oh(s),this.Qt().Vh(this),this.Qt().Ah(),this.Qt().ar()}Lh(t){const i=new vt(this,t);return this.bh.push(i),this.Qt().Vh(this),i}Nh(t){const i=this.bh.indexOf(t);-1!==i&&this.bh.splice(i,1),this.Qt().Vh(this)}Wh(){return this.bh}Rr(){return this.kh}zt(){const t=this.Fh();return null===t?null:{Ft:t.Ft[3],Hh:t.wt}}Fh(){const t=this.Qt().Vt().ye();if(null===t)return null;const i=t.Uh();return this.qt.Wr(i,1)}Xs(){return this.qt}$h(t){const i=this.qt.kr(t);return null===i?null:"Bar"===this.kh||"Candlestick"===this.kh||"Custom"===this.kh?{jh:i.Ft[0],qh:i.Ft[1],Yh:i.Ft[2],Kh:i.Ft[3]}:i.Ft[3]}Xh(t){const i=[];Ot(this.Ph,Lt,"top",i);const s=this.Sh;return null!==s&&s.Bt()?(null===this.yh&&s.Se()&&(this.yh=setTimeout((()=>{this.yh=null,this.Qt().Zh()}),0)),s.xe(),i.unshift(s),i):i}Ws(){const t=[];this.Gh()||t.push(this.xh),t.push(this.sh,this.er);const i=this.bh.map((t=>t.lr()));return t.push(...i),Ot(this.Ph,Lt,"normal",t),t}Jh(){return this.Qh(Lt,"bottom")}ta(t){return this.Qh(Nt,t)}ia(t){return this.Qh(Wt,t)}sa(t,i){return this.Ph.map((s=>s.Yn(t,i))).filter((t=>null!==t))}us(){return[this.rr,...this.bh.map((t=>t._r()))]}Fs(t,i){if(i!==this.es&&!this.Gh())return[];const s=[...this.vs];for(const t of this.bh)s.push(t.ur());return this.Ph.forEach((t=>{s.push(...t.Fs())})),s}cs(){const t=[];return this.Ph.forEach((i=>{t.push(...i.cs())})),t}Mh(t,i){if(void 0!==this.ys.autoscaleInfoProvider){const s=this.ys.autoscaleInfoProvider((()=>{const s=this.na(t,i);return null===s?null:s.Xe()}));return dt.Ze(s)}return this.na(t,i)}ea(){return this.ys.priceFormat.minMove}ra(){return this.ha}Ns(){this.sh.Pt();for(const t of this.vs)t.Pt();for(const t of this.bh)t.Pt();this.er.Pt(),this.xh.Pt(),this.Sh?.Pt(),this.Ph.forEach((t=>t.Ns()))}Wt(){return a(super.Wt())}At(t){if(!(("Line"===this.kh||"Area"===this.kh||"Baseline"===this.kh)&&this.ys.crosshairMarkerVisible))return null;const i=this.qt.kr(t);if(null===i)return null;return{gt:i.Ft[3],ft:this.aa(),Ht:this.la(),Lt:this.oa(),Ot:this._a(t)}}Ve(){return this.ys.title}Bt(){return this.ys.visible}ua(t){this.Ph.push(new zt(t,this))}ca(t){this.Ph=this.Ph.filter((i=>i.oh()!==t))}da(){if("Custom"===this.kh)return t=>this.sh.fa(t)}pa(){if("Custom"===this.kh)return t=>this.sh.va(t)}ma(){return this.qt.jr()}Gh(){return!q(this.Wt().wa())}na(t,i){if(!c(t)||!c(i)||this.qt.Ki())return null;const s="Line"===this.kh||"Area"===this.kh||"Baseline"===this.kh||"Histogram"===this.kh?[3]:[2,1],n=this.qt.Ur(t,i,s);let e=null!==n?new ct(n.Jr,n.Qr):null,r=null;if("Histogram"===this.Rr()){const t=this.ys.base,i=new ct(t,t);e=null!==e?e.vn(i):i}return this.Ph.forEach((s=>{const n=s.Mh(t,i);if(n?.priceRange){const t=new ct(n.priceRange.minValue,n.priceRange.maxValue);e=null!==e?e.vn(t):t}n?.margins&&(r=n.margins)})),new dt(e,r)}aa(){switch(this.kh){case"Line":case"Area":case"Baseline":return this.ys.crosshairMarkerRadius}return 0}la(){switch(this.kh){case"Line":case"Area":case"Baseline":{const t=this.ys.crosshairMarkerBorderColor;if(0!==t.length)return t}}return null}oa(){switch(this.kh){case"Line":case"Area":case"Baseline":return this.ys.crosshairMarkerBorderWidth}return 0}_a(t){switch(this.kh){case"Line":case"Area":case"Baseline":{const t=this.ys.crosshairMarkerBackgroundColor;if(0!==t.length)return t}}return this.Rh().Dr(t).cr}Th(){switch(this.ys.priceFormat.type){case"custom":this.ha={format:this.ys.priceFormat.formatter};break;case"volume":this.ha=new J(this.ys.priceFormat.precision);break;case"percent":this.ha=new G(this.ys.priceFormat.precision);break;default:{const t=Math.pow(10,this.ys.priceFormat.precision);this.ha=new Z(t,this.ys.priceFormat.minMove*t)}}null!==this.es&&this.es.ga()}Qh(t,i){const s=[];return Ot(this.Ph,t,i,s),s}}const Ut=[3],$t=[0,1,2,3];class jt{constructor(t){this.ys=t}Ma(t,i,s){let n=t;if(0===this.ys.mode)return n;const e=s.ks(),r=e.zt();if(null===r)return n;const h=e.Nt(t,r),a=s.ba().filter((t=>t instanceof Ht)).reduce(((t,n)=>{if(s.Un(n)||!n.Bt())return t;const e=n.Wt(),r=n.Xs();if(e.Ki()||!r.Te(i))return t;const h=r.kr(i);if(null===h)return t;const a=l(n.zt()),o=3===this.ys.mode?$t:Ut;return t.concat(o.map((t=>e.Nt(h.Ft[t],a.Ft))))}),[]);if(0===a.length)return n;a.sort(((t,i)=>Math.abs(t-h)-Math.abs(i-h)));const o=a[0];return n=e.Ts(o,r),n}}function qt(t,i,s){return Math.min(Math.max(t,i),s)}function Yt(t,i,s){return i-t<=s}function Kt(t){const i=Math.ceil(t);return i%2==0?i-1:i}class Xt extends y{constructor(){super(...arguments),this.qt=null}ht(t){this.qt=t}et({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(null===this.qt)return;const r=Math.max(1,Math.floor(s));t.lineWidth=r,function(t,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),i(),t.restore()}(t,(()=>{const h=a(this.qt);if(h.xa){t.strokeStyle=h.Sa,n(t,h.Ca),t.beginPath();for(const n of h.ya){const e=Math.round(n.Pa*s);t.moveTo(e,-r),t.lineTo(e,i.height+r)}t.stroke()}if(h.ka){t.strokeStyle=h.Ta,n(t,h.Ra),t.beginPath();for(const s of h.Da){const n=Math.round(s.Pa*e);t.moveTo(-r,n),t.lineTo(i.width+r,n)}t.stroke()}}))}}class Zt{constructor(t){this.Gt=new Xt,this.St=!0,this.yt=t}Pt(){this.St=!0}Tt(){if(this.St){const t=this.yt.Qt().N().grid,i={ka:t.horzLines.visible,xa:t.vertLines.visible,Ta:t.horzLines.color,Sa:t.vertLines.color,Ra:t.horzLines.style,Ca:t.vertLines.style,Da:this.yt.ks().Ba(),ya:(this.yt.Qt().Vt().Ba()||[]).map((t=>({Pa:t.coord})))};this.Gt.ht(i),this.St=!1}return this.Gt}}class Gt{constructor(t){this.sh=new Zt(t)}lr(){return this.sh}}const Jt={Ea:4,Ia:1e-4};function Qt(t,i){const s=100*(t-i)/i;return i<0?-s:s}function ti(t,i){const s=Qt(t.$e(),i),n=Qt(t.je(),i);return new ct(s,n)}function ii(t,i){const s=100*(t-i)/i+100;return i<0?-s:s}function si(t,i){const s=ii(t.$e(),i),n=ii(t.je(),i);return new ct(s,n)}function ni(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.log10(s+i.Ia)+i.Ea;return t<0?-n:n}function ei(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const n=Math.pow(10,s-i.Ea)-i.Ia;return t<0?-n:n}function ri(t,i){if(null===t)return null;const s=ni(t.$e(),i),n=ni(t.je(),i);return new ct(s,n)}function hi(t,i){if(null===t)return null;const s=ei(t.$e(),i),n=ei(t.je(),i);return new ct(s,n)}function ai(t){if(null===t)return Jt;const i=Math.abs(t.je()-t.$e());if(i>=1||i<1e-15)return Jt;const s=Math.ceil(Math.abs(Math.log10(i))),n=Jt.Ea+s;return{Ea:n,Ia:1/Math.pow(10,n)}}class li{constructor(t,i){if(this.Va=t,this.Aa=i,function(t){if(t<0)return!1;for(let i=t;i>1;i/=10)if(i%10!=0)return!1;return!0}(this.Va))this.za=[2,2.5,2];else{this.za=[];for(let t=this.Va;1!==t;){if(t%2==0)this.za.push(2),t/=2;else{if(t%5!=0)throw new Error("unexpected base");this.za.push(2,2.5),t/=5}if(this.za.length>100)throw new Error("something wrong with base")}}}Oa(t,i,s){const n=0===this.Va?0:1/this.Va;let e=Math.pow(10,Math.max(0,Math.ceil(Math.log10(t-i)))),r=0,h=this.Aa[0];for(;;){const t=Yt(e,n,1e-14)&&e>n+1e-14,i=Yt(e,s*h,1e-14),a=Yt(e,1,1e-14);if(!(t&&i&&a))break;e/=h,h=this.Aa[++r%this.Aa.length]}if(e<=n+1e-14&&(e=n),e=Math.max(1,e),this.za.length>0&&(a=e,l=1,o=1e-14,Math.abs(a-l)<o))for(r=0,h=this.za[0];Yt(e,s*h,1e-14)&&e>n+1e-14;)e/=h,h=this.za[++r%this.za.length];var a,l,o;return e}}class oi{constructor(t,i,s,n){this.La=[],this.qi=t,this.Va=i,this.Na=s,this.Wa=n}Oa(t,i){if(t<i)throw new Error("high < low");const s=this.qi.$t(),n=(t-i)*this.Fa()/s,e=new li(this.Va,[2,2.5,2]),r=new li(this.Va,[2,2,2.5]),h=new li(this.Va,[2.5,2,2]),a=[];return a.push(e.Oa(t,i,n),r.Oa(t,i,n),h.Oa(t,i,n)),function(t){if(t.length<1)throw Error("array is empty");let i=t[0];for(let s=1;s<t.length;++s)t[s]<i&&(i=t[s]);return i}(a)}Ha(){const t=this.qi,i=t.zt();if(null===i)return void(this.La=[]);const s=t.$t(),n=this.Na(s-1,i),e=this.Na(0,i),r=this.qi.N().entireTextOnly?this.Ua()/2:0,h=r,a=s-1-r,l=Math.max(n,e),o=Math.min(n,e);if(l===o)return void(this.La=[]);let _=this.Oa(l,o),u=l%_;u+=u<0?_:0;const c=l>=o?1:-1;let d=null,f=0;for(let s=l-u;s>o;s-=_){const n=this.Wa(s,i,!0);null!==d&&Math.abs(n-d)<this.Fa()||(n<h||n>a||(f<this.La.length?(this.La[f].Pa=n,this.La[f].$a=t.ja(s)):this.La.push({Pa:n,$a:t.ja(s)}),f++,d=n,t.qa()&&(_=this.Oa(s*c,o))))}this.La.length=f}Ba(){return this.La}Ua(){return this.qi.P()}Fa(){return Math.ceil(2.5*this.Ua())}}function _i(t){return t.slice().sort(((t,i)=>a(t.hs())-a(i.hs())))}var ui;!function(t){t[t.Normal=0]="Normal",t[t.Logarithmic=1]="Logarithmic",t[t.Percentage=2]="Percentage",t[t.IndexedTo100=3]="IndexedTo100"}(ui||(ui={}));const ci=new G,di=new Z(100,1);class fi{constructor(t,i,s,n,e){this.Ya=0,this.Ka=null,this.Ge=null,this.Xa=null,this.Za={Ga:!1,Ja:null},this.Qa=0,this.tl=0,this.il=new o,this.sl=new o,this.nl=[],this.el=null,this.rl=null,this.hl=null,this.al=null,this.ll=null,this.ha=di,this.ol=ai(null),this._l=t,this.ys=i,this.ul=s,this.cl=n,this.dl=e,this.fl=new oi(this,100,this.pl.bind(this),this.vl.bind(this))}wa(){return this._l}N(){return this.ys}hr(t){if(_(this.ys,t),this.ga(),void 0!==t.mode&&this.ml({ie:t.mode}),void 0!==t.scaleMargins){const i=h(t.scaleMargins.top),s=h(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this.wl(),this.hl=null}}gl(){return this.ys.autoScale}qa(){return 1===this.ys.mode}Oe(){return 2===this.ys.mode}Ml(){return 3===this.ys.mode}ie(){return{sn:this.ys.autoScale,bl:this.ys.invertScale,ie:this.ys.mode}}ml(t){const i=this.ie();let s=null;void 0!==t.sn&&(this.ys.autoScale=t.sn),void 0!==t.ie&&(this.ys.mode=t.ie,2!==t.ie&&3!==t.ie||(this.ys.autoScale=!0),this.Za.Ga=!1),1===i.ie&&t.ie!==i.ie&&(!function(t,i){if(null===t)return!1;const s=ei(t.$e(),i),n=ei(t.je(),i);return isFinite(s)&&isFinite(n)}(this.Ge,this.ol)?this.ys.autoScale=!0:(s=hi(this.Ge,this.ol),null!==s&&this.xl(s))),1===t.ie&&t.ie!==i.ie&&(s=ri(this.Ge,this.ol),null!==s&&this.xl(s));const n=i.ie!==this.ys.mode;n&&(2===i.ie||this.Oe())&&this.ga(),n&&(3===i.ie||this.Ml())&&this.ga(),void 0!==t.bl&&i.bl!==t.bl&&(this.ys.invertScale=t.bl,this.Sl()),this.sl.p(i,this.ie())}Cl(){return this.sl}P(){return this.ul.fontSize}$t(){return this.Ya}yl(t){this.Ya!==t&&(this.Ya=t,this.wl(),this.hl=null)}Pl(){if(this.Ka)return this.Ka;const t=this.$t()-this.kl()-this.Tl();return this.Ka=t,t}Qe(){return this.Rl(),this.Ge}xl(t,i){const s=this.Ge;(i||null===s&&null!==t||null!==s&&!s.He(t))&&(this.hl=null,this.Ge=t)}Ki(){return this.Rl(),0===this.Ya||!this.Ge||this.Ge.Ki()}Dl(t){return this.bl()?t:this.$t()-1-t}Nt(t,i){return this.Oe()?t=Qt(t,i):this.Ml()&&(t=ii(t,i)),this.vl(t,i)}Bl(t,i,s){this.Rl();const n=this.Tl(),e=a(this.Qe()),r=e.$e(),h=e.je(),l=this.Pl()-1,o=this.bl(),_=l/(h-r),u=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.El();for(let s=u;s<c;s++){const e=t[s],h=e.gt;if(isNaN(h))continue;let a=h;null!==d&&(a=d(e.gt,i));const l=n+_*(a-r),u=o?l:this.Ya-1-l;e.ut=u}}Il(t,i,s){this.Rl();const n=this.Tl(),e=a(this.Qe()),r=e.$e(),h=e.je(),l=this.Pl()-1,o=this.bl(),_=l/(h-r),u=void 0===s?0:s.from,c=void 0===s?t.length:s.to,d=this.El();for(let s=u;s<c;s++){const e=t[s];let h=e.jh,a=e.qh,l=e.Yh,u=e.Kh;null!==d&&(h=d(e.jh,i),a=d(e.qh,i),l=d(e.Yh,i),u=d(e.Kh,i));let c=n+_*(h-r),f=o?c:this.Ya-1-c;e.Vl=f,c=n+_*(a-r),f=o?c:this.Ya-1-c,e.Al=f,c=n+_*(l-r),f=o?c:this.Ya-1-c,e.zl=f,c=n+_*(u-r),f=o?c:this.Ya-1-c,e.Ol=f}}Ts(t,i){const s=this.pl(t,i);return this.Ll(s,i)}Ll(t,i){let s=t;return this.Oe()?s=function(t,i){return i<0&&(t=-t),t/100*i+i}(s,i):this.Ml()&&(s=function(t,i){return t-=100,i<0&&(t=-t),t/100*i+i}(s,i)),s}ba(){return this.nl}Dt(){return this.rl||(this.rl=_i(this.nl)),this.rl}Nl(t){-1===this.nl.indexOf(t)&&(this.nl.push(t),this.ga(),this.Wl())}Fl(t){const i=this.nl.indexOf(t);if(-1===i)throw new Error("source is not attached to scale");this.nl.splice(i,1),0===this.nl.length&&(this.ml({sn:!0}),this.xl(null)),this.ga(),this.Wl()}zt(){let t=null;for(const i of this.nl){const s=i.zt();null!==s&&((null===t||s.Hh<t.Hh)&&(t=s))}return null===t?null:t.Ft}bl(){return this.ys.invertScale}Ba(){const t=null===this.zt();if(null!==this.hl&&(t||this.hl.Hl===t))return this.hl.Ba;this.fl.Ha();const i=this.fl.Ba();return this.hl={Ba:i,Hl:t},this.il.p(),i}Ul(){return this.il}$l(t){this.Oe()||this.Ml()||null===this.al&&null===this.Xa&&(this.Ki()||(this.al=this.Ya-t,this.Xa=a(this.Qe()).Ue()))}jl(t){if(this.Oe()||this.Ml())return;if(null===this.al)return;this.ml({sn:!1}),(t=this.Ya-t)<0&&(t=0);let i=(this.al+.2*(this.Ya-1))/(t+.2*(this.Ya-1));const s=a(this.Xa).Ue();i=Math.max(i,.1),s.Ye(i),this.xl(s)}ql(){this.Oe()||this.Ml()||(this.al=null,this.Xa=null)}Yl(t){this.gl()||null===this.ll&&null===this.Xa&&(this.Ki()||(this.ll=t,this.Xa=a(this.Qe()).Ue()))}Kl(t){if(this.gl())return;if(null===this.ll)return;const i=a(this.Qe()).qe()/(this.Pl()-1);let s=t-this.ll;this.bl()&&(s*=-1);const n=s*i,e=a(this.Xa).Ue();e.Ke(n),this.xl(e,!0),this.hl=null}Xl(){this.gl()||null!==this.ll&&(this.ll=null,this.Xa=null)}ra(){return this.ha||this.ga(),this.ha}Zi(t,i){switch(this.ys.mode){case 2:return this.Zl(Qt(t,i));case 3:return this.ra().format(ii(t,i));default:return this.nr(t)}}ja(t){switch(this.ys.mode){case 2:return this.Zl(t);case 3:return this.ra().format(t);default:return this.nr(t)}}Dh(t){return this.nr(t,a(this.el).ra())}Bh(t,i){return t=Qt(t,i),this.Zl(t,ci)}Gl(){return this.nl}Jl(t){this.Za={Ja:t,Ga:!1}}Ns(){this.nl.forEach((t=>t.Ns()))}ga(){this.hl=null;let t=1/0;this.el=null;for(const i of this.nl)i.hs()<t&&(t=i.hs(),this.el=i);let i=100;null!==this.el&&(i=Math.round(1/this.el.ea())),this.ha=di,this.Oe()?(this.ha=ci,i=100):this.Ml()?(this.ha=new Z(100,1),i=100):null!==this.el&&(this.ha=this.el.ra()),this.fl=new oi(this,i,this.pl.bind(this),this.vl.bind(this)),this.fl.Ha()}Wl(){this.rl=null}Xi(){return this.dl}kl(){return this.bl()?this.ys.scaleMargins.bottom*this.$t()+this.tl:this.ys.scaleMargins.top*this.$t()+this.Qa}Tl(){return this.bl()?this.ys.scaleMargins.top*this.$t()+this.Qa:this.ys.scaleMargins.bottom*this.$t()+this.tl}Rl(){this.Za.Ga||(this.Za.Ga=!0,this.Ql())}wl(){this.Ka=null}vl(t,i){if(this.Rl(),this.Ki())return 0;t=this.qa()&&t?ni(t,this.ol):t;const s=a(this.Qe()),n=this.Tl()+(this.Pl()-1)*(t-s.$e())/s.qe();return this.Dl(n)}pl(t,i){if(this.Rl(),this.Ki())return 0;const s=this.Dl(t),n=a(this.Qe()),e=n.$e()+n.qe()*((s-this.Tl())/(this.Pl()-1));return this.qa()?ei(e,this.ol):e}Sl(){this.hl=null,this.fl.Ha()}Ql(){const t=this.Za.Ja;if(null===t)return;let i=null;const s=this.Gl();let n=0,e=0;for(const r of s){if(!r.Bt())continue;const s=r.zt();if(null===s)continue;const h=r.Mh(t.Uh(),t.bi());let l=h&&h.Qe();if(null!==l){switch(this.ys.mode){case 1:l=ri(l,this.ol);break;case 2:l=ti(l,s.Ft);break;case 3:l=si(l,s.Ft)}if(i=null===i?l:i.vn(a(l)),null!==h){const t=h.tr();null!==t&&(n=Math.max(n,t.above),e=Math.max(e,t.below))}}}if(n===this.Qa&&e===this.tl||(this.Qa=n,this.tl=e,this.hl=null,this.wl()),null!==i){if(i.$e()===i.je()){const t=this.el,s=5*(null===t||this.Oe()||this.Ml()?1:t.ea());this.qa()&&(i=hi(i,this.ol)),i=new ct(i.$e()-s,i.je()+s),this.qa()&&(i=ri(i,this.ol))}if(this.qa()){const t=hi(i,this.ol),s=ai(t);if(r=s,h=this.ol,r.Ea!==h.Ea||r.Ia!==h.Ia){const n=null!==this.Xa?hi(this.Xa,this.ol):null;this.ol=s,i=ri(t,s),null!==n&&(this.Xa=ri(n,s))}}this.xl(i)}else null===this.Ge&&(this.xl(new ct(-.5,.5)),this.ol=ai(null));var r,h;this.Za.Ga=!0}El(){return this.Oe()?Qt:this.Ml()?ii:this.qa()?t=>ni(t,this.ol):null}io(t,i,s){return void 0===i?(void 0===s&&(s=this.ra()),s.format(t)):i(t)}nr(t,i){return this.io(t,this.cl.priceFormatter,i)}Zl(t,i){return this.io(t,this.cl.percentageFormatter,i)}}function pi(t){return t instanceof Ht}class vi{constructor(t,i){this.nl=[],this.so=new Map,this.Ya=0,this.no=0,this.eo=1e3,this.rl=null,this.ro=new o,this.Ph=[],this.uh=t,this.ts=i,this.ho=new Gt(this);const s=i.N();this.ao=this.lo("left",s.leftPriceScale),this.oo=this.lo("right",s.rightPriceScale),this.ao.Cl().i(this._o.bind(this,this.ao),this),this.oo.Cl().i(this._o.bind(this,this.oo),this),this.uo(s)}uo(t){if(t.leftPriceScale&&this.ao.hr(t.leftPriceScale),t.rightPriceScale&&this.oo.hr(t.rightPriceScale),t.localization&&(this.ao.ga(),this.oo.ga()),t.overlayPriceScales){const i=Array.from(this.so.values());for(const s of i){const i=a(s[0].Wt());i.hr(t.overlayPriceScales),t.localization&&i.ga()}}}co(t){switch(t){case"left":return this.ao;case"right":return this.oo}return this.so.has(t)?h(this.so.get(t))[0].Wt():null}m(){this.Qt().do().u(this),this.ao.Cl().u(this),this.oo.Cl().u(this),this.nl.forEach((t=>{t.m&&t.m()})),this.Ph=this.Ph.filter((t=>{const i=t.oh();return i.detached&&i.detached(),!1})),this.ro.p()}fo(){return this.eo}po(t){this.eo=t}Qt(){return this.ts}Qi(){return this.no}$t(){return this.Ya}vo(t){this.no=t,this.mo()}yl(t){this.Ya=t,this.ao.yl(t),this.oo.yl(t),this.nl.forEach((i=>{if(this.Un(i)){const s=i.Wt();null!==s&&s.yl(t)}})),this.mo()}wo(){return this.nl.filter(pi)}ba(){return this.nl}Un(t){const i=t.Wt();return null===i||this.ao!==i&&this.oo!==i}Nl(t,i,s){this.Mo(t,i,s?t.hs():this.nl.length)}Fl(t,i){const s=this.nl.indexOf(t);r(-1!==s,"removeDataSource: invalid data source"),this.nl.splice(s,1),i||this.nl.forEach(((t,i)=>t.ls(i)));const n=a(t.Wt()).wa();if(this.so.has(n)){const i=h(this.so.get(n)),s=i.indexOf(t);-1!==s&&(i.splice(s,1),0===i.length&&this.so.delete(n))}const e=t.Wt();e&&e.ba().indexOf(t)>=0&&(e.Fl(t),this.bo(e)),this.rl=null}jn(t){return t===this.ao?"left":t===this.oo?"right":"overlay"}xo(){return this.ao}So(){return this.oo}Co(t,i){t.$l(i)}yo(t,i){t.jl(i),this.mo()}Po(t){t.ql()}ko(t,i){t.Yl(i)}To(t,i){t.Kl(i),this.mo()}Ro(t){t.Xl()}mo(){this.nl.forEach((t=>{t.Ns()}))}ks(){let t=null;return this.ts.N().rightPriceScale.visible&&0!==this.oo.ba().length?t=this.oo:this.ts.N().leftPriceScale.visible&&0!==this.ao.ba().length?t=this.ao:0!==this.nl.length&&(t=this.nl[0].Wt()),null===t&&(t=this.oo),t}$n(){let t=null;return this.ts.N().rightPriceScale.visible?t=this.oo:this.ts.N().leftPriceScale.visible&&(t=this.ao),t}bo(t){null!==t&&t.gl()&&this.Do(t)}Bo(t){const i=this.uh.ye();t.ml({sn:!0}),null!==i&&t.Jl(i),this.mo()}Eo(){this.Do(this.ao),this.Do(this.oo)}Io(){this.bo(this.ao),this.bo(this.oo),this.nl.forEach((t=>{this.Un(t)&&this.bo(t.Wt())})),this.mo(),this.ts.ar()}Dt(){return null===this.rl&&(this.rl=_i(this.nl)),this.rl}Vo(t,i){i=qt(i,0,this.nl.length-1);const s=this.nl.indexOf(t);r(-1!==s,"setSeriesOrder: invalid data source"),this.nl.splice(s,1),this.nl.splice(i,0,t),this.nl.forEach(((t,i)=>t.ls(i))),this.rl=null;for(const t of[this.ao,this.oo])t.Wl(),t.ga();this.ts.ar()}Et(){return this.Dt().filter(pi)}Ao(){return this.ro}zo(){return this.ho}ua(t){this.Ph.push(new Dt(t))}ca(t){this.Ph=this.Ph.filter((i=>i.oh()!==t)),t.detached&&t.detached(),this.ts.ar()}Oo(){return this.Ph}sa(t,i){return this.Ph.map((s=>s.Yn(t,i))).filter((t=>null!==t))}Do(t){const i=t.Gl();if(i&&i.length>0&&!this.uh.Ki()){const i=this.uh.ye();null!==i&&t.Jl(i)}t.Ns()}Mo(t,i,s){let n=this.co(i);if(null===n&&(n=this.lo(i,this.ts.N().overlayPriceScales)),this.nl.splice(s,0,t),!q(i)){const s=this.so.get(i)||[];s.push(t),this.so.set(i,s)}t.ls(s),n.Nl(t),t._s(n),this.bo(n),this.rl=null}_o(t,i,s){i.ie!==s.ie&&this.Do(t)}lo(t,i){const s={visible:!0,autoScale:!0,...p(i)},n=new fi(t,s,this.ts.N().layout,this.ts.N().localization,this.ts.Xi());return n.yl(this.$t()),n}}function mi(t){return{Lo:t.Lo,No:{Kn:t.Wo.externalId},Fo:t.Wo.cursorStyle}}function wi(t,i,s,n){for(const e of t){const t=e.Tt(n);if(null!==t&&t.Yn){const n=t.Yn(i,s);if(null!==n)return{Ho:e,No:n}}}return null}function gi(t){return void 0!==t.Ws}function Mi(t,i,s){const n=[t,...t.Dt()],e=function(t,i,s){let n,e;for(const a of t){const t=a.sa?.(i,s)??[];for(const i of t)r=i.zOrder,h=n?.zOrder,(!h||"top"===r&&"top"!==h||"normal"===r&&"bottom"===h)&&(n=i,e=a)}var r,h;return n&&e?{Wo:n,Lo:e}:null}(n,i,s);if("top"===e?.Wo.zOrder)return mi(e);for(const r of n){if(e&&e.Lo===r&&"bottom"!==e.Wo.zOrder&&!e.Wo.isBackground)return mi(e);if(gi(r)){const n=wi(r.Ws(t),i,s,t);if(null!==n)return{Lo:r,Ho:n.Ho,No:n.No}}if(e&&e.Lo===r&&"bottom"!==e.Wo.zOrder&&e.Wo.isBackground)return mi(e)}return e?.Wo?mi(e):null}class bi{constructor(t,i,s=50){this.Pn=0,this.kn=1,this.Tn=1,this.Dn=new Map,this.Rn=new Map,this.Uo=t,this.$o=i,this.Bn=s}jo(t){const i=t.time,s=this.$o.cacheKey(i),n=this.Dn.get(s);if(void 0!==n)return n.qo;if(this.Pn===this.Bn){const t=this.Rn.get(this.Tn);this.Rn.delete(this.Tn),this.Dn.delete(h(t)),this.Tn++,this.Pn--}const e=this.Uo(t);return this.Dn.set(s,{qo:e,An:this.kn}),this.Rn.set(this.kn,s),this.Pn++,this.kn++,e}}class xi{constructor(t,i){r(t<=i,"right should be >= left"),this.Yo=t,this.Ko=i}Uh(){return this.Yo}bi(){return this.Ko}Xo(){return this.Ko-this.Yo+1}Te(t){return this.Yo<=t&&t<=this.Ko}He(t){return this.Yo===t.Uh()&&this.Ko===t.bi()}}function Si(t,i){return null===t||null===i?t===i:t.He(i)}class Ci{constructor(){this.Zo=new Map,this.Dn=null,this.Go=!1}Jo(t){this.Go=t,this.Dn=null}Qo(t,i){this.t_(i),this.Dn=null;for(let s=i;s<t.length;++s){const i=t[s];let n=this.Zo.get(i.timeWeight);void 0===n&&(n=[],this.Zo.set(i.timeWeight,n)),n.push({index:s,time:i.time,weight:i.timeWeight,originalTime:i.originalTime})}}i_(t,i,s,n,e){const r=Math.ceil(i/t);return null!==this.Dn&&this.Dn.s_===r&&e===this.Dn.n_&&s===this.Dn.e_||(this.Dn={n_:e,e_:s,Ba:this.r_(r,s,n),s_:r}),this.Dn.Ba}t_(t){if(0===t)return void this.Zo.clear();const i=[];this.Zo.forEach(((s,n)=>{t<=s[0].index?i.push(n):s.splice(bt(s,t,(i=>i.index<t)),1/0)}));for(const t of i)this.Zo.delete(t)}r_(t,i,s){let n=[];const e=t=>!i||s.has(t.index);for(const i of Array.from(this.Zo.keys()).sort(((t,i)=>i-t))){if(!this.Zo.get(i))continue;const s=n;n=[];const r=s.length;let a=0;const l=h(this.Zo.get(i)),o=l.length;let _=1/0,u=-1/0;for(let i=0;i<o;i++){const h=l[i],o=h.index;for(;a<r;){const t=s[a],i=t.index;if(!(i<o&&e(t))){_=i;break}a++,n.push(t),u=i,_=1/0}if(_-o>=t&&o-u>=t&&e(h))n.push(h),u=o;else if(this.Go)return s}for(;a<r;a++)e(s[a])&&n.push(s[a])}return n}}class yi{constructor(t){this.h_=t}a_(){return null===this.h_?null:new xi(Math.floor(this.h_.Uh()),Math.ceil(this.h_.bi()))}l_(){return this.h_}static o_(){return new yi(null)}}function Pi(t,i){return t.weight>i.weight?t:i}class ki{constructor(t,i,s,n){this.no=0,this.__=null,this.u_=[],this.ll=null,this.al=null,this.c_=new Ci,this.d_=new Map,this.f_=yi.o_(),this.p_=!0,this.v_=new o,this.m_=new o,this.w_=new o,this.g_=null,this.M_=null,this.b_=new Map,this.x_=-1,this.S_=[],this.ys=i,this.cl=s,this.C_=i.rightOffset,this.y_=i.barSpacing,this.ts=t,this.$o=n,this.P_(),this.c_.Jo(i.uniformDistribution),this.k_()}N(){return this.ys}T_(t){_(this.cl,t),this.R_(),this.P_()}hr(t,i){_(this.ys,t),this.ys.fixLeftEdge&&this.D_(),this.ys.fixRightEdge&&this.B_(),void 0!==t.barSpacing&&this.ts.dn(t.barSpacing),void 0!==t.rightOffset&&this.ts.fn(t.rightOffset),void 0===t.minBarSpacing&&void 0===t.maxBarSpacing||this.ts.dn(t.barSpacing??this.y_),void 0!==t.ignoreWhitespaceIndices&&t.ignoreWhitespaceIndices!==this.ys.ignoreWhitespaceIndices&&this.k_(),this.R_(),this.P_(),this.w_.p()}Rs(t){return this.u_[t]?.time??null}ss(t){return this.u_[t]??null}E_(t,i){if(this.u_.length<1)return null;if(this.$o.key(t)>this.$o.key(this.u_[this.u_.length-1].time))return i?this.u_.length-1:null;const s=bt(this.u_,this.$o.key(t),((t,i)=>this.$o.key(t.time)<i));return this.$o.key(t)<this.$o.key(this.u_[s].time)?i?s:null:s}Ki(){return 0===this.no||0===this.u_.length||null===this.__}I_(){return this.u_.length>0}ye(){return this.V_(),this.f_.a_()}A_(){return this.V_(),this.f_.l_()}z_(){const t=this.ye();if(null===t)return null;const i={from:t.Uh(),to:t.bi()};return this.O_(i)}O_(t){const i=Math.round(t.from),s=Math.round(t.to),n=a(this.L_()),e=a(this.N_());return{from:a(this.ss(Math.max(n,i))),to:a(this.ss(Math.min(e,s)))}}W_(t){return{from:a(this.E_(t.from,!0)),to:a(this.E_(t.to,!0))}}Qi(){return this.no}vo(t){if(!isFinite(t)||t<=0)return;if(this.no===t)return;const i=this.A_(),s=this.no;if(this.no=t,this.p_=!0,this.ys.lockVisibleTimeRangeOnResize&&0!==s){const i=this.y_*t/s;this.y_=i}if(this.ys.fixLeftEdge&&null!==i&&i.Uh()<=0){const i=s-t;this.C_-=Math.round(i/this.y_)+1,this.p_=!0}this.F_(),this.H_()}jt(t){if(this.Ki()||!c(t))return 0;const i=this.U_()+this.C_-t;return this.no-(i+.5)*this.y_-1}j_(t,i){const s=this.U_(),n=void 0===i?0:i.from,e=void 0===i?t.length:i.to;for(let i=n;i<e;i++){const n=t[i].wt,e=s+this.C_-n,r=this.no-(e+.5)*this.y_-1;t[i]._t=r}}q_(t,i){const s=Math.ceil(this.Y_(t));return i&&this.ys.ignoreWhitespaceIndices&&!this.K_(s)?this.X_(s):s}fn(t){this.p_=!0,this.C_=t,this.H_(),this.ts.Z_(),this.ts.ar()}G_(){return this.y_}dn(t){this.J_(t),this.H_(),this.ts.Z_(),this.ts.ar()}Q_(){return this.C_}Ba(){if(this.Ki())return null;if(null!==this.M_)return this.M_;const t=this.y_,i=5*(this.ts.N().layout.fontSize+4)/8*(this.ys.tickMarkMaxCharacterLength||8),s=Math.round(i/t),n=a(this.ye()),e=Math.max(n.Uh(),n.Uh()-s),r=Math.max(n.bi(),n.bi()-s),h=this.c_.i_(t,i,this.ys.ignoreWhitespaceIndices,this.b_,this.x_),l=this.L_()+s,o=this.N_()-s,_=this.tu(),u=this.ys.fixLeftEdge||_,c=this.ys.fixRightEdge||_;let d=0;for(const t of h){if(!(e<=t.index&&t.index<=r))continue;let s;d<this.S_.length?(s=this.S_[d],s.coord=this.jt(t.index),s.label=this.iu(t),s.weight=t.weight):(s={needAlignCoordinate:!1,coord:this.jt(t.index),label:this.iu(t),weight:t.weight},this.S_.push(s)),this.y_>i/2&&!_?s.needAlignCoordinate=!1:s.needAlignCoordinate=u&&t.index<=l||c&&t.index>=o,d++}return this.S_.length=d,this.M_=this.S_,this.S_}su(){this.p_=!0,this.dn(this.ys.barSpacing),this.fn(this.ys.rightOffset)}nu(t){this.p_=!0,this.__=t,this.H_(),this.D_()}eu(t,i){const s=this.Y_(t),n=this.G_(),e=n+i*(n/10);this.dn(e),this.ys.rightBarStaysOnScroll||this.fn(this.Q_()+(s-this.Y_(t)))}$l(t){this.ll&&this.Xl(),null===this.al&&null===this.g_&&(this.Ki()||(this.al=t,this.ru()))}jl(t){if(null===this.g_)return;const i=qt(this.no-t,0,this.no),s=qt(this.no-a(this.al),0,this.no);0!==i&&0!==s&&this.dn(this.g_.G_*i/s)}ql(){null!==this.al&&(this.al=null,this.hu())}Yl(t){null===this.ll&&null===this.g_&&(this.Ki()||(this.ll=t,this.ru()))}Kl(t){if(null===this.ll)return;const i=(this.ll-t)/this.G_();this.C_=a(this.g_).Q_+i,this.p_=!0,this.H_()}Xl(){null!==this.ll&&(this.ll=null,this.hu())}au(){this.lu(this.ys.rightOffset)}lu(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this.C_,n=performance.now();this.ts._n({ou:t=>(t-n)/i>=1,_u:e=>{const r=(e-n)/i;return r>=1?t:s+(t-s)*r}})}Pt(t,i){this.p_=!0,this.u_=t,this.c_.Qo(t,i),this.H_()}uu(){return this.v_}cu(){return this.m_}du(){return this.w_}U_(){return this.__||0}fu(t){const i=t.Xo();this.J_(this.no/i),this.C_=t.bi()-this.U_(),this.H_(),this.p_=!0,this.ts.Z_(),this.ts.ar()}pu(){const t=this.L_(),i=this.N_();null!==t&&null!==i&&this.fu(new xi(t,i+this.ys.rightOffset))}vu(t){const i=new xi(t.from,t.to);this.fu(i)}ns(t){return void 0!==this.cl.timeFormatter?this.cl.timeFormatter(t.originalTime):this.$o.formatHorzItem(t.time)}k_(){if(!this.ys.ignoreWhitespaceIndices)return;this.b_.clear();const t=this.ts.Ys();for(const i of t)for(const t of i.ma())this.b_.set(t,!0);this.x_++}tu(){const t=this.ts.N().handleScroll,i=this.ts.N().handleScale;return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}L_(){return 0===this.u_.length?null:0}N_(){return 0===this.u_.length?null:this.u_.length-1}mu(t){return(this.no-1-t)/this.y_}Y_(t){const i=this.mu(t),s=this.U_()+this.C_-i;return Math.round(1e6*s)/1e6}J_(t){const i=this.y_;this.y_=t,this.F_(),i!==this.y_&&(this.p_=!0,this.wu())}V_(){if(!this.p_)return;if(this.p_=!1,this.Ki())return void this.gu(yi.o_());const t=this.U_(),i=this.no/this.y_,s=this.C_+t,n=new xi(s-i+1,s);this.gu(new yi(n))}F_(){const t=qt(this.y_,this.Mu(),this.bu());this.y_!==t&&(this.y_=t,this.p_=!0)}bu(){return this.ys.maxBarSpacing>0?this.ys.maxBarSpacing:.5*this.no}Mu(){return this.ys.fixLeftEdge&&this.ys.fixRightEdge&&0!==this.u_.length?this.no/this.u_.length:this.ys.minBarSpacing}H_(){const t=this.xu();null!==t&&this.C_<t&&(this.C_=t,this.p_=!0);const i=this.Su();this.C_>i&&(this.C_=i,this.p_=!0)}xu(){const t=this.L_(),i=this.__;if(null===t||null===i)return null;return t-i-1+(this.ys.fixLeftEdge?this.no/this.y_:Math.min(2,this.u_.length))}Su(){return this.ys.fixRightEdge?0:this.no/this.y_-Math.min(2,this.u_.length)}ru(){this.g_={G_:this.G_(),Q_:this.Q_()}}hu(){this.g_=null}iu(t){let i=this.d_.get(t.weight);return void 0===i&&(i=new bi((t=>this.Cu(t)),this.$o),this.d_.set(t.weight,i)),i.jo(t)}Cu(t){return this.$o.formatTickmark(t,this.cl)}gu(t){const i=this.f_;this.f_=t,Si(i.a_(),this.f_.a_())||this.v_.p(),Si(i.l_(),this.f_.l_())||this.m_.p(),this.wu()}wu(){this.M_=null}R_(){this.wu(),this.d_.clear()}P_(){this.$o.updateFormatter(this.cl)}D_(){if(!this.ys.fixLeftEdge)return;const t=this.L_();if(null===t)return;const i=this.ye();if(null===i)return;const s=i.Uh()-t;if(s<0){const t=this.C_-s-1;this.fn(t)}this.F_()}B_(){this.H_(),this.F_()}K_(t){return!this.ys.ignoreWhitespaceIndices||(this.b_.get(t)||!1)}X_(t){const i=function*(t){const i=Math.round(t),s=i<t;let n=1;for(;;)s?(yield i+n,yield i-n):(yield i-n,yield i+n),n++}(t),s=this.N_();for(;s;){const t=i.next().value;if(this.b_.get(t))return t;if(t<0||t>s)break}return t}}var Ti,Ri,Di,Bi,Ei;!function(t){t[t.OnTouchEnd=0]="OnTouchEnd",t[t.OnNextTap=1]="OnNextTap"}(Ti||(Ti={}));class Ii{constructor(t,i,s){this.yu=[],this.Pu=[],this.no=0,this.ku=null,this.Tu=new o,this.Ru=new o,this.Du=null,this.Bu=t,this.ys=i,this.$o=s,this.dl=new S(this.ys.layout.colorParsers),this.Eu=new M(this),this.uh=new ki(this,i.timeScale,this.ys.localization,s),this.Ct=new j(this,i.crosshair),this.Iu=new jt(i.crosshair),this.Vu(0),this.yu[0].po(2e3),this.Au=this.zu(0),this.Ou=this.zu(1)}Ih(){this.Lu(Y.gn())}ar(){this.Lu(Y.wn())}Zh(){this.Lu(new Y(1))}Vh(t){const i=this.Nu(t);this.Lu(i)}Wu(){return this.ku}Fu(t){if(this.ku?.Lo===t?.Lo&&this.ku?.No?.Kn===t?.No?.Kn)return;const i=this.ku;this.ku=t,null!==i&&this.Vh(i.Lo),null!==t&&t.Lo!==i?.Lo&&this.Vh(t.Lo)}N(){return this.ys}hr(t){_(this.ys,t),this.yu.forEach((i=>i.uo(t))),void 0!==t.timeScale&&this.uh.hr(t.timeScale),void 0!==t.localization&&this.uh.T_(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.Tu.p(),this.Au=this.zu(0),this.Ou=this.zu(1),this.Ih()}Hu(t,i,s=0){const n=this.yu[s];if(void 0===n)return;if("left"===t)return _(this.ys,{leftPriceScale:i}),n.uo({leftPriceScale:i}),this.Tu.p(),void this.Ih();if("right"===t)return _(this.ys,{rightPriceScale:i}),n.uo({rightPriceScale:i}),this.Tu.p(),void this.Ih();const e=this.Uu(t,s);null!==e&&(e.Wt.hr(i),this.Tu.p())}Uu(t,i){const s=this.yu[i];if(void 0===s)return null;const n=s.co(t);return null!==n?{Us:s,Wt:n}:null}Vt(){return this.uh}$s(){return this.yu}$u(){return this.Ct}ju(){return this.Ru}qu(t,i){t.yl(i),this.Z_()}vo(t){this.no=t,this.uh.vo(this.no),this.yu.forEach((i=>i.vo(t))),this.Z_()}Yu(t){1!==this.yu.length&&(r(t>=0&&t<this.yu.length,"Invalid pane index"),this.yu.splice(t,1),this.Ih())}Ku(t,i){if(this.yu.length<2)return;r(t>=0&&t<this.yu.length,"Invalid pane index");const s=this.yu[t],n=this.yu.reduce(((t,i)=>t+i.fo()),0),e=this.yu.reduce(((t,i)=>t+i.$t()),0),h=e-30*(this.yu.length-1);i=Math.min(h,Math.max(30,i));const a=n/e,l=s.$t();s.po(i*a);let o=i-l,_=this.yu.length-1;for(const t of this.yu)if(t!==s){const i=Math.min(h,Math.max(30,t.$t()-o/_));o-=t.$t()-i,_-=1;const s=i*a;t.po(s)}this.Ih()}Xu(t,i){r(t>=0&&t<this.yu.length&&i>=0&&i<this.yu.length,"Invalid pane index");const s=this.yu[t],n=this.yu[i];this.yu[t]=n,this.yu[i]=s,this.Ih()}Co(t,i,s){t.Co(i,s)}yo(t,i,s){t.yo(i,s),this.Ah(),this.Lu(this.Zu(t,2))}Po(t,i){t.Po(i),this.Lu(this.Zu(t,2))}ko(t,i,s){i.gl()||t.ko(i,s)}To(t,i,s){i.gl()||(t.To(i,s),this.Ah(),this.Lu(this.Zu(t,2)))}Ro(t,i){i.gl()||(t.Ro(i),this.Lu(this.Zu(t,2)))}Bo(t,i){t.Bo(i),this.Lu(this.Zu(t,2))}Gu(t){this.uh.$l(t)}Ju(t,i){const s=this.Vt();if(s.Ki()||0===i)return;const n=s.Qi();t=Math.max(1,Math.min(t,n)),s.eu(t,i),this.Z_()}Qu(t){this.tc(0),this.sc(t),this.nc()}ec(t){this.uh.jl(t),this.Z_()}rc(){this.uh.ql(),this.ar()}tc(t){this.uh.Yl(t)}sc(t){this.uh.Kl(t),this.Z_()}nc(){this.uh.Xl(),this.ar()}Ys(){return this.Pu}hc(t,i,s,n,e){this.Ct.Bs(t,i);let r=NaN,h=this.uh.q_(t,!0);const a=this.uh.ye();null!==a&&(h=Math.min(Math.max(a.Uh(),h),a.bi()));const l=n.ks(),o=l.zt();if(null!==o&&(r=l.Ts(i,o)),r=this.Iu.Ma(r,h,n),this.Ct.As(h,r,n),this.Zh(),!e){const e=Mi(n,t,i);this.Fu(e&&{Lo:e.Lo,No:e.No,Fo:e.Fo||null}),this.Ru.p(this.Ct.It(),{x:t,y:i},s)}}ac(t,i,s){const n=s.ks(),e=n.zt(),r=n.Nt(t,a(e)),h=this.uh.E_(i,!0),l=this.uh.jt(a(h));this.hc(l,r,null,s,!0)}lc(t){this.$u().Os(),this.Zh(),t||this.Ru.p(null,null,null)}Ah(){const t=this.Ct.Us();if(null!==t){const i=this.Ct.Is(),s=this.Ct.Vs();this.hc(i,s,null,t)}this.Ct.Ns()}oc(t,i,s){const n=this.uh.Rs(0);void 0!==i&&void 0!==s&&this.uh.Pt(i,s);const e=this.uh.Rs(0),r=this.uh.U_(),h=this.uh.ye();if(null!==h&&null!==n&&null!==e){const i=h.Te(r),a=this.$o.key(n)>this.$o.key(e),l=null!==t&&t>r&&!a,o=this.uh.N().allowShiftVisibleRangeOnWhitespaceReplacement,_=i&&(!(void 0===s)||o)&&this.uh.N().shiftVisibleRangeOnNewBar;if(l&&!_){const i=t-r;this.uh.fn(this.uh.Q_()-i)}}this.uh.nu(t)}Oh(t){null!==t&&t.Io()}Hn(t){if(function(t){return t instanceof vi}(t))return t;const i=this.yu.find((i=>i.Dt().includes(t)));return void 0===i?null:i}Z_(){this.yu.forEach((t=>t.Io())),this.Ah()}m(){this.yu.forEach((t=>t.m())),this.yu.length=0,this.ys.localization.priceFormatter=void 0,this.ys.localization.percentageFormatter=void 0,this.ys.localization.timeFormatter=void 0}_c(){return this.Eu}qn(){return this.Eu.N()}do(){return this.Tu}uc(t,i){const s=this.Vu(i);this.cc(t,s),this.Pu.push(t),1===this.Pu.length?this.Ih():this.ar()}dc(t){const i=this.Hn(t),s=this.Pu.indexOf(t);r(-1!==s,"Series not found");const n=a(i);this.Pu.splice(s,1),n.Fl(t),t.m&&t.m(),this.uh.k_(),this.fc(n)}Eh(t,i){const s=a(this.Hn(t));s.Fl(t,!0),s.Nl(t,i,!0)}pu(){const t=Y.wn();t.rn(),this.Lu(t)}vc(t){const i=Y.wn();i.ln(t),this.Lu(i)}cn(){const t=Y.wn();t.cn(),this.Lu(t)}dn(t){const i=Y.wn();i.dn(t),this.Lu(i)}fn(t){const i=Y.wn();i.fn(t),this.Lu(i)}_n(t){const i=Y.wn();i._n(t),this.Lu(i)}hn(){const t=Y.wn();t.hn(),this.Lu(t)}mc(){return this.ys.rightPriceScale.visible?"right":"left"}wc(t,i){r(i>=0,"Index should be greater or equal to 0");if(i===this.gc(t))return;const s=a(this.Hn(t));s.Fl(t);const n=this.Vu(i);this.cc(t,n),0===s.ba().length&&this.fc(s)}Mc(){return this.Ou}$(){return this.Au}Ut(t){const i=this.Ou,s=this.Au;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),null===this.Du||this.Du.mr!==s||this.Du.wr!==i)this.Du={mr:s,wr:i,bc:new Map};else{const i=this.Du.bc.get(t);if(void 0!==i)return i}const n=this.dl.tt(s,i,t/100);return this.Du.bc.set(t,n),n}xc(t){return this.yu.indexOf(t)}Xi(){return this.dl}Vu(t){if(r(t>=0,"Index should be greater or equal to 0"),(t=Math.min(this.yu.length,t))<this.yu.length)return this.yu[t];const i=new vi(this.uh,this);this.yu.push(i);const s=Y.gn();return s.Qs(t,{tn:0,sn:!0}),this.Lu(s),i}gc(t){return this.yu.findIndex((i=>i.wo().includes(t)))}Zu(t,i){const s=new Y(i);if(null!==t){const n=this.yu.indexOf(t);s.Qs(n,{tn:i})}return s}Nu(t,i){return void 0===i&&(i=2),this.Zu(this.Hn(t),i)}Lu(t){this.Bu&&this.Bu(t),this.yu.forEach((t=>t.zo().lr().Pt()))}cc(t,i){const s=t.N().priceScaleId,n=void 0!==s?s:this.mc();i.Nl(t,n),q(n)||t.hr(t.N())}zu(t){const i=this.ys.layout;return"gradient"===i.background.type?0===t?i.background.topColor:i.background.bottomColor:i.background.color}fc(t){0===t.ba().length&&this.yu.length>1&&(this.yu.splice(this.xc(t),1),this.Ih())}}function Vi(t){return!u(t)&&!d(t)}function Ai(t){return u(t)}!function(t){t[t.Disabled=0]="Disabled",t[t.Continuous=1]="Continuous",t[t.OnDataUpdate=2]="OnDataUpdate"}(Ri||(Ri={})),function(t){t[t.LastBar=0]="LastBar",t[t.LastVisible=1]="LastVisible"}(Di||(Di={})),function(t){t.Solid="solid",t.VerticalGradient="gradient"}(Bi||(Bi={})),function(t){t[t.Year=0]="Year",t[t.Month=1]="Month",t[t.DayOfMonth=2]="DayOfMonth",t[t.Time=3]="Time",t[t.TimeWithSeconds=4]="TimeWithSeconds"}(Ei||(Ei={}));const zi=t=>t.getUTCFullYear();function Oi(t,i,s){return i.replace(/yyyy/g,(t=>X(zi(t),4))(t)).replace(/yy/g,(t=>X(zi(t)%100,2))(t)).replace(/MMMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"long"}))(t,s)).replace(/MMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"short"}))(t,s)).replace(/MM/g,(t=>X((t=>t.getUTCMonth()+1)(t),2))(t)).replace(/dd/g,(t=>X((t=>t.getUTCDate())(t),2))(t))}class Li{constructor(t="yyyy-MM-dd",i="default"){this.Sc=t,this.Cc=i}jo(t){return Oi(t,this.Sc,this.Cc)}}class Ni{constructor(t){this.yc=t||"%h:%m:%s"}jo(t){return this.yc.replace("%h",X(t.getUTCHours(),2)).replace("%m",X(t.getUTCMinutes(),2)).replace("%s",X(t.getUTCSeconds(),2))}}const Wi={Pc:"yyyy-MM-dd",kc:"%h:%m:%s",Tc:" ",Rc:"default"};class Fi{constructor(t={}){const i={...Wi,...t};this.Dc=new Li(i.Pc,i.Rc),this.Bc=new Ni(i.kc),this.Ec=i.Tc}jo(t){return`${this.Dc.jo(t)}${this.Ec}${this.Bc.jo(t)}`}}function Hi(t){return 60*t*60*1e3}function Ui(t){return 60*t*1e3}const $i=[{Ic:(ji=1,1e3*ji),Vc:10},{Ic:Ui(1),Vc:20},{Ic:Ui(5),Vc:21},{Ic:Ui(30),Vc:22},{Ic:Hi(1),Vc:30},{Ic:Hi(3),Vc:31},{Ic:Hi(6),Vc:32},{Ic:Hi(12),Vc:33}];var ji;function qi(t,i){if(t.getUTCFullYear()!==i.getUTCFullYear())return 70;if(t.getUTCMonth()!==i.getUTCMonth())return 60;if(t.getUTCDate()!==i.getUTCDate())return 50;for(let s=$i.length-1;s>=0;--s)if(Math.floor(i.getTime()/$i[s].Ic)!==Math.floor(t.getTime()/$i[s].Ic))return $i[s].Vc;return 0}function Yi(t){let i=t;if(d(t)&&(i=Xi(t)),!Vi(i))throw new Error("time must be of type BusinessDay");const s=new Date(Date.UTC(i.year,i.month-1,i.day,0,0,0,0));return{Ac:Math.round(s.getTime()/1e3),zc:i}}function Ki(t){if(!Ai(t))throw new Error("time must be of type isUTCTimestamp");return{Ac:t}}function Xi(t){const i=new Date(t);if(isNaN(i.getTime()))throw new Error(`Invalid date string=${t}, expected format=yyyy-mm-dd`);return{day:i.getUTCDate(),month:i.getUTCMonth()+1,year:i.getUTCFullYear()}}function Zi(t){d(t.time)&&(t.time=Xi(t.time))}class Gi{options(){return this.ys}setOptions(t){this.ys=t,this.updateFormatter(t.localization)}preprocessData(t){Array.isArray(t)?function(t){t.forEach(Zi)}(t):Zi(t)}createConverterToInternalObj(t){return a(function(t){return 0===t.length?null:Vi(t[0].time)||d(t[0].time)?Yi:Ki}(t))}key(t){return"object"==typeof t&&"Ac"in t?t.Ac:this.key(this.convertHorzItemToInternal(t))}cacheKey(t){const i=t;return void 0===i.zc?new Date(1e3*i.Ac).getTime():new Date(Date.UTC(i.zc.year,i.zc.month-1,i.zc.day)).getTime()}convertHorzItemToInternal(t){return Ai(i=t)?Ki(i):Vi(i)?Yi(i):Yi(Xi(i));var i}updateFormatter(t){if(!this.ys)return;const i=t.dateFormat;this.ys.timeScale.timeVisible?this.Oc=new Fi({Pc:i,kc:this.ys.timeScale.secondsVisible?"%h:%m:%s":"%h:%m",Tc:"   ",Rc:t.locale}):this.Oc=new Li(i,t.locale)}formatHorzItem(t){const i=t;return this.Oc.jo(new Date(1e3*i.Ac))}formatTickmark(t,i){const s=function(t,i,s){switch(t){case 0:case 10:return i?s?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return i?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this.ys.timeScale.timeVisible,this.ys.timeScale.secondsVisible),n=this.ys.timeScale;if(void 0!==n.tickMarkFormatter){const e=n.tickMarkFormatter(t.originalTime,s,i.locale);if(null!==e)return e}return function(t,i,s){const n={};switch(i){case 0:n.year="numeric";break;case 1:n.month="short";break;case 2:n.day="numeric";break;case 3:n.hour12=!1,n.hour="2-digit",n.minute="2-digit";break;case 4:n.hour12=!1,n.hour="2-digit",n.minute="2-digit",n.second="2-digit"}const e=void 0===t.zc?new Date(1e3*t.Ac):new Date(Date.UTC(t.zc.year,t.zc.month-1,t.zc.day));return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()).toLocaleString(s,n)}(t.time,s,i.locale)}maxTickMarkWeight(t){let i=t.reduce(Pi,t[0]).weight;return i>30&&i<50&&(i=30),i}fillWeightsForPoints(t,i){!function(t,i=0){if(0===t.length)return;let s=0===i?null:t[i-1].time.Ac,n=null!==s?new Date(1e3*s):null,e=0;for(let r=i;r<t.length;++r){const i=t[r],h=new Date(1e3*i.time.Ac);null!==n&&(i.timeWeight=qi(h,n)),e+=i.time.Ac-(s||i.time.Ac),s=i.time.Ac,n=h}if(0===i&&t.length>1){const i=Math.ceil(e/(t.length-1)),s=new Date(1e3*(t[0].time.Ac-i));t[0].timeWeight=qi(new Date(1e3*t[0].time.Ac),s)}}(t,i)}static Lc(t){return _({localization:{dateFormat:"dd MMM 'yy"}},t??{})}}function Ji(t){var i=t.width,s=t.height;if(i<0)throw new Error("Negative width is not allowed for Size");if(s<0)throw new Error("Negative height is not allowed for Size");return{width:i,height:s}}function Qi(t,i){return t.width===i.width&&t.height===i.height}var ts=function(){function t(t){var i=this;this._resolutionListener=function(){return i._onResolutionChanged()},this._resolutionMediaQueryList=null,this._observers=[],this._window=t,this._installResolutionListener()}return t.prototype.dispose=function(){this._uninstallResolutionListener(),this._window=null},Object.defineProperty(t.prototype,"value",{get:function(){return this._window.devicePixelRatio},enumerable:!1,configurable:!0}),t.prototype.subscribe=function(t){var i=this,s={next:t};return this._observers.push(s),{unsubscribe:function(){i._observers=i._observers.filter((function(t){return t!==s}))}}},t.prototype._installResolutionListener=function(){if(null!==this._resolutionMediaQueryList)throw new Error("Resolution listener is already installed");var t=this._window.devicePixelRatio;this._resolutionMediaQueryList=this._window.matchMedia("all and (resolution: ".concat(t,"dppx)")),this._resolutionMediaQueryList.addListener(this._resolutionListener)},t.prototype._uninstallResolutionListener=function(){null!==this._resolutionMediaQueryList&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},t.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},t.prototype._onResolutionChanged=function(){var t=this;this._observers.forEach((function(i){return i.next(t._window.devicePixelRatio)})),this._reinstallResolutionListener()},t}();var is=function(){function t(t,i,s){var n;this._canvasElement=null,this._bitmapSizeChangedListeners=[],this._suggestedBitmapSize=null,this._suggestedBitmapSizeChangedListeners=[],this._devicePixelRatioObservable=null,this._canvasElementResizeObserver=null,this._canvasElement=t,this._canvasElementClientSize=Ji({width:this._canvasElement.clientWidth,height:this._canvasElement.clientHeight}),this._transformBitmapSize=null!=i?i:function(t){return t},this._allowResizeObserver=null===(n=null==s?void 0:s.allowResizeObserver)||void 0===n||n,this._chooseAndInitObserver()}return t.prototype.dispose=function(){var t,i;if(null===this._canvasElement)throw new Error("Object is disposed");null===(t=this._canvasElementResizeObserver)||void 0===t||t.disconnect(),this._canvasElementResizeObserver=null,null===(i=this._devicePixelRatioObservable)||void 0===i||i.dispose(),this._devicePixelRatioObservable=null,this._suggestedBitmapSizeChangedListeners.length=0,this._bitmapSizeChangedListeners.length=0,this._canvasElement=null},Object.defineProperty(t.prototype,"canvasElement",{get:function(){if(null===this._canvasElement)throw new Error("Object is disposed");return this._canvasElement},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canvasElementClientSize",{get:function(){return this._canvasElementClientSize},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"bitmapSize",{get:function(){return Ji({width:this.canvasElement.width,height:this.canvasElement.height})},enumerable:!1,configurable:!0}),t.prototype.resizeCanvasElement=function(t){this._canvasElementClientSize=Ji(t),this.canvasElement.style.width="".concat(this._canvasElementClientSize.width,"px"),this.canvasElement.style.height="".concat(this._canvasElementClientSize.height,"px"),this._invalidateBitmapSize()},t.prototype.subscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners.push(t)},t.prototype.unsubscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners=this._bitmapSizeChangedListeners.filter((function(i){return i!==t}))},Object.defineProperty(t.prototype,"suggestedBitmapSize",{get:function(){return this._suggestedBitmapSize},enumerable:!1,configurable:!0}),t.prototype.subscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners.push(t)},t.prototype.unsubscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners=this._suggestedBitmapSizeChangedListeners.filter((function(i){return i!==t}))},t.prototype.applySuggestedBitmapSize=function(){if(null!==this._suggestedBitmapSize){var t=this._suggestedBitmapSize;this._suggestedBitmapSize=null,this._resizeBitmap(t),this._emitSuggestedBitmapSizeChanged(t,this._suggestedBitmapSize)}},t.prototype._resizeBitmap=function(t){var i=this.bitmapSize;Qi(i,t)||(this.canvasElement.width=t.width,this.canvasElement.height=t.height,this._emitBitmapSizeChanged(i,t))},t.prototype._emitBitmapSizeChanged=function(t,i){var s=this;this._bitmapSizeChangedListeners.forEach((function(n){return n.call(s,t,i)}))},t.prototype._suggestNewBitmapSize=function(t){var i=this._suggestedBitmapSize,s=Ji(this._transformBitmapSize(t,this._canvasElementClientSize)),n=Qi(this.bitmapSize,s)?null:s;null===i&&null===n||null!==i&&null!==n&&Qi(i,n)||(this._suggestedBitmapSize=n,this._emitSuggestedBitmapSizeChanged(i,n))},t.prototype._emitSuggestedBitmapSizeChanged=function(t,i){var s=this;this._suggestedBitmapSizeChangedListeners.forEach((function(n){return n.call(s,t,i)}))},t.prototype._chooseAndInitObserver=function(){var t=this;this._allowResizeObserver?new Promise((function(t){var i=new ResizeObserver((function(s){t(s.every((function(t){return"devicePixelContentBoxSize"in t}))),i.disconnect()}));i.observe(document.body,{box:"device-pixel-content-box"})})).catch((function(){return!1})).then((function(i){return i?t._initResizeObserver():t._initDevicePixelRatioObservable()})):this._initDevicePixelRatioObservable()},t.prototype._initDevicePixelRatioObservable=function(){var t=this;if(null!==this._canvasElement){var i=ss(this._canvasElement);if(null===i)throw new Error("No window is associated with the canvas");this._devicePixelRatioObservable=function(t){return new ts(t)}(i),this._devicePixelRatioObservable.subscribe((function(){return t._invalidateBitmapSize()})),this._invalidateBitmapSize()}},t.prototype._invalidateBitmapSize=function(){var t,i;if(null!==this._canvasElement){var s=ss(this._canvasElement);if(null!==s){var n=null!==(i=null===(t=this._devicePixelRatioObservable)||void 0===t?void 0:t.value)&&void 0!==i?i:s.devicePixelRatio,e=this._canvasElement.getClientRects(),r=void 0!==e[0]?function(t,i){return Ji({width:Math.round(t.left*i+t.width*i)-Math.round(t.left*i),height:Math.round(t.top*i+t.height*i)-Math.round(t.top*i)})}(e[0],n):Ji({width:this._canvasElementClientSize.width*n,height:this._canvasElementClientSize.height*n});this._suggestNewBitmapSize(r)}}},t.prototype._initResizeObserver=function(){var t=this;null!==this._canvasElement&&(this._canvasElementResizeObserver=new ResizeObserver((function(i){var s=i.find((function(i){return i.target===t._canvasElement}));if(s&&s.devicePixelContentBoxSize&&s.devicePixelContentBoxSize[0]){var n=s.devicePixelContentBoxSize[0],e=Ji({width:n.inlineSize,height:n.blockSize});t._suggestNewBitmapSize(e)}})),this._canvasElementResizeObserver.observe(this._canvasElement,{box:"device-pixel-content-box"}))},t}();function ss(t){return t.ownerDocument.defaultView}var ns=function(){function t(t,i,s){if(0===i.width||0===i.height)throw new TypeError("Rendering target could only be created on a media with positive width and height");if(this._mediaSize=i,0===s.width||0===s.height)throw new TypeError("Rendering target could only be created using a bitmap with positive integer width and height");this._bitmapSize=s,this._context=t}return t.prototype.useMediaCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),this._context.scale(this._horizontalPixelRatio,this._verticalPixelRatio),t({context:this._context,mediaSize:this._mediaSize})}finally{this._context.restore()}},t.prototype.useBitmapCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),t({context:this._context,mediaSize:this._mediaSize,bitmapSize:this._bitmapSize,horizontalPixelRatio:this._horizontalPixelRatio,verticalPixelRatio:this._verticalPixelRatio})}finally{this._context.restore()}},Object.defineProperty(t.prototype,"_horizontalPixelRatio",{get:function(){return this._bitmapSize.width/this._mediaSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_verticalPixelRatio",{get:function(){return this._bitmapSize.height/this._mediaSize.height},enumerable:!1,configurable:!0}),t}();function es(t,i){var s=t.canvasElementClientSize;if(0===s.width||0===s.height)return null;var n=t.bitmapSize;if(0===n.width||0===n.height)return null;var e=t.canvasElement.getContext("2d",i);return null===e?null:new ns(e,s,n)}const rs="undefined"!=typeof window;function hs(){return!!rs&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function as(){return!!rs&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function ls(t){return t+t%2}function os(t){rs&&void 0!==window.chrome&&t.addEventListener("mousedown",(t=>{if(1===t.button)return t.preventDefault(),!1}))}class _s{constructor(t,i,s){this.Nc=0,this.Wc=null,this.Fc={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.Hc=0,this.Uc=null,this.$c={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY},this.jc=null,this.qc=!1,this.Yc=null,this.Kc=null,this.Xc=!1,this.Zc=!1,this.Gc=!1,this.Jc=null,this.Qc=null,this.td=null,this.sd=null,this.nd=null,this.ed=null,this.rd=null,this.hd=0,this.ad=!1,this.ld=!1,this.od=!1,this._d=0,this.ud=null,this.dd=!as(),this.fd=t=>{this.pd(t)},this.vd=t=>{if(this.md(t)){const i=this.wd(t);if(++this.Hc,this.Uc&&this.Hc>1){const{gd:s}=this.Md(ds(t),this.$c);s<30&&!this.Gc&&this.bd(i,this.Sd.xd),this.Cd()}}else{const i=this.wd(t);if(++this.Nc,this.Wc&&this.Nc>1){const{gd:s}=this.Md(ds(t),this.Fc);s<5&&!this.Zc&&this.yd(i,this.Sd.Pd),this.kd()}}},this.Td=t,this.Sd=i,this.ys=s,this.Rd()}m(){null!==this.Jc&&(this.Jc(),this.Jc=null),null!==this.Qc&&(this.Qc(),this.Qc=null),null!==this.sd&&(this.sd(),this.sd=null),null!==this.nd&&(this.nd(),this.nd=null),null!==this.ed&&(this.ed(),this.ed=null),null!==this.td&&(this.td(),this.td=null),this.Dd(),this.kd()}Bd(t){this.sd&&this.sd();const i=this.Ed.bind(this);if(this.sd=()=>{this.Td.removeEventListener("mousemove",i)},this.Td.addEventListener("mousemove",i),this.md(t))return;const s=this.wd(t);this.yd(s,this.Sd.Id),this.dd=!0}kd(){null!==this.Wc&&clearTimeout(this.Wc),this.Nc=0,this.Wc=null,this.Fc={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}Cd(){null!==this.Uc&&clearTimeout(this.Uc),this.Hc=0,this.Uc=null,this.$c={_t:Number.NEGATIVE_INFINITY,ut:Number.POSITIVE_INFINITY}}Ed(t){if(this.od||null!==this.Kc)return;if(this.md(t))return;const i=this.wd(t);this.yd(i,this.Sd.Vd),this.dd=!0}Ad(t){const i=ps(t.changedTouches,a(this.ud));if(null===i)return;if(this._d=fs(t),null!==this.rd)return;if(this.ld)return;this.ad=!0;const s=this.Md(ds(i),a(this.Kc)),{zd:n,Od:e,gd:r}=s;if(this.Xc||!(r<5)){if(!this.Xc){const t=.5*n,i=e>=t&&!this.ys.Ld(),s=t>e&&!this.ys.Nd();i||s||(this.ld=!0),this.Xc=!0,this.Gc=!0,this.Dd(),this.Cd()}if(!this.ld){const s=this.wd(t,i);this.bd(s,this.Sd.Wd),cs(t)}}}Fd(t){if(0!==t.button)return;const i=this.Md(ds(t),a(this.Yc)),{gd:s}=i;if(s>=5&&(this.Zc=!0,this.kd()),this.Zc){const i=this.wd(t);this.yd(i,this.Sd.Hd)}}Md(t,i){const s=Math.abs(i._t-t._t),n=Math.abs(i.ut-t.ut);return{zd:s,Od:n,gd:s+n}}Ud(t){let i=ps(t.changedTouches,a(this.ud));if(null===i&&0===t.touches.length&&(i=t.changedTouches[0]),null===i)return;this.ud=null,this._d=fs(t),this.Dd(),this.Kc=null,this.ed&&(this.ed(),this.ed=null);const s=this.wd(t,i);if(this.bd(s,this.Sd.$d),++this.Hc,this.Uc&&this.Hc>1){const{gd:t}=this.Md(ds(i),this.$c);t<30&&!this.Gc&&this.bd(s,this.Sd.xd),this.Cd()}else this.Gc||(this.bd(s,this.Sd.jd),this.Sd.jd&&cs(t));0===this.Hc&&cs(t),0===t.touches.length&&this.qc&&(this.qc=!1,cs(t))}pd(t){if(0!==t.button)return;const i=this.wd(t);if(this.Yc=null,this.od=!1,this.nd&&(this.nd(),this.nd=null),hs()){this.Td.ownerDocument.documentElement.removeEventListener("mouseleave",this.fd)}if(!this.md(t))if(this.yd(i,this.Sd.qd),++this.Nc,this.Wc&&this.Nc>1){const{gd:s}=this.Md(ds(t),this.Fc);s<5&&!this.Zc&&this.yd(i,this.Sd.Pd),this.kd()}else this.Zc||this.yd(i,this.Sd.Yd)}Dd(){null!==this.jc&&(clearTimeout(this.jc),this.jc=null)}Kd(t){if(null!==this.ud)return;const i=t.changedTouches[0];this.ud=i.identifier,this._d=fs(t);const s=this.Td.ownerDocument.documentElement;this.Gc=!1,this.Xc=!1,this.ld=!1,this.Kc=ds(i),this.ed&&(this.ed(),this.ed=null);{const i=this.Ad.bind(this),n=this.Ud.bind(this);this.ed=()=>{s.removeEventListener("touchmove",i),s.removeEventListener("touchend",n)},s.addEventListener("touchmove",i,{passive:!1}),s.addEventListener("touchend",n,{passive:!1}),this.Dd(),this.jc=setTimeout(this.Xd.bind(this,t),240)}const n=this.wd(t,i);this.bd(n,this.Sd.Zd),this.Uc||(this.Hc=0,this.Uc=setTimeout(this.Cd.bind(this),500),this.$c=ds(i))}Gd(t){if(0!==t.button)return;const i=this.Td.ownerDocument.documentElement;hs()&&i.addEventListener("mouseleave",this.fd),this.Zc=!1,this.Yc=ds(t),this.nd&&(this.nd(),this.nd=null);{const t=this.Fd.bind(this),s=this.pd.bind(this);this.nd=()=>{i.removeEventListener("mousemove",t),i.removeEventListener("mouseup",s)},i.addEventListener("mousemove",t),i.addEventListener("mouseup",s)}if(this.od=!0,this.md(t))return;const s=this.wd(t);this.yd(s,this.Sd.Jd),this.Wc||(this.Nc=0,this.Wc=setTimeout(this.kd.bind(this),500),this.Fc=ds(t))}Rd(){this.Td.addEventListener("mouseenter",this.Bd.bind(this)),this.Td.addEventListener("touchcancel",this.Dd.bind(this));{const t=this.Td.ownerDocument,i=t=>{this.Sd.Qd&&(t.composed&&this.Td.contains(t.composedPath()[0])||t.target&&this.Td.contains(t.target)||this.Sd.Qd())};this.Qc=()=>{t.removeEventListener("touchstart",i)},this.Jc=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}as()&&(this.td=()=>{this.Td.removeEventListener("dblclick",this.vd)},this.Td.addEventListener("dblclick",this.vd)),this.Td.addEventListener("mouseleave",this.tf.bind(this)),this.Td.addEventListener("touchstart",this.Kd.bind(this),{passive:!0}),os(this.Td),this.Td.addEventListener("mousedown",this.Gd.bind(this)),this.if(),this.Td.addEventListener("touchmove",(()=>{}),{passive:!1})}if(){void 0===this.Sd.sf&&void 0===this.Sd.nf&&void 0===this.Sd.ef||(this.Td.addEventListener("touchstart",(t=>this.rf(t.touches)),{passive:!0}),this.Td.addEventListener("touchmove",(t=>{if(2===t.touches.length&&null!==this.rd&&void 0!==this.Sd.nf){const i=us(t.touches[0],t.touches[1])/this.hd;this.Sd.nf(this.rd,i),cs(t)}}),{passive:!1}),this.Td.addEventListener("touchend",(t=>{this.rf(t.touches)})))}rf(t){1===t.length&&(this.ad=!1),2!==t.length||this.ad||this.qc?this.hf():this.af(t)}af(t){const i=this.Td.getBoundingClientRect()||{left:0,top:0};this.rd={_t:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,ut:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.hd=us(t[0],t[1]),void 0!==this.Sd.sf&&this.Sd.sf(),this.Dd()}hf(){null!==this.rd&&(this.rd=null,void 0!==this.Sd.ef&&this.Sd.ef())}tf(t){if(this.sd&&this.sd(),this.md(t))return;if(!this.dd)return;const i=this.wd(t);this.yd(i,this.Sd.lf),this.dd=!as()}Xd(t){const i=ps(t.touches,a(this.ud));if(null===i)return;const s=this.wd(t,i);this.bd(s,this.Sd._f),this.Gc=!0,this.qc=!0}md(t){return t.sourceCapabilities&&void 0!==t.sourceCapabilities.firesTouchEvents?t.sourceCapabilities.firesTouchEvents:fs(t)<this._d+500}bd(t,i){i&&i.call(this.Sd,t)}yd(t,i){i&&i.call(this.Sd,t)}wd(t,i){const s=i||t,n=this.Td.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-n.left,localY:s.clientY-n.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,uf:!t.type.startsWith("mouse")&&"contextmenu"!==t.type&&"click"!==t.type,cf:t.type,df:s.target,Ho:t.view,ff:()=>{"touchstart"!==t.type&&cs(t)}}}}function us(t,i){const s=t.clientX-i.clientX,n=t.clientY-i.clientY;return Math.sqrt(s*s+n*n)}function cs(t){t.cancelable&&t.preventDefault()}function ds(t){return{_t:t.pageX,ut:t.pageY}}function fs(t){return t.timeStamp||performance.now()}function ps(t,i){for(let s=0;s<t.length;++s)if(t[s].identifier===i)return t[s];return null}class vs{constructor(t,i,s){this.pf=null,this.vf=null,this.mf=!0,this.wf=null,this.gf=t,this.Mf=t.bf()[i],this.xf=t.bf()[s],this.Sf=document.createElement("tr"),this.Sf.style.height="1px",this.Cf=document.createElement("td"),this.Cf.style.position="relative",this.Cf.style.padding="0",this.Cf.style.margin="0",this.Cf.setAttribute("colspan","3"),this.yf(),this.Sf.appendChild(this.Cf),this.mf=this.gf.N().layout.panes.enableResize,this.mf?this.Pf():(this.pf=null,this.vf=null)}m(){null!==this.vf&&this.vf.m()}kf(){return this.Sf}Tf(){return Ji({width:this.Mf.Tf().width,height:1})}Rf(){return Ji({width:this.Mf.Rf().width,height:1*window.devicePixelRatio})}Df(t,i,s){const n=this.Rf();t.fillStyle=this.gf.N().layout.panes.separatorColor,t.fillRect(i,s,n.width,n.height)}Pt(){this.yf(),this.gf.N().layout.panes.enableResize!==this.mf&&(this.mf=this.gf.N().layout.panes.enableResize,this.mf?this.Pf():(null!==this.pf&&(this.Cf.removeChild(this.pf.Bf),this.Cf.removeChild(this.pf.Ef),this.pf=null),null!==this.vf&&(this.vf.m(),this.vf=null)))}Pf(){const t=document.createElement("div"),i=t.style;i.position="fixed",i.display="none",i.zIndex="49",i.top="0",i.left="0",i.width="100%",i.height="100%",i.cursor="row-resize",this.Cf.appendChild(t);const s=document.createElement("div"),n=s.style;n.position="absolute",n.zIndex="50",n.top="-4px",n.height="9px",n.width="100%",n.backgroundColor="",n.cursor="row-resize",this.Cf.appendChild(s);const e={Id:this.If.bind(this),lf:this.Vf.bind(this),Jd:this.Af.bind(this),Zd:this.Af.bind(this),Hd:this.zf.bind(this),Wd:this.zf.bind(this),qd:this.Of.bind(this),$d:this.Of.bind(this)};this.vf=new _s(s,e,{Ld:()=>!1,Nd:()=>!0}),this.pf={Ef:s,Bf:t}}yf(){this.Cf.style.background=this.gf.N().layout.panes.separatorColor}If(t){null!==this.pf&&(this.pf.Ef.style.backgroundColor=this.gf.N().layout.panes.separatorHoverColor)}Vf(t){null!==this.pf&&null===this.wf&&(this.pf.Ef.style.backgroundColor="")}Af(t){if(null===this.pf)return;const i=this.Mf.Lf().fo()+this.xf.Lf().fo(),s=i/(this.Mf.Tf().height+this.xf.Tf().height),n=30*s;i<=2*n||(this.wf={Nf:t.pageY,Wf:this.Mf.Lf().fo(),Ff:i-n,Hf:i,Uf:s,$f:n},this.pf.Bf.style.display="block")}zf(t){const i=this.wf;if(null===i)return;const s=(t.pageY-i.Nf)*i.Uf,n=qt(i.Wf+s,i.$f,i.Ff);this.Mf.Lf().po(n),this.xf.Lf().po(i.Hf-n),this.gf.Qt().Ih()}Of(t){null!==this.wf&&null!==this.pf&&(this.wf=null,this.pf.Bf.style.display="none")}}function ms(t,i){return t.jf-i.jf}function ws(t,i,s){const n=(t.jf-i.jf)/(t.wt-i.wt);return Math.sign(n)*Math.min(Math.abs(n),s)}class gs{constructor(t,i,s,n){this.qf=null,this.Yf=null,this.Kf=null,this.Xf=null,this.Zf=null,this.Gf=0,this.Jf=0,this.Qf=t,this.tp=i,this.ip=s,this.Mn=n}sp(t,i){if(null!==this.qf){if(this.qf.wt===i)return void(this.qf.jf=t);if(Math.abs(this.qf.jf-t)<this.Mn)return}this.Xf=this.Kf,this.Kf=this.Yf,this.Yf=this.qf,this.qf={wt:i,jf:t}}le(t,i){if(null===this.qf||null===this.Yf)return;if(i-this.qf.wt>50)return;let s=0;const n=ws(this.qf,this.Yf,this.tp),e=ms(this.qf,this.Yf),r=[n],h=[e];if(s+=e,null!==this.Kf){const t=ws(this.Yf,this.Kf,this.tp);if(Math.sign(t)===Math.sign(n)){const i=ms(this.Yf,this.Kf);if(r.push(t),h.push(i),s+=i,null!==this.Xf){const t=ws(this.Kf,this.Xf,this.tp);if(Math.sign(t)===Math.sign(n)){const i=ms(this.Kf,this.Xf);r.push(t),h.push(i),s+=i}}}}let a=0;for(let t=0;t<r.length;++t)a+=h[t]/s*r[t];Math.abs(a)<this.Qf||(this.Zf={jf:t,wt:i},this.Jf=a,this.Gf=function(t,i){const s=Math.log(i);return Math.log(1*s/-t)/s}(Math.abs(a),this.ip))}_u(t){const i=a(this.Zf),s=t-i.wt;return i.jf+this.Jf*(Math.pow(this.ip,s)-1)/Math.log(this.ip)}ou(t){return null===this.Zf||this.np(t)===this.Gf}np(t){const i=t-a(this.Zf).wt;return Math.min(i,this.Gf)}}class Ms{constructor(t,i){this.ep=void 0,this.rp=void 0,this.hp=void 0,this.ps=!1,this.ap=t,this.lp=i,this.op()}Pt(){this.op()}_p(){this.ep&&this.ap.removeChild(this.ep),this.rp&&this.ap.removeChild(this.rp),this.ep=void 0,this.rp=void 0}up(){return this.ps!==this.cp()||this.hp!==this.dp()}dp(){return this.lp.Qt().Xi().J(this.lp.N().layout.textColor)>160?"dark":"light"}cp(){return this.lp.N().layout.attributionLogo}fp(){const t=new URL(location.href);return t.hostname?"&utm_source="+t.hostname+t.pathname:""}op(){this.up()&&(this._p(),this.ps=this.cp(),this.ps&&(this.hp=this.dp(),this.rp=document.createElement("style"),this.rp.innerText="a#tv-attr-logo{--fill:#131722;--stroke:#fff;position:absolute;left:10px;bottom:10px;height:19px;width:35px;margin:0;padding:0;border:0;z-index:3;}a#tv-attr-logo[data-dark]{--fill:#D1D4DC;--stroke:#131722;}",this.ep=document.createElement("a"),this.ep.href=`https://www.tradingview.com/?utm_medium=lwc-link&utm_campaign=lwc-chart${this.fp()}`,this.ep.title="Charting by TradingView",this.ep.id="tv-attr-logo",this.ep.target="_blank",this.ep.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="35" height="19" fill="none"><g fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd"><path fill="var(--stroke)" d="M2 0H0v10h6v9h21.4l.5-1.3 6-15 1-2.7H23.7l-.5 1.3-.2.6a5 5 0 0 0-7-.9V0H2Zm20 17h4l5.2-13 .8-2h-7l-1 2.5-.2.5-1.5 3.8-.3.7V17Zm-.8-10a3 3 0 0 0 .7-2.7A3 3 0 1 0 16.8 7h4.4ZM14 7V2H2v6h6v9h4V7h2Z"/><path fill="var(--fill)" d="M14 2H2v6h6v9h6V2Zm12 15h-7l6-15h7l-6 15Zm-7-9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g><defs><clipPath id="a"><path fill="var(--stroke)" d="M0 0h35v19H0z"/></clipPath></defs></svg>',this.ep.toggleAttribute("data-dark","dark"===this.hp),this.ap.appendChild(this.rp),this.ap.appendChild(this.ep)))}}function bs(t,i){const s=a(t.ownerDocument).createElement("canvas");t.appendChild(s);const n=new is(s,(e={options:{allowResizeObserver:!0},transform:(t,i)=>({width:Math.max(t.width,i.width),height:Math.max(t.height,i.height)})}).transform,e.options);var e;return n.resizeCanvasElement(i),n}function xs(t){t.width=1,t.height=1,t.getContext("2d")?.clearRect(0,0,1,1)}function Ss(t,i,s,n){t.ih&&t.ih(i,s,n)}function Cs(t,i,s,n){t.nt(i,s,n)}function ys(t,i,s,n){const e=t(s,n);for(const t of e){const s=t.Tt(n);null!==s&&i(s)}}function Ps(t,i){return s=>{if(!function(t){return void 0!==t.Wt}(s))return[];return(s.Wt()?.wa()??"")!==i?[]:s.ta?.(t)??[]}}function ks(t,i,s,n){if(!t.length)return;let e=0;const r=t[0].$t(n,!0);let h=1===i?s/2-(t[0].Wi()-r/2):t[0].Wi()-r/2-s/2;h=Math.max(0,h);for(let r=1;r<t.length;r++){const a=t[r],l=t[r-1],o=l.$t(n,!1),_=a.Wi(),u=l.Wi();if(1===i?_>u-o:_<u+o){const n=u-o*i;a.Fi(n);const r=n-i*o/2;if((1===i?r<0:r>s)&&h>0){const n=1===i?-1-r:r-s,a=Math.min(n,h);for(let s=e;s<t.length;s++)t[s].Fi(t[s].Wi()+i*a);h-=a}}else e=r,h=1===i?u-o-_:_-(u+o)}}class Ts{constructor(t,i,s,n){this.qi=null,this.pp=null,this.vp=!1,this.mp=new tt(200),this.wp=null,this.gp=0,this.Mp=!1,this.bp=()=>{this.Mp||this.yt.xp().Qt().ar()},this.Sp=()=>{this.Mp||this.yt.xp().Qt().ar()},this.yt=t,this.ys=i,this.ul=i.layout,this.Eu=s,this.Cp="left"===n,this.yp=Ps("normal",n),this.Pp=Ps("top",n),this.kp=Ps("bottom",n),this.Cf=document.createElement("div"),this.Cf.style.height="100%",this.Cf.style.overflow="hidden",this.Cf.style.width="25px",this.Cf.style.left="0",this.Cf.style.position="relative",this.Tp=bs(this.Cf,Ji({width:16,height:16})),this.Tp.subscribeSuggestedBitmapSizeChanged(this.bp);const e=this.Tp.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this.Rp=bs(this.Cf,Ji({width:16,height:16})),this.Rp.subscribeSuggestedBitmapSizeChanged(this.Sp);const r=this.Rp.canvasElement;r.style.position="absolute",r.style.zIndex="2",r.style.left="0",r.style.top="0";const h={Jd:this.Af.bind(this),Zd:this.Af.bind(this),Hd:this.zf.bind(this),Wd:this.zf.bind(this),Qd:this.Dp.bind(this),qd:this.Of.bind(this),$d:this.Of.bind(this),Pd:this.Bp.bind(this),xd:this.Bp.bind(this),Id:this.Ep.bind(this),lf:this.Vf.bind(this)};this.vf=new _s(this.Rp.canvasElement,h,{Ld:()=>!this.ys.handleScroll.vertTouchDrag,Nd:()=>!0})}m(){this.vf.m(),this.Rp.unsubscribeSuggestedBitmapSizeChanged(this.Sp),xs(this.Rp.canvasElement),this.Rp.dispose(),this.Tp.unsubscribeSuggestedBitmapSizeChanged(this.bp),xs(this.Tp.canvasElement),this.Tp.dispose(),null!==this.qi&&this.qi.Ul().u(this),this.qi=null}kf(){return this.Cf}P(){return this.ul.fontSize}Ip(){const t=this.Eu.N();return this.wp!==t.k&&(this.mp.En(),this.wp=t.k),t}Vp(){if(null===this.qi)return 0;let t=0;const i=this.Ip(),s=a(this.Tp.canvasElement.getContext("2d",{colorSpace:this.yt.xp().N().layout.colorSpace}));s.save();const n=this.qi.Ba();s.font=this.Ap(),n.length>0&&(t=Math.max(this.mp.Bi(s,n[0].$a),this.mp.Bi(s,n[n.length-1].$a)));const e=this.zp();for(let i=e.length;i--;){const n=this.mp.Bi(s,e[i].ri());n>t&&(t=n)}const r=this.qi.zt();if(null!==r&&null!==this.pp&&(2!==(h=this.ys.crosshair).mode&&h.horzLine.visible&&h.horzLine.labelVisible)){const i=this.qi.Ts(1,r),n=this.qi.Ts(this.pp.height-2,r);t=Math.max(t,this.mp.Bi(s,this.qi.Zi(Math.floor(Math.min(i,n))+.11111111111111,r)),this.mp.Bi(s,this.qi.Zi(Math.ceil(Math.max(i,n))-.11111111111111,r)))}var h;s.restore();const l=t||34;return ls(Math.ceil(i.S+i.C+i.I+i.V+5+l))}Op(t){null!==this.pp&&Qi(this.pp,t)||(this.pp=t,this.Mp=!0,this.Tp.resizeCanvasElement(t),this.Rp.resizeCanvasElement(t),this.Mp=!1,this.Cf.style.width=`${t.width}px`,this.Cf.style.height=`${t.height}px`)}Lp(){return a(this.pp).width}_s(t){this.qi!==t&&(null!==this.qi&&this.qi.Ul().u(this),this.qi=t,t.Ul().i(this.il.bind(this),this))}Wt(){return this.qi}En(){const t=this.yt.Lf();this.yt.xp().Qt().Bo(t,a(this.Wt()))}Np(t){if(null===this.pp)return;const i={colorSpace:this.yt.xp().N().layout.colorSpace};if(1!==t){this.Wp(),this.Tp.applySuggestedBitmapSize();const t=es(this.Tp,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.Fp(t),this.Hp(t)})),this.yt.Up(t,this.kp),this.$p(t),this.yt.Up(t,this.yp),this.jp(t))}this.Rp.applySuggestedBitmapSize();const s=es(this.Rp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.qp(s),this.yt.Up(s,this.Pp))}Rf(){return this.Tp.bitmapSize}Df(t,i,s){const n=this.Rf();n.width>0&&n.height>0&&t.drawImage(this.Tp.canvasElement,i,s)}Pt(){this.qi?.Ba()}Af(t){if(null===this.qi||this.qi.Ki()||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.xp().Qt(),s=this.yt.Lf();this.vp=!0,i.Co(s,this.qi,t.localY)}zf(t){if(null===this.qi||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.xp().Qt(),s=this.yt.Lf(),n=this.qi;i.yo(s,n,t.localY)}Dp(){if(null===this.qi||!this.ys.handleScale.axisPressedMouseMove.price)return;const t=this.yt.xp().Qt(),i=this.yt.Lf(),s=this.qi;this.vp&&(this.vp=!1,t.Po(i,s))}Of(t){if(null===this.qi||!this.ys.handleScale.axisPressedMouseMove.price)return;const i=this.yt.xp().Qt(),s=this.yt.Lf();this.vp=!1,i.Po(s,this.qi)}Bp(t){this.ys.handleScale.axisDoubleClickReset.price&&this.En()}Ep(t){if(null===this.qi)return;!this.yt.xp().Qt().N().handleScale.axisPressedMouseMove.price||this.qi.Oe()||this.qi.Ml()||this.Yp(1)}Vf(t){this.Yp(0)}zp(){const t=[],i=null===this.qi?void 0:this.qi;return(s=>{for(let n=0;n<s.length;++n){const e=s[n].Fs(this.yt.Lf(),i);for(let i=0;i<e.length;i++)t.push(e[i])}})(this.yt.Lf().Dt()),t}Fp({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.yt.Lf().Qt(),r=e.$(),h=e.Mc();r===h?I(t,0,0,s,n,r):z(t,0,0,s,n,r,h)}Hp({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(null===this.pp||null===this.qi||!this.qi.N().borderVisible)return;t.fillStyle=this.qi.N().borderColor;const n=Math.max(1,Math.floor(this.Ip().S*s));let e;e=this.Cp?i.width-n:0,t.fillRect(e,0,n,i.height)}$p(t){if(null===this.pp||null===this.qi)return;const i=this.qi.Ba(),s=this.qi.N(),n=this.Ip(),e=this.Cp?this.pp.width-n.C:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:r,verticalPixelRatio:h})=>{t.fillStyle=s.borderColor;const a=Math.max(1,Math.floor(h)),l=Math.floor(.5*h),o=Math.round(n.C*r);t.beginPath();for(const s of i)t.rect(Math.floor(e*r),Math.round(s.Pa*h)-l,o,a);t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{t.font=this.Ap(),t.fillStyle=s.textColor??this.ul.textColor,t.textAlign=this.Cp?"right":"left",t.textBaseline="middle";const r=this.Cp?Math.round(e-n.I):Math.round(e+n.C+n.I),h=i.map((i=>this.mp.Di(t,i.$a)));for(let s=i.length;s--;){const n=i[s];t.fillText(n.$a,r,n.Pa+h[s])}}))}Wp(){if(null===this.pp||null===this.qi)return;let t=this.pp.height/2;const i=[],s=this.qi.Dt().slice(),n=this.yt.Lf(),e=this.Ip();this.qi===n.$n()&&this.yt.Lf().Dt().forEach((t=>{n.Un(t)&&s.push(t)}));const r=this.qi.ba()[0],h=this.qi;s.forEach((s=>{const e=s.Fs(n,h);e.forEach((t=>{t.Fi(null),t.Hi()&&i.push(t)})),r===s&&e.length>0&&(t=e[0].Ii())})),i.forEach((t=>t.Fi(t.Ii())));this.qi.N().alignLabels&&this.Kp(i,e,t)}Kp(t,i,s){if(null===this.pp)return;const n=t.filter((t=>t.Ii()<=s)),e=t.filter((t=>t.Ii()>s));n.sort(((t,i)=>i.Ii()-t.Ii())),n.length&&e.length&&e.push(n[0]),e.sort(((t,i)=>t.Ii()-i.Ii()));for(const s of t){const t=Math.floor(s.$t(i)/2),n=s.Ii();n>-t&&n<t&&s.Fi(t),n>this.pp.height-t&&n<this.pp.height+t&&s.Fi(this.pp.height-t)}ks(n,1,this.pp.height,i),ks(e,-1,this.pp.height,i)}jp(t){if(null===this.pp)return;const i=this.zp(),s=this.Ip(),n=this.Cp?"right":"left";i.forEach((i=>{if(i.Ui()){i.Tt(a(this.qi)).nt(t,s,this.mp,n)}}))}qp(t){if(null===this.pp||null===this.qi)return;const i=this.yt.xp().Qt(),s=[],n=this.yt.Lf(),e=i.$u().Fs(n,this.qi);e.length&&s.push(e);const r=this.Ip(),h=this.Cp?"right":"left";s.forEach((i=>{i.forEach((i=>{i.Tt(a(this.qi)).nt(t,r,this.mp,h)}))}))}Yp(t){this.Cf.style.cursor=1===t?"ns-resize":"default"}il(){const t=this.Vp();this.gp<t&&this.yt.xp().Qt().Ih(),this.gp=t}Ap(){return g(this.ul.fontSize,this.ul.fontFamily)}}function Rs(t,i){return t.Jh?.(i)??[]}function Ds(t,i){return t.Ws?.(i)??[]}function Bs(t,i){return t.us?.(i)??[]}function Es(t,i){return t.Xh?.(i)??[]}class Is{constructor(t,i){this.pp=Ji({width:0,height:0}),this.Xp=null,this.Zp=null,this.Gp=null,this.Jp=null,this.Qp=!1,this.tv=new o,this.iv=new o,this.sv=0,this.nv=!1,this.ev=null,this.rv=!1,this.hv=null,this.av=null,this.Mp=!1,this.bp=()=>{this.Mp||null===this.lv||this.ts().ar()},this.Sp=()=>{this.Mp||null===this.lv||this.ts().ar()},this.lp=t,this.lv=i,this.lv.Ao().i(this.ov.bind(this),this,!0),this._v=document.createElement("td"),this._v.style.padding="0",this._v.style.position="relative";const s=document.createElement("div");s.style.width="100%",s.style.height="100%",s.style.position="relative",s.style.overflow="hidden",this.uv=document.createElement("td"),this.uv.style.padding="0",this.cv=document.createElement("td"),this.cv.style.padding="0",this._v.appendChild(s),this.Tp=bs(s,Ji({width:16,height:16})),this.Tp.subscribeSuggestedBitmapSizeChanged(this.bp);const n=this.Tp.canvasElement;n.style.position="absolute",n.style.zIndex="1",n.style.left="0",n.style.top="0",this.Rp=bs(s,Ji({width:16,height:16})),this.Rp.subscribeSuggestedBitmapSizeChanged(this.Sp);const e=this.Rp.canvasElement;e.style.position="absolute",e.style.zIndex="2",e.style.left="0",e.style.top="0",this.Sf=document.createElement("tr"),this.Sf.appendChild(this.uv),this.Sf.appendChild(this._v),this.Sf.appendChild(this.cv),this.dv(),this.vf=new _s(this.Rp.canvasElement,this,{Ld:()=>null===this.ev&&!this.lp.N().handleScroll.vertTouchDrag,Nd:()=>null===this.ev&&!this.lp.N().handleScroll.horzTouchDrag})}m(){null!==this.Xp&&this.Xp.m(),null!==this.Zp&&this.Zp.m(),this.Gp=null,this.Rp.unsubscribeSuggestedBitmapSizeChanged(this.Sp),xs(this.Rp.canvasElement),this.Rp.dispose(),this.Tp.unsubscribeSuggestedBitmapSizeChanged(this.bp),xs(this.Tp.canvasElement),this.Tp.dispose(),null!==this.lv&&(this.lv.Ao().u(this),this.lv.m()),this.vf.m()}Lf(){return a(this.lv)}fv(t){null!==this.lv&&this.lv.Ao().u(this),this.lv=t,null!==this.lv&&this.lv.Ao().i(Is.prototype.ov.bind(this),this,!0),this.dv(),this.lp.bf().indexOf(this)===this.lp.bf().length-1?(this.Gp=this.Gp??new Ms(this._v,this.lp),this.Gp.Pt()):(this.Gp?._p(),this.Gp=null)}xp(){return this.lp}kf(){return this.Sf}dv(){if(null!==this.lv&&(this.pv(),0!==this.ts().Ys().length)){if(null!==this.Xp){const t=this.lv.xo();this.Xp._s(a(t))}if(null!==this.Zp){const t=this.lv.So();this.Zp._s(a(t))}}}vv(){null!==this.Xp&&this.Xp.Pt(),null!==this.Zp&&this.Zp.Pt()}fo(){return null!==this.lv?this.lv.fo():0}po(t){this.lv&&this.lv.po(t)}Id(t){if(!this.lv)return;this.mv();const i=t.localX,s=t.localY;this.wv(i,s,t)}Jd(t){this.mv(),this.gv(),this.wv(t.localX,t.localY,t)}Vd(t){if(!this.lv)return;this.mv();const i=t.localX,s=t.localY;this.wv(i,s,t)}Yd(t){null!==this.lv&&(this.mv(),this.Mv(t))}Pd(t){null!==this.lv&&this.bv(this.iv,t)}xd(t){this.Pd(t)}Hd(t){this.mv(),this.xv(t),this.wv(t.localX,t.localY,t)}qd(t){null!==this.lv&&(this.mv(),this.nv=!1,this.Sv(t))}jd(t){null!==this.lv&&this.Mv(t)}_f(t){if(this.nv=!0,null===this.ev){const i={x:t.localX,y:t.localY};this.Cv(i,i,t)}}lf(t){null!==this.lv&&(this.mv(),this.lv.Qt().Fu(null),this.yv())}Pv(){return this.tv}kv(){return this.iv}sf(){this.sv=1,this.ts().hn()}nf(t,i){if(!this.lp.N().handleScale.pinch)return;const s=5*(i-this.sv);this.sv=i,this.ts().Ju(t._t,s)}Zd(t){this.nv=!1,this.rv=null!==this.ev,this.gv();const i=this.ts().$u();null!==this.ev&&i.Bt()&&(this.hv={x:i.si(),y:i.ni()},this.ev={x:t.localX,y:t.localY})}Wd(t){if(null===this.lv)return;const i=t.localX,s=t.localY;if(null===this.ev)this.xv(t);else{this.rv=!1;const n=a(this.hv),e=n.x+(i-this.ev.x),r=n.y+(s-this.ev.y);this.wv(e,r,t)}}$d(t){0===this.xp().N().trackingMode.exitMode&&(this.rv=!0),this.Tv(),this.Sv(t)}Yn(t,i){const s=this.lv;return null===s?null:Mi(s,t,i)}Rv(t,i){a("left"===i?this.Xp:this.Zp).Op(Ji({width:t,height:this.pp.height}))}Tf(){return this.pp}Op(t){Qi(this.pp,t)||(this.pp=t,this.Mp=!0,this.Tp.resizeCanvasElement(t),this.Rp.resizeCanvasElement(t),this.Mp=!1,this._v.style.width=t.width+"px",this._v.style.height=t.height+"px")}Dv(){const t=a(this.lv);t.bo(t.xo()),t.bo(t.So());for(const i of t.ba())if(t.Un(i)){const s=i.Wt();null!==s&&t.bo(s),i.Ns()}for(const i of t.Oo())i.Ns()}Rf(){return this.Tp.bitmapSize}Df(t,i,s){const n=this.Rf();n.width>0&&n.height>0&&t.drawImage(this.Tp.canvasElement,i,s)}Np(t){if(0===t)return;if(null===this.lv)return;t>1&&this.Dv(),null!==this.Xp&&this.Xp.Np(t),null!==this.Zp&&this.Zp.Np(t);const i={colorSpace:this.lp.N().layout.colorSpace};if(1!==t){this.Tp.applySuggestedBitmapSize();const t=es(this.Tp,i);null!==t&&(t.useBitmapCoordinateSpace((t=>{this.Fp(t)})),this.lv&&(this.Bv(t,Rs),this.Ev(t),this.Bv(t,Ds),this.Bv(t,Bs)))}this.Rp.applySuggestedBitmapSize();const s=es(this.Rp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.Iv(s),this.Bv(s,Es),this.Bv(s,Bs))}Vv(){return this.Xp}Av(){return this.Zp}Up(t,i){this.Bv(t,i)}ov(){null!==this.lv&&this.lv.Ao().u(this),this.lv=null}Mv(t){this.bv(this.tv,t)}bv(t,i){const s=i.localX,n=i.localY;t.v()&&t.p(this.ts().Vt().q_(s),{x:s,y:n},i)}Fp({context:t,bitmapSize:i}){const{width:s,height:n}=i,e=this.ts(),r=e.$(),h=e.Mc();r===h?I(t,0,0,s,n,h):z(t,0,0,s,n,r,h)}Ev(t){const i=a(this.lv),s=i.zo().lr().Tt(i);null!==s&&s.nt(t,!1)}Iv(t){this.zv(t,Ds,Cs,this.ts().$u())}Bv(t,i){const s=a(this.lv),n=s.Dt(),e=s.Oo();for(const s of e)this.zv(t,i,Ss,s);for(const s of n)this.zv(t,i,Ss,s);for(const s of e)this.zv(t,i,Cs,s);for(const s of n)this.zv(t,i,Cs,s)}zv(t,i,s,n){const e=a(this.lv),r=e.Qt().Wu(),h=null!==r&&r.Lo===n,l=null!==r&&h&&void 0!==r.No?r.No.Xn:void 0;ys(i,(i=>s(i,t,h,l)),n,e)}pv(){if(null===this.lv)return;const t=this.lp,i=this.lv.xo().N().visible,s=this.lv.So().N().visible;i||null===this.Xp||(this.uv.removeChild(this.Xp.kf()),this.Xp.m(),this.Xp=null),s||null===this.Zp||(this.cv.removeChild(this.Zp.kf()),this.Zp.m(),this.Zp=null);const n=t.Qt()._c();i&&null===this.Xp&&(this.Xp=new Ts(this,t.N(),n,"left"),this.uv.appendChild(this.Xp.kf())),s&&null===this.Zp&&(this.Zp=new Ts(this,t.N(),n,"right"),this.cv.appendChild(this.Zp.kf()))}Ov(t){return t.uf&&this.nv||null!==this.ev}Lv(t){return Math.max(0,Math.min(t,this.pp.width-1))}Nv(t){return Math.max(0,Math.min(t,this.pp.height-1))}wv(t,i,s){this.ts().hc(this.Lv(t),this.Nv(i),s,a(this.lv))}yv(){this.ts().lc()}Tv(){this.rv&&(this.ev=null,this.yv())}Cv(t,i,s){this.ev=t,this.rv=!1,this.wv(i.x,i.y,s);const n=this.ts().$u();this.hv={x:n.si(),y:n.ni()}}ts(){return this.lp.Qt()}Sv(t){if(!this.Qp)return;const i=this.ts(),s=this.Lf();if(i.Ro(s,s.ks()),this.Jp=null,this.Qp=!1,i.nc(),null!==this.av){const t=performance.now(),s=i.Vt();this.av.le(s.Q_(),t),this.av.ou(t)||i._n(this.av)}}mv(){this.ev=null}gv(){if(!this.lv)return;if(this.ts().hn(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)a(document.activeElement).blur();else{const t=document.getSelection();null!==t&&t.removeAllRanges()}!this.lv.ks().Ki()&&this.ts().Vt().Ki()}xv(t){if(null===this.lv)return;const i=this.ts(),s=i.Vt();if(s.Ki())return;const n=this.lp.N(),e=n.handleScroll,r=n.kineticScroll;if((!e.pressedMouseMove||t.uf)&&(!e.horzTouchDrag&&!e.vertTouchDrag||!t.uf))return;const h=this.lv.ks(),a=performance.now();if(null!==this.Jp||this.Ov(t)||(this.Jp={x:t.clientX,y:t.clientY,Ac:a,Wv:t.localX,Fv:t.localY}),null!==this.Jp&&!this.Qp&&(this.Jp.x!==t.clientX||this.Jp.y!==t.clientY)){if(t.uf&&r.touch||!t.uf&&r.mouse){const t=s.G_();this.av=new gs(.2/t,7/t,.997,15/t),this.av.sp(s.Q_(),this.Jp.Ac)}else this.av=null;h.Ki()||i.ko(this.lv,h,t.localY),i.tc(t.localX),this.Qp=!0}this.Qp&&(h.Ki()||i.To(this.lv,h,t.localY),i.sc(t.localX),null!==this.av&&this.av.sp(s.Q_(),a))}}class Vs{constructor(t,i,s,n,e){this.St=!0,this.pp=Ji({width:0,height:0}),this.bp=()=>this.Np(3),this.Cp="left"===t,this.Eu=s._c,this.ys=i,this.Hv=n,this.Uv=e,this.Cf=document.createElement("div"),this.Cf.style.width="25px",this.Cf.style.height="100%",this.Cf.style.overflow="hidden",this.Tp=bs(this.Cf,Ji({width:16,height:16})),this.Tp.subscribeSuggestedBitmapSizeChanged(this.bp)}m(){this.Tp.unsubscribeSuggestedBitmapSizeChanged(this.bp),xs(this.Tp.canvasElement),this.Tp.dispose()}kf(){return this.Cf}Tf(){return this.pp}Op(t){Qi(this.pp,t)||(this.pp=t,this.Tp.resizeCanvasElement(t),this.Cf.style.width=`${t.width}px`,this.Cf.style.height=`${t.height}px`,this.St=!0)}Np(t){if(t<3&&!this.St)return;if(0===this.pp.width||0===this.pp.height)return;this.St=!1,this.Tp.applySuggestedBitmapSize();const i=es(this.Tp,{colorSpace:this.ys.layout.colorSpace});null!==i&&i.useBitmapCoordinateSpace((t=>{this.Fp(t),this.Hp(t)}))}Rf(){return this.Tp.bitmapSize}Df(t,i,s){const n=this.Rf();n.width>0&&n.height>0&&t.drawImage(this.Tp.canvasElement,i,s)}Hp({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:n}){if(!this.Hv())return;t.fillStyle=this.ys.timeScale.borderColor;const e=Math.floor(this.Eu.N().S*s),r=Math.floor(this.Eu.N().S*n),h=this.Cp?i.width-e:0;t.fillRect(h,0,e,r)}Fp({context:t,bitmapSize:i}){I(t,0,0,i.width,i.height,this.Uv())}}function As(t){return i=>i.ia?.(t)??[]}const zs=As("normal"),Os=As("top"),Ls=As("bottom");class Ns{constructor(t,i){this.$v=null,this.jv=null,this.M=null,this.qv=!1,this.pp=Ji({width:0,height:0}),this.Yv=new o,this.mp=new tt(5),this.Mp=!1,this.bp=()=>{this.Mp||this.lp.Qt().ar()},this.Sp=()=>{this.Mp||this.lp.Qt().ar()},this.lp=t,this.$o=i,this.ys=t.N().layout,this.ep=document.createElement("tr"),this.Kv=document.createElement("td"),this.Kv.style.padding="0",this.Xv=document.createElement("td"),this.Xv.style.padding="0",this.Cf=document.createElement("td"),this.Cf.style.height="25px",this.Cf.style.padding="0",this.Zv=document.createElement("div"),this.Zv.style.width="100%",this.Zv.style.height="100%",this.Zv.style.position="relative",this.Zv.style.overflow="hidden",this.Cf.appendChild(this.Zv),this.Tp=bs(this.Zv,Ji({width:16,height:16})),this.Tp.subscribeSuggestedBitmapSizeChanged(this.bp);const s=this.Tp.canvasElement;s.style.position="absolute",s.style.zIndex="1",s.style.left="0",s.style.top="0",this.Rp=bs(this.Zv,Ji({width:16,height:16})),this.Rp.subscribeSuggestedBitmapSizeChanged(this.Sp);const n=this.Rp.canvasElement;n.style.position="absolute",n.style.zIndex="2",n.style.left="0",n.style.top="0",this.ep.appendChild(this.Kv),this.ep.appendChild(this.Cf),this.ep.appendChild(this.Xv),this.Gv(),this.lp.Qt().do().i(this.Gv.bind(this),this),this.vf=new _s(this.Rp.canvasElement,this,{Ld:()=>!0,Nd:()=>!this.lp.N().handleScroll.horzTouchDrag})}m(){this.vf.m(),null!==this.$v&&this.$v.m(),null!==this.jv&&this.jv.m(),this.Rp.unsubscribeSuggestedBitmapSizeChanged(this.Sp),xs(this.Rp.canvasElement),this.Rp.dispose(),this.Tp.unsubscribeSuggestedBitmapSizeChanged(this.bp),xs(this.Tp.canvasElement),this.Tp.dispose()}kf(){return this.ep}Jv(){return this.$v}Qv(){return this.jv}Jd(t){if(this.qv)return;this.qv=!0;const i=this.lp.Qt();!i.Vt().Ki()&&this.lp.N().handleScale.axisPressedMouseMove.time&&i.Gu(t.localX)}Zd(t){this.Jd(t)}Qd(){const t=this.lp.Qt();!t.Vt().Ki()&&this.qv&&(this.qv=!1,this.lp.N().handleScale.axisPressedMouseMove.time&&t.rc())}Hd(t){const i=this.lp.Qt();!i.Vt().Ki()&&this.lp.N().handleScale.axisPressedMouseMove.time&&i.ec(t.localX)}Wd(t){this.Hd(t)}qd(){this.qv=!1;const t=this.lp.Qt();t.Vt().Ki()&&!this.lp.N().handleScale.axisPressedMouseMove.time||t.rc()}$d(){this.qd()}Pd(){this.lp.N().handleScale.axisDoubleClickReset.time&&this.lp.Qt().cn()}xd(){this.Pd()}Id(){this.lp.Qt().N().handleScale.axisPressedMouseMove.time&&this.Yp(1)}lf(){this.Yp(0)}Tf(){return this.pp}tm(){return this.Yv}im(t,i,s){Qi(this.pp,t)||(this.pp=t,this.Mp=!0,this.Tp.resizeCanvasElement(t),this.Rp.resizeCanvasElement(t),this.Mp=!1,this.Cf.style.width=`${t.width}px`,this.Cf.style.height=`${t.height}px`,this.Yv.p(t)),null!==this.$v&&this.$v.Op(Ji({width:i,height:t.height})),null!==this.jv&&this.jv.Op(Ji({width:s,height:t.height}))}sm(){const t=this.nm();return Math.ceil(t.S+t.C+t.P+t.A+t.B+t.rm)}Pt(){this.lp.Qt().Vt().Ba()}Rf(){return this.Tp.bitmapSize}Df(t,i,s){const n=this.Rf();n.width>0&&n.height>0&&t.drawImage(this.Tp.canvasElement,i,s)}Np(t){if(0===t)return;const i={colorSpace:this.ys.colorSpace};if(1!==t){this.Tp.applySuggestedBitmapSize();const s=es(this.Tp,i);null!==s&&(s.useBitmapCoordinateSpace((t=>{this.Fp(t),this.Hp(t),this.hm(s,Ls)})),this.$p(s),this.hm(s,zs)),null!==this.$v&&this.$v.Np(t),null!==this.jv&&this.jv.Np(t)}this.Rp.applySuggestedBitmapSize();const s=es(this.Rp,i);null!==s&&(s.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this.am([...this.lp.Qt().Ys(),this.lp.Qt().$u()],s),this.hm(s,Os))}hm(t,i){const s=this.lp.Qt().Ys();for(const n of s)ys(i,(i=>Ss(i,t,!1,void 0)),n,void 0);for(const n of s)ys(i,(i=>Cs(i,t,!1,void 0)),n,void 0)}Fp({context:t,bitmapSize:i}){I(t,0,0,i.width,i.height,this.lp.Qt().Mc())}Hp({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this.lp.N().timeScale.borderVisible){t.fillStyle=this.lm();const n=Math.max(1,Math.floor(this.nm().S*s));t.fillRect(0,0,i.width,n)}}$p(t){const i=this.lp.Qt().Vt(),s=i.Ba();if(!s||0===s.length)return;const n=this.$o.maxTickMarkWeight(s),e=this.nm(),r=i.N();r.borderVisible&&r.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:i,verticalPixelRatio:n})=>{t.strokeStyle=this.lm(),t.fillStyle=this.lm();const r=Math.max(1,Math.floor(i)),h=Math.floor(.5*i);t.beginPath();const a=Math.round(e.C*n);for(let n=s.length;n--;){const e=Math.round(s[n].coord*i);t.rect(e-h,0,r,a)}t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{const i=e.S+e.C+e.A+e.P/2;t.textAlign="center",t.textBaseline="middle",t.fillStyle=this.H(),t.font=this.Ap();for(const e of s)if(e.weight<n){const s=e.needAlignCoordinate?this.om(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}this.lp.N().timeScale.allowBoldLabels&&(t.font=this._m());for(const e of s)if(e.weight>=n){const s=e.needAlignCoordinate?this.om(t,e.coord,e.label):e.coord;t.fillText(e.label,s,i)}}))}om(t,i,s){const n=this.mp.Bi(t,s),e=n/2,r=Math.floor(i-e)+.5;return r<0?i+=Math.abs(0-r):r+n>this.pp.width&&(i-=Math.abs(this.pp.width-(r+n))),i}am(t,i){const s=this.nm();for(const n of t)for(const t of n.cs())t.Tt().nt(i,s)}lm(){return this.lp.N().timeScale.borderColor}H(){return this.ys.textColor}W(){return this.ys.fontSize}Ap(){return g(this.W(),this.ys.fontFamily)}_m(){return g(this.W(),this.ys.fontFamily,"bold")}nm(){null===this.M&&(this.M={S:1,O:NaN,A:NaN,B:NaN,Ji:NaN,C:5,P:NaN,k:"",Gi:new tt,rm:0});const t=this.M,i=this.Ap();if(t.k!==i){const s=this.W();t.P=s,t.k=i,t.A=3*s/12,t.B=3*s/12,t.Ji=9*s/12,t.O=0,t.rm=4*s/12,t.Gi.En()}return this.M}Yp(t){this.Cf.style.cursor=1===t?"ew-resize":"default"}Gv(){const t=this.lp.Qt(),i=t.N();i.leftPriceScale.visible||null===this.$v||(this.Kv.removeChild(this.$v.kf()),this.$v.m(),this.$v=null),i.rightPriceScale.visible||null===this.jv||(this.Xv.removeChild(this.jv.kf()),this.jv.m(),this.jv=null);const s={_c:this.lp.Qt()._c()},n=()=>i.leftPriceScale.borderVisible&&t.Vt().N().borderVisible,e=()=>t.Mc();i.leftPriceScale.visible&&null===this.$v&&(this.$v=new Vs("left",i,s,n,e),this.Kv.appendChild(this.$v.kf())),i.rightPriceScale.visible&&null===this.jv&&(this.jv=new Vs("right",i,s,n,e),this.Xv.appendChild(this.jv.kf()))}}const Ws=!!rs&&!!navigator.userAgentData&&navigator.userAgentData.brands.some((t=>t.brand.includes("Chromium")))&&!!rs&&(navigator?.userAgentData?.platform?"Windows"===navigator.userAgentData.platform:navigator.userAgent.toLowerCase().indexOf("win")>=0);class Fs{constructor(t,i,s){var n;this.um=[],this.dm=[],this.fm=0,this.Ya=0,this.no=0,this.pm=0,this.vm=0,this.wm=null,this.gm=!1,this.tv=new o,this.iv=new o,this.Ru=new o,this.Mm=null,this.bm=null,this.ap=t,this.ys=i,this.$o=s,this.ep=document.createElement("div"),this.ep.classList.add("tv-lightweight-charts"),this.ep.style.overflow="hidden",this.ep.style.direction="ltr",this.ep.style.width="100%",this.ep.style.height="100%",(n=this.ep).style.userSelect="none",n.style.webkitUserSelect="none",n.style.msUserSelect="none",n.style.MozUserSelect="none",n.style.webkitTapHighlightColor="transparent",this.xm=document.createElement("table"),this.xm.setAttribute("cellspacing","0"),this.ep.appendChild(this.xm),this.Sm=this.Cm.bind(this),Hs(this.ys)&&this.ym(!0),this.ts=new Ii(this.Bu.bind(this),this.ys,s),this.Qt().ju().i(this.Pm.bind(this),this),this.km=new Ns(this,this.$o),this.xm.appendChild(this.km.kf());const e=i.autoSize&&this.Tm();let r=this.ys.width,h=this.ys.height;if(e||0===r||0===h){const i=t.getBoundingClientRect();r=r||i.width,h=h||i.height}this.Rm(r,h),this.Dm(),t.appendChild(this.ep),this.Bm(),this.ts.Vt().du().i(this.ts.Ih.bind(this.ts),this),this.ts.do().i(this.ts.Ih.bind(this.ts),this)}Qt(){return this.ts}N(){return this.ys}bf(){return this.um}Em(){return this.km}m(){this.ym(!1),0!==this.fm&&window.cancelAnimationFrame(this.fm),this.ts.ju().u(this),this.ts.Vt().du().u(this),this.ts.do().u(this),this.ts.m();for(const t of this.um)this.xm.removeChild(t.kf()),t.Pv().u(this),t.kv().u(this),t.m();this.um=[];for(const t of this.dm)this.Im(t);this.dm=[],a(this.km).m(),null!==this.ep.parentElement&&this.ep.parentElement.removeChild(this.ep),this.Ru.m(),this.tv.m(),this.iv.m(),this.Vm()}Rm(t,i,s=!1){if(this.Ya===i&&this.no===t)return;const n=function(t){const i=Math.floor(t.width),s=Math.floor(t.height);return Ji({width:i-i%2,height:s-s%2})}(Ji({width:t,height:i}));this.Ya=n.height,this.no=n.width;const e=this.Ya+"px",r=this.no+"px";a(this.ep).style.height=e,a(this.ep).style.width=r,this.xm.style.height=e,this.xm.style.width=r,s?this.Am(Y.gn(),performance.now()):this.ts.Ih()}Np(t){void 0===t&&(t=Y.gn());for(let i=0;i<this.um.length;i++)this.um[i].Np(t.en(i).tn);this.ys.timeScale.visible&&this.km.Np(t.nn())}hr(t){const i=Hs(this.ys);this.ts.hr(t);const s=Hs(this.ys);s!==i&&this.ym(s),t.layout?.panes&&this.zm(),this.Bm(),this.Om(t)}Pv(){return this.tv}kv(){return this.iv}ju(){return this.Ru}Lm(){null!==this.wm&&(this.Am(this.wm,performance.now()),this.wm=null);const t=this.Nm(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=a(i.getContext("2d"));return this.Nm(s),i}Wm(t){if("left"===t&&!this.Fm())return 0;if("right"===t&&!this.Hm())return 0;if(0===this.um.length)return 0;return a("left"===t?this.um[0].Vv():this.um[0].Av()).Lp()}Um(){return this.ys.autoSize&&null!==this.Mm}Ef(){return this.ep}$m(t){this.bm=t,this.bm?this.Ef().style.setProperty("cursor",t):this.Ef().style.removeProperty("cursor")}jm(){return this.bm}qm(t){return h(this.um[t]).Tf()}zm(){this.dm.forEach((t=>{t.Pt()}))}Om(t){(void 0!==t.autoSize||!this.Mm||void 0===t.width&&void 0===t.height)&&(t.autoSize&&!this.Mm&&this.Tm(),!1===t.autoSize&&null!==this.Mm&&this.Vm(),t.autoSize||void 0===t.width&&void 0===t.height||this.Rm(t.width||this.no,t.height||this.Ya))}Nm(t){let i=0,s=0;const n=this.um[0],e=(i,s)=>{let n=0;for(let e=0;e<this.um.length;e++){const r=this.um[e],h=a("left"===i?r.Vv():r.Av()),l=h.Rf();if(null!==t&&h.Df(t,s,n),n+=l.height,e<this.um.length-1){const i=this.dm[e],r=i.Rf();null!==t&&i.Df(t,s,n),n+=r.height}}};if(this.Fm()){e("left",0);i+=a(n.Vv()).Rf().width}for(let n=0;n<this.um.length;n++){const e=this.um[n],r=e.Rf();if(null!==t&&e.Df(t,i,s),s+=r.height,n<this.um.length-1){const e=this.dm[n],r=e.Rf();null!==t&&e.Df(t,i,s),s+=r.height}}if(i+=n.Rf().width,this.Hm()){e("right",i);i+=a(n.Av()).Rf().width}const r=(i,s,n)=>{a("left"===i?this.km.Jv():this.km.Qv()).Df(a(t),s,n)};if(this.ys.timeScale.visible){const i=this.km.Rf();if(null!==t){let e=0;this.Fm()&&(r("left",e,s),e=a(n.Vv()).Rf().width),this.km.Df(t,e,s),e+=i.width,this.Hm()&&r("right",e,s)}s+=i.height}return Ji({width:i,height:s})}Ym(){let t=0,i=0,s=0;for(const n of this.um)this.Fm()&&(i=Math.max(i,a(n.Vv()).Vp(),this.ys.leftPriceScale.minimumWidth)),this.Hm()&&(s=Math.max(s,a(n.Av()).Vp(),this.ys.rightPriceScale.minimumWidth)),t+=n.fo();i=ls(i),s=ls(s);const n=this.no,e=this.Ya,r=Math.max(n-i-s,0),h=1*this.dm.length,l=this.ys.timeScale.visible;let o=l?Math.max(this.km.sm(),this.ys.timeScale.minimumHeight):0;var _;o=(_=o)+_%2;const u=h+o,c=e<u?0:e-u,d=c/t;let f=0;const p=window.devicePixelRatio||1;for(let t=0;t<this.um.length;++t){const n=this.um[t];n.fv(this.ts.$s()[t]);let e=0,h=0;h=t===this.um.length-1?Math.ceil((c-f)*p)/p:Math.round(n.fo()*d*p)/p,e=Math.max(h,2),f+=e,n.Op(Ji({width:r,height:e})),this.Fm()&&n.Rv(i,"left"),this.Hm()&&n.Rv(s,"right"),n.Lf()&&this.ts.qu(n.Lf(),e)}this.km.im(Ji({width:l?r:0,height:o}),l?i:0,l?s:0),this.ts.vo(r),this.pm!==i&&(this.pm=i),this.vm!==s&&(this.vm=s)}ym(t){t?this.ep.addEventListener("wheel",this.Sm,{passive:!1}):this.ep.removeEventListener("wheel",this.Sm)}Km(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return Ws?1/window.devicePixelRatio:1}Cm(t){if(!(0!==t.deltaX&&this.ys.handleScroll.mouseWheel||0!==t.deltaY&&this.ys.handleScale.mouseWheel))return;const i=this.Km(t),s=i*t.deltaX/100,n=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),0!==n&&this.ys.handleScale.mouseWheel){const i=Math.sign(n)*Math.min(1,Math.abs(n)),s=t.clientX-this.ep.getBoundingClientRect().left;this.Qt().Ju(s,i)}0!==s&&this.ys.handleScroll.mouseWheel&&this.Qt().Qu(-80*s)}Am(t,i){const s=t.nn();3===s&&this.Xm(),3!==s&&2!==s||(this.Zm(t),this.Gm(t,i),this.km.Pt(),this.um.forEach((t=>{t.vv()})),3===this.wm?.nn()&&(this.wm.vn(t),this.Xm(),this.Zm(this.wm),this.Gm(this.wm,i),t=this.wm,this.wm=null)),this.Np(t)}Gm(t,i){for(const s of t.pn())this.mn(s,i)}Zm(t){const i=this.ts.$s();for(let s=0;s<i.length;s++)t.en(s).sn&&i[s].Eo()}mn(t,i){const s=this.ts.Vt();switch(t.an){case 0:s.pu();break;case 1:s.vu(t.Ft);break;case 2:s.dn(t.Ft);break;case 3:s.fn(t.Ft);break;case 4:s.su();break;case 5:t.Ft.ou(i)||s.fn(t.Ft._u(i))}}Bu(t){null!==this.wm?this.wm.vn(t):this.wm=t,this.gm||(this.gm=!0,this.fm=window.requestAnimationFrame((t=>{if(this.gm=!1,this.fm=0,null!==this.wm){const i=this.wm;this.wm=null,this.Am(i,t);for(const s of i.pn())if(5===s.an&&!s.Ft.ou(t)){this.Qt()._n(s.Ft);break}}})))}Xm(){this.Dm()}Im(t){this.xm.removeChild(t.kf()),t.m()}Dm(){const t=this.ts.$s(),i=t.length,s=this.um.length;for(let t=i;t<s;t++){const t=h(this.um.pop());this.xm.removeChild(t.kf()),t.Pv().u(this),t.kv().u(this),t.m();const i=this.dm.pop();void 0!==i&&this.Im(i)}for(let n=s;n<i;n++){const i=new Is(this,t[n]);if(i.Pv().i(this.Jm.bind(this,i),this),i.kv().i(this.Qm.bind(this,i),this),this.um.push(i),n>0){const t=new vs(this,n-1,n);this.dm.push(t),this.xm.insertBefore(t.kf(),this.km.kf())}this.xm.insertBefore(i.kf(),this.km.kf())}for(let s=0;s<i;s++){const i=t[s],n=this.um[s];n.Lf()!==i?n.fv(i):n.dv()}this.Bm(),this.Ym()}tw(t,i,s,n){const e=new Map;if(null!==t){this.ts.Ys().forEach((i=>{const s=i.Xs().Wr(t);null!==s&&e.set(i,s)}))}let r;if(null!==t){const i=this.ts.Vt().ss(t)?.originalTime;void 0!==i&&(r=i)}const h=this.Qt().Wu(),a=null!==h&&h.Lo instanceof Ht?h.Lo:void 0,l=null!==h&&void 0!==h.No?h.No.Kn:void 0,o=this.iw(n);return{sw:r,Re:t??void 0,nw:i??void 0,ew:-1!==o?o:void 0,rw:a,hw:e,aw:l,lw:s??void 0}}iw(t){let i=-1;if(t)i=this.um.indexOf(t);else{const t=this.Qt().$u().Us();null!==t&&(i=this.Qt().$s().indexOf(t))}return i}Jm(t,i,s,n){this.tv.p((()=>this.tw(i,s,n,t)))}Qm(t,i,s,n){this.iv.p((()=>this.tw(i,s,n,t)))}Pm(t,i,s){this.$m(this.Qt().Wu()?.Fo??null),this.Ru.p((()=>this.tw(t,i,s)))}Bm(){const t=this.ys.timeScale.visible?"":"none";this.km.kf().style.display=t}Fm(){return this.um[0].Lf().xo().N().visible}Hm(){return this.um[0].Lf().So().N().visible}Tm(){return"ResizeObserver"in window&&(this.Mm=new ResizeObserver((t=>{const i=t[t.length-1];i&&this.Rm(i.contentRect.width,i.contentRect.height)})),this.Mm.observe(this.ap,{box:"border-box"}),!0)}Vm(){null!==this.Mm&&this.Mm.disconnect(),this.Mm=null}}function Hs(t){return Boolean(t.handleScroll.mouseWheel||t.handleScale.mouseWheel)}function Us(t){return void 0===t.open&&void 0===t.value}function $s(t){return function(t){return void 0!==t.open}(t)||function(t){return void 0!==t.value}(t)}function js(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],sw:n};return void 0!==s.color&&(r.R=s.color),r}function qs(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],sw:n};return void 0!==s.lineColor&&(r.vt=s.lineColor),void 0!==s.topColor&&(r.mr=s.topColor),void 0!==s.bottomColor&&(r.wr=s.bottomColor),r}function Ys(t,i,s,n){const e=s.value,r={Re:i,wt:t,Ft:[e,e,e,e],sw:n};return void 0!==s.topLineColor&&(r.gr=s.topLineColor),void 0!==s.bottomLineColor&&(r.Mr=s.bottomLineColor),void 0!==s.topFillColor1&&(r.br=s.topFillColor1),void 0!==s.topFillColor2&&(r.Sr=s.topFillColor2),void 0!==s.bottomFillColor1&&(r.Cr=s.bottomFillColor1),void 0!==s.bottomFillColor2&&(r.yr=s.bottomFillColor2),r}function Ks(t,i,s,n){const e={Re:i,wt:t,Ft:[s.open,s.high,s.low,s.close],sw:n};return void 0!==s.color&&(e.R=s.color),e}function Xs(t,i,s,n){const e={Re:i,wt:t,Ft:[s.open,s.high,s.low,s.close],sw:n};return void 0!==s.color&&(e.R=s.color),void 0!==s.borderColor&&(e.Ht=s.borderColor),void 0!==s.wickColor&&(e.vr=s.wickColor),e}function Zs(t,i,s,n,e){const r=h(e)(s),a=Math.max(...r),l=Math.min(...r),o=r[r.length-1],_=[o,a,l,o],{time:u,color:c,...d}=s;return{Re:i,wt:t,Ft:_,sw:n,se:d,R:c}}function Gs(t){return void 0!==t.Ft}function Js(t,i){return void 0!==i.customValues&&(t.ow=i.customValues),t}function Qs(t){return(i,s,n,e,r,h)=>function(t,i){return i?i(t):Us(t)}(n,h)?Js({wt:i,Re:s,sw:e},n):Js(t(i,s,n,e,r),n)}function tn(t){return{Candlestick:Qs(Xs),Bar:Qs(Ks),Area:Qs(qs),Baseline:Qs(Ys),Histogram:Qs(js),Line:Qs(js),Custom:Qs(Zs)}[t]}function sn(t){return{Re:0,_w:new Map,Hh:t}}function nn(t,i){if(void 0!==t&&0!==t.length)return{uw:i.key(t[0].wt),cw:i.key(t[t.length-1].wt)}}function en(t){let i;return t.forEach((t=>{void 0===i&&(i=t.sw)})),h(i)}class rn{constructor(t){this.dw=new Map,this.fw=new Map,this.pw=new Map,this.mw=[],this.$o=t}m(){this.dw.clear(),this.fw.clear(),this.pw.clear(),this.mw=[]}ww(t,i){let s=0!==this.dw.size,n=!1;const e=this.fw.get(t);if(void 0!==e)if(1===this.fw.size)s=!1,n=!0,this.dw.clear();else for(const i of this.mw)i.pointData._w.delete(t)&&(n=!0);let r=[];if(0!==i.length){const s=i.map((t=>t.time)),e=this.$o.createConverterToInternalObj(i),h=tn(t.Rr()),a=t.da(),l=t.pa();r=i.map(((i,r)=>{const o=e(i.time),_=this.$o.key(o);let u=this.dw.get(_);void 0===u&&(u=sn(o),this.dw.set(_,u),n=!0);const c=h(o,u.Re,i,s[r],a,l);return u._w.set(t,c),c}))}s&&this.gw(),this.Mw(t,r);let h=-1;if(n){const t=[];this.dw.forEach((i=>{t.push({timeWeight:0,time:i.Hh,pointData:i,originalTime:en(i._w)})})),t.sort(((t,i)=>this.$o.key(t.time)-this.$o.key(i.time))),h=this.bw(t)}return this.xw(t,h,function(t,i,s){const n=nn(t,s),e=nn(i,s);if(void 0!==n&&void 0!==e)return{Sw:!1,zh:n.cw>=e.cw&&n.uw>=e.uw}}(this.fw.get(t),e,this.$o))}dc(t){return this.ww(t,[])}Cw(t,i,s){const n=i;!function(t){void 0===t.sw&&(t.sw=t.time)}(n),this.$o.preprocessData(i);const e=this.$o.createConverterToInternalObj([i])(i.time),r=this.pw.get(t);if(!s&&void 0!==r&&this.$o.key(e)<this.$o.key(r))throw new Error(`Cannot update oldest data, last time=${r}, new time=${e}`);let h=this.dw.get(this.$o.key(e));if(s&&void 0===h)throw new Error("Cannot update non-existing data point when historicalUpdate is true");const a=void 0===h;void 0===h&&(h=sn(e),this.dw.set(this.$o.key(e),h));const l=tn(t.Rr()),o=t.da(),_=t.pa(),u=l(e,h.Re,i,n.sw,o,_);h._w.set(t,u),s?this.yw(t,u,h.Re):this.Pw(t,u);const c={zh:Gs(u),Sw:s};if(!a)return this.xw(t,-1,c);const d={timeWeight:0,time:h.Hh,pointData:h,originalTime:en(h._w)},f=bt(this.mw,this.$o.key(d.time),((t,i)=>this.$o.key(t.time)<i));this.mw.splice(f,0,d);for(let t=f;t<this.mw.length;++t)hn(this.mw[t].pointData,t);return this.$o.fillWeightsForPoints(this.mw,f),this.xw(t,f,c)}Pw(t,i){let s=this.fw.get(t);void 0===s&&(s=[],this.fw.set(t,s));const n=0!==s.length?s[s.length-1]:null;null===n||this.$o.key(i.wt)>this.$o.key(n.wt)?Gs(i)&&s.push(i):Gs(i)?s[s.length-1]=i:s.splice(-1,1),this.pw.set(t,i.wt)}yw(t,i,s){const n=this.fw.get(t);if(void 0===n)return;const e=bt(n,s,((t,i)=>t.Re<i));Gs(i)?n[e]=i:n.splice(e,1)}Mw(t,i){0!==i.length?(this.fw.set(t,i.filter(Gs)),this.pw.set(t,i[i.length-1].wt)):(this.fw.delete(t),this.pw.delete(t))}gw(){for(const t of this.mw)0===t.pointData._w.size&&this.dw.delete(this.$o.key(t.time))}bw(t){let i=-1;for(let s=0;s<this.mw.length&&s<t.length;++s){const n=this.mw[s],e=t[s];if(this.$o.key(n.time)!==this.$o.key(e.time)){i=s;break}e.timeWeight=n.timeWeight,hn(e.pointData,s)}if(-1===i&&this.mw.length!==t.length&&(i=Math.min(this.mw.length,t.length)),-1===i)return-1;for(let s=i;s<t.length;++s)hn(t[s].pointData,s);return this.$o.fillWeightsForPoints(t,i),this.mw=t,i}kw(){if(0===this.fw.size)return null;let t=0;return this.fw.forEach((i=>{0!==i.length&&(t=Math.max(t,i[i.length-1].Re))})),t}xw(t,i,s){const n={wo:new Map,Vt:{U_:this.kw()}};if(-1!==i)this.fw.forEach(((i,e)=>{n.wo.set(e,{se:i,Tw:e===t?s:void 0})})),this.fw.has(t)||n.wo.set(t,{se:[],Tw:s}),n.Vt.Rw=this.mw,n.Vt.Dw=i;else{const i=this.fw.get(t);n.wo.set(t,{se:i||[],Tw:s})}return n}}function hn(t,i){t.Re=i,t._w.forEach((t=>{t.Re=i}))}function an(t,i){return t.wt<i}function ln(t,i){return i<t.wt}function on(t,i,s){const n=i.Uh(),e=i.bi(),r=bt(t,n,an),h=xt(t,e,ln);if(!s)return{from:r,to:h};let a=r,l=h;return r>0&&r<t.length&&t[r].wt>=n&&(a=r-1),h>0&&h<t.length&&t[h-1].wt<=e&&(l=h+1),{from:a,to:l}}class _n{constructor(t,i,s){this.Bw=!0,this.Ew=!0,this.Iw=!0,this.Vw=[],this.Aw=null,this.Jn=t,this.Qn=i,this.zw=s}Pt(t){this.Bw=!0,"data"===t&&(this.Ew=!0),"options"===t&&(this.Iw=!0)}Tt(){return this.Jn.Bt()?(this.Ow(),null===this.Aw?null:this.Lw):null}Nw(){this.Vw=this.Vw.map((t=>({...t,...this.Jn.Rh().Dr(t.wt)})))}Ww(){this.Aw=null}Ow(){this.Ew&&(this.Fw(),this.Ew=!1),this.Iw&&(this.Nw(),this.Iw=!1),this.Bw&&(this.Hw(),this.Bw=!1)}Hw(){const t=this.Jn.Wt(),i=this.Qn.Vt();if(this.Ww(),i.Ki()||t.Ki())return;const s=i.ye();if(null===s)return;if(0===this.Jn.Xs().zr())return;const n=this.Jn.zt();null!==n&&(this.Aw=on(this.Vw,s,this.zw),this.Uw(t,i,n.Ft),this.$w())}}class un{constructor(t,i){this.jw=t,this.qi=i}nt(t,i,s){this.jw.draw(t,this.qi,i,s)}}class cn extends _n{constructor(t,i,s){super(t,i,!1),this.sh=s,this.Lw=new un(this.sh.renderer(),(i=>{const s=t.zt();return null===s?null:t.Wt().Nt(i,s.Ft)}))}fa(t){return this.sh.priceValueBuilder(t)}va(t){return this.sh.isWhitespace(t)}Fw(){const t=this.Jn.Rh();this.Vw=this.Jn.Xs().Hr().map((i=>({wt:i.Re,_t:NaN,...t.Dr(i.Re),qw:i.se})))}Uw(t,i){i.j_(this.Vw,m(this.Aw))}$w(){this.sh.update({bars:this.Vw.map(dn),barSpacing:this.Qn.Vt().G_(),visibleRange:this.Aw},this.Jn.N())}}function dn(t){return{x:t._t,time:t.wt,originalData:t.qw,barColor:t.cr}}const fn={color:"#2196f3"},pn=(t,i,s)=>{const n=l(s);return new cn(t,i,n)};function vn(t){const i={value:t.Ft[3],time:t.sw};return void 0!==t.ow&&(i.customValues=t.ow),i}function mn(t){const i=vn(t);return void 0!==t.R&&(i.color=t.R),i}function wn(t){const i=vn(t);return void 0!==t.vt&&(i.lineColor=t.vt),void 0!==t.mr&&(i.topColor=t.mr),void 0!==t.wr&&(i.bottomColor=t.wr),i}function gn(t){const i=vn(t);return void 0!==t.gr&&(i.topLineColor=t.gr),void 0!==t.Mr&&(i.bottomLineColor=t.Mr),void 0!==t.br&&(i.topFillColor1=t.br),void 0!==t.Sr&&(i.topFillColor2=t.Sr),void 0!==t.Cr&&(i.bottomFillColor1=t.Cr),void 0!==t.yr&&(i.bottomFillColor2=t.yr),i}function Mn(t){const i={open:t.Ft[0],high:t.Ft[1],low:t.Ft[2],close:t.Ft[3],time:t.sw};return void 0!==t.ow&&(i.customValues=t.ow),i}function bn(t){const i=Mn(t);return void 0!==t.R&&(i.color=t.R),i}function xn(t){const i=Mn(t),{R:s,Ht:n,vr:e}=t;return void 0!==s&&(i.color=s),void 0!==n&&(i.borderColor=n),void 0!==e&&(i.wickColor=e),i}function Sn(t){return{Area:wn,Line:mn,Baseline:gn,Histogram:mn,Bar:bn,Candlestick:xn,Custom:Cn}[t]}function Cn(t){const i=t.sw;return{...t.se,time:i}}const yn={vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},Pn={vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},kn={background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:w,panes:{enableResize:!0,separatorColor:"#E0E3EB",separatorHoverColor:"rgba(178, 181, 189, 0.2)"},attributionLogo:!0,colorSpace:"srgb",colorParsers:[]},Tn={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2},minimumWidth:0},Rn={rightOffset:0,barSpacing:6,minBarSpacing:.5,maxBarSpacing:0,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,allowShiftVisibleRangeOnWhitespaceReplacement:!1,ticksVisible:!1,uniformDistribution:!1,minimumHeight:0,allowBoldLabels:!0,ignoreWhitespaceIndices:!1};function Dn(){return{width:0,height:0,autoSize:!1,layout:kn,crosshair:yn,grid:Pn,overlayPriceScales:{...Tn},leftPriceScale:{...Tn,visible:!1},rightPriceScale:{...Tn,visible:!0},timeScale:Rn,localization:{locale:rs?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}}}class Bn{constructor(t,i,s){this.gf=t,this.Yw=i,this.Kw=s??0}applyOptions(t){this.gf.Qt().Hu(this.Yw,t,this.Kw)}options(){return this.qi().N()}width(){return q(this.Yw)?this.gf.Wm(this.Yw):0}qi(){return a(this.gf.Qt().Uu(this.Yw,this.Kw)).Wt}}class En{constructor(t,i,s,n){this.gf=t,this.yt=s,this.Xw=i,this.Zw=n}getHeight(){return this.yt.$t()}setHeight(t){const i=this.gf.Qt(),s=i.xc(this.yt);i.Ku(s,t)}paneIndex(){return this.gf.Qt().xc(this.yt)}moveTo(t){const i=this.paneIndex();i!==t&&(r(t>=0&&t<this.gf.bf().length,"Invalid pane index"),this.gf.Qt().Xu(i,t))}getSeries(){return this.yt.wo().map((t=>this.Xw(t)))??[]}getHTMLElement(){return this.gf.bf()[this.paneIndex()].kf()}attachPrimitive(t){this.yt.ua(t),t.attached&&t.attached({chart:this.Zw,requestUpdate:()=>this.yt.Qt().Ih()})}detachPrimitive(t){this.yt.ca(t)}priceScale(t){if(null===this.yt.co(t))throw new Error(`Cannot find price scale with id: ${t}`);return new Bn(this.gf,t,this.paneIndex())}}const In={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class Vn{constructor(t){this.ir=t}applyOptions(t){this.ir.hr(t)}options(){return this.ir.N()}Gw(){return this.ir}}class An{constructor(t,i,s,n,e,r){this.Jw=new o,this.Jn=t,this.Qw=i,this.tg=s,this.$o=e,this.Zw=n,this.ig=r}m(){this.Jw.m()}priceFormatter(){return this.Jn.ra()}priceToCoordinate(t){const i=this.Jn.zt();return null===i?null:this.Jn.Wt().Nt(t,i.Ft)}coordinateToPrice(t){const i=this.Jn.zt();return null===i?null:this.Jn.Wt().Ts(t,i.Ft)}barsInLogicalRange(t){if(null===t)return null;const i=new yi(new xi(t.from,t.to)).a_(),s=this.Jn.Xs();if(s.Ki())return null;const n=s.Wr(i.Uh(),1),e=s.Wr(i.bi(),-1),r=a(s.Or()),h=a(s.Ks());if(null!==n&&null!==e&&n.Re>e.Re)return{barsBefore:t.from-r,barsAfter:h-t.to};const l={barsBefore:null===n||n.Re===r?t.from-r:n.Re-r,barsAfter:null===e||e.Re===h?h-t.to:h-e.Re};return null!==n&&null!==e&&(l.from=n.sw,l.to=e.sw),l}setData(t){this.$o,this.Jn.Rr(),this.Qw.sg(this.Jn,t),this.ng("full")}update(t,i=!1){this.Jn.Rr(),this.Qw.eg(this.Jn,t,i),this.ng("update")}dataByIndex(t,i){const s=this.Jn.Xs().Wr(t,i);if(null===s)return null;return Sn(this.seriesType())(s)}data(){const t=Sn(this.seriesType());return this.Jn.Xs().Hr().map((i=>t(i)))}subscribeDataChanged(t){this.Jw.i(t)}unsubscribeDataChanged(t){this.Jw._(t)}applyOptions(t){this.Jn.hr(t)}options(){return p(this.Jn.N())}priceScale(){return this.tg.priceScale(this.Jn.Wt().wa(),this.getPane().paneIndex())}createPriceLine(t){const i=_(p(In),t),s=this.Jn.Lh(i);return new Vn(s)}removePriceLine(t){this.Jn.Nh(t.Gw())}priceLines(){return this.Jn.Wh().map((t=>new Vn(t)))}seriesType(){return this.Jn.Rr()}attachPrimitive(t){this.Jn.ua(t),t.attached&&t.attached({chart:this.Zw,series:this,requestUpdate:()=>this.Jn.Qt().Ih(),horzScaleBehavior:this.$o})}detachPrimitive(t){this.Jn.ca(t),t.detached&&t.detached(),this.Jn.Qt().Ih()}getPane(){const t=this.Jn,i=a(this.Jn.Qt().Hn(t));return this.ig(i)}moveToPane(t){this.Jn.Qt().wc(this.Jn,t)}seriesOrder(){const t=this.Jn.Qt().Hn(this.Jn);return null===t?-1:t.wo().indexOf(this.Jn)}setSeriesOrder(t){const i=this.Jn.Qt().Hn(this.Jn);null!==i&&i.Vo(this.Jn,t)}ng(t){this.Jw.v()&&this.Jw.p(t)}}class zn{constructor(t,i,s){this.rg=new o,this.m_=new o,this.Yv=new o,this.ts=t,this.uh=t.Vt(),this.km=i,this.uh.uu().i(this.hg.bind(this)),this.uh.cu().i(this.ag.bind(this)),this.km.tm().i(this.lg.bind(this)),this.$o=s}m(){this.uh.uu().u(this),this.uh.cu().u(this),this.km.tm().u(this),this.rg.m(),this.m_.m(),this.Yv.m()}scrollPosition(){return this.uh.Q_()}scrollToPosition(t,i){i?this.uh.lu(t,1e3):this.ts.fn(t)}scrollToRealTime(){this.uh.au()}getVisibleRange(){const t=this.uh.z_();return null===t?null:{from:t.from.originalTime,to:t.to.originalTime}}setVisibleRange(t){const i={from:this.$o.convertHorzItemToInternal(t.from),to:this.$o.convertHorzItemToInternal(t.to)},s=this.uh.W_(i);this.ts.vc(s)}getVisibleLogicalRange(){const t=this.uh.A_();return null===t?null:{from:t.Uh(),to:t.bi()}}setVisibleLogicalRange(t){r(t.from<=t.to,"The from index cannot be after the to index."),this.ts.vc(t)}resetTimeScale(){this.ts.cn()}fitContent(){this.ts.pu()}logicalToCoordinate(t){const i=this.ts.Vt();return i.Ki()?null:i.jt(t)}coordinateToLogical(t){return this.uh.Ki()?null:this.uh.q_(t)}timeToIndex(t,i){const s=this.$o.convertHorzItemToInternal(t);return this.uh.E_(s,i)}timeToCoordinate(t){const i=this.timeToIndex(t,!1);return null===i?null:this.uh.jt(i)}coordinateToTime(t){const i=this.ts.Vt(),s=i.q_(t),n=i.ss(s);return null===n?null:n.originalTime}width(){return this.km.Tf().width}height(){return this.km.Tf().height}subscribeVisibleTimeRangeChange(t){this.rg.i(t)}unsubscribeVisibleTimeRangeChange(t){this.rg._(t)}subscribeVisibleLogicalRangeChange(t){this.m_.i(t)}unsubscribeVisibleLogicalRangeChange(t){this.m_._(t)}subscribeSizeChange(t){this.Yv.i(t)}unsubscribeSizeChange(t){this.Yv._(t)}applyOptions(t){this.uh.hr(t)}options(){return{...p(this.uh.N()),barSpacing:this.uh.G_()}}hg(){this.rg.v()&&this.rg.p(this.getVisibleRange())}ag(){this.m_.v()&&this.m_.p(this.getVisibleLogicalRange())}lg(t){this.Yv.p(t.width,t.height)}}function On(t){if(void 0===t||"custom"===t.type)return;const i=t;void 0!==i.minMove&&void 0===i.precision&&(i.precision=function(t){if(t>=1)return 0;let i=0;for(;i<8;i++){const s=Math.round(t);if(Math.abs(s-t)<1e-8)return i;t*=10}return i}(i.minMove))}function Ln(t){return function(t){if(f(t.handleScale)){const i=t.handleScale;t.handleScale={axisDoubleClickReset:{time:i,price:i},axisPressedMouseMove:{time:i,price:i},mouseWheel:i,pinch:i}}else if(void 0!==t.handleScale){const{axisPressedMouseMove:i,axisDoubleClickReset:s}=t.handleScale;f(i)&&(t.handleScale.axisPressedMouseMove={time:i,price:i}),f(s)&&(t.handleScale.axisDoubleClickReset={time:s,price:s})}const i=t.handleScroll;f(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(t),t}class Nn{constructor(t,i,s){this.og=new Map,this._g=new Map,this.ug=new o,this.cg=new o,this.dg=new o,this.yu=new WeakMap,this.fg=new rn(i);const n=void 0===s?p(Dn()):_(p(Dn()),Ln(s));this.pg=i,this.gf=new Fs(t,n,i),this.gf.Pv().i((t=>{this.ug.v()&&this.ug.p(this.vg(t()))}),this),this.gf.kv().i((t=>{this.cg.v()&&this.cg.p(this.vg(t()))}),this),this.gf.ju().i((t=>{this.dg.v()&&this.dg.p(this.vg(t()))}),this);const e=this.gf.Qt();this.mg=new zn(e,this.gf.Em(),this.pg)}remove(){this.gf.Pv().u(this),this.gf.kv().u(this),this.gf.ju().u(this),this.mg.m(),this.gf.m(),this.og.clear(),this._g.clear(),this.ug.m(),this.cg.m(),this.dg.m(),this.fg.m()}resize(t,i,s){this.autoSizeActive()||this.gf.Rm(t,i,s)}addCustomSeries(t,i={},s=0){const n=(t=>({type:"Custom",isBuiltIn:!1,defaultOptions:{...fn,...t.defaultOptions()},wg:pn,gg:t}))(l(t));return this.Mg(n,i,s)}addSeries(t,i={},s=0){return this.Mg(t,i,s)}removeSeries(t){const i=h(this.og.get(t)),s=this.fg.dc(i);this.gf.Qt().dc(i),this.bg(s),this.og.delete(t),this._g.delete(i)}sg(t,i){this.bg(this.fg.ww(t,i))}eg(t,i,s){this.bg(this.fg.Cw(t,i,s))}subscribeClick(t){this.ug.i(t)}unsubscribeClick(t){this.ug._(t)}subscribeCrosshairMove(t){this.dg.i(t)}unsubscribeCrosshairMove(t){this.dg._(t)}subscribeDblClick(t){this.cg.i(t)}unsubscribeDblClick(t){this.cg._(t)}priceScale(t,i=0){return new Bn(this.gf,t,i)}timeScale(){return this.mg}applyOptions(t){this.gf.hr(Ln(t))}options(){return this.gf.N()}takeScreenshot(){return this.gf.Lm()}removePane(t){this.gf.Qt().Yu(t)}swapPanes(t,i){this.gf.Qt().Xu(t,i)}autoSizeActive(){return this.gf.Um()}chartElement(){return this.gf.Ef()}panes(){return this.gf.Qt().$s().map((t=>this.xg(t)))}paneSize(t=0){const i=this.gf.qm(t);return{height:i.height,width:i.width}}setCrosshairPosition(t,i,s){const n=this.og.get(s);if(void 0===n)return;const e=this.gf.Qt().Hn(n);null!==e&&this.gf.Qt().ac(t,i,e)}clearCrosshairPosition(){this.gf.Qt().lc(!0)}horzBehaviour(){return this.pg}Mg(i,s={},n=0){r(void 0!==i.wg),On(s.priceFormat),"Candlestick"===i.type&&function(t){void 0!==t.borderColor&&(t.borderUpColor=t.borderColor,t.borderDownColor=t.borderColor),void 0!==t.wickColor&&(t.wickUpColor=t.wickColor,t.wickDownColor=t.wickColor)}(s);const e=_(p(t),p(i.defaultOptions),s),h=i.wg,a=new Ht(this.gf.Qt(),i.type,e,h,i.gg);this.gf.Qt().uc(a,n);const l=new An(a,this,this,this,this.pg,(t=>this.xg(t)));return this.og.set(l,a),this._g.set(a,l),l}bg(t){const i=this.gf.Qt();i.oc(t.Vt.U_,t.Vt.Rw,t.Vt.Dw),t.wo.forEach(((t,i)=>i.ht(t.se,t.Tw))),i.Vt().k_(),i.Z_()}Sg(t){return h(this._g.get(t))}vg(t){const i=new Map;t.hw.forEach(((t,s)=>{const n=s.Rr(),e=Sn(n)(t);if("Custom"!==n)r($s(e));else{const t=s.pa();r(!t||!1===t(e))}i.set(this.Sg(s),e)}));const s=void 0!==t.rw&&this._g.has(t.rw)?this.Sg(t.rw):void 0;return{time:t.sw,logical:t.Re,point:t.nw,paneIndex:t.ew,hoveredSeries:s,hoveredObjectId:t.aw,seriesData:i,sourceEvent:t.lw}}xg(t){let i=this.yu.get(t);return i||(i=new En(this.gf,(t=>this.Sg(t)),t,this),this.yu.set(t,i)),i}}function Wn(t){if(d(t)){const i=document.getElementById(t);return r(null!==i,`Cannot find element in DOM with id=${t}`),i}return t}function Fn(t,i,s){const n=Wn(t),e=new Nn(n,i,s);return i.setOptions(e.options()),e}class Hn extends _n{constructor(t,i){super(t,i,!0)}Uw(t,i,s){i.j_(this.Vw,m(this.Aw)),t.Bl(this.Vw,s,m(this.Aw))}Cg(t,i){return{wt:t,gt:i,_t:NaN,ut:NaN}}Fw(){const t=this.Jn.Rh();this.Vw=this.Jn.Xs().Hr().map((i=>{const s=i.Ft[3];return this.yg(i.Re,s,t)}))}}function Un(t,i,s,n,e,r,h){if(0===i.length||n.from>=i.length||n.to<=0)return;const{context:a,horizontalPixelRatio:l,verticalPixelRatio:o}=t,_=i[n.from];let u=r(t,_),c=_;if(n.to-n.from<2){const i=e/2;a.beginPath();const s={_t:_._t-i,ut:_.ut},n={_t:_._t+i,ut:_.ut};a.moveTo(s._t*l,s.ut*o),a.lineTo(n._t*l,n.ut*o),h(t,u,s,n)}else{const e=(i,s)=>{h(t,u,c,s),a.beginPath(),u=i,c=s};let d=c;a.beginPath(),a.moveTo(_._t*l,_.ut*o);for(let h=n.from+1;h<n.to;++h){d=i[h];const n=r(t,d);switch(s){case 0:a.lineTo(d._t*l,d.ut*o);break;case 1:a.lineTo(d._t*l,i[h-1].ut*o),n!==u&&(e(n,d),a.lineTo(d._t*l,i[h-1].ut*o)),a.lineTo(d._t*l,d.ut*o);break;case 2:{const[t,s]=Yn(i,h-1,h);a.bezierCurveTo(t._t*l,t.ut*o,s._t*l,s.ut*o,d._t*l,d.ut*o);break}}1!==s&&n!==u&&(e(n,d),a.moveTo(d._t*l,d.ut*o))}(c!==d||c===d&&1===s)&&h(t,u,c,d)}}const $n=6;function jn(t,i){return{_t:t._t-i._t,ut:t.ut-i.ut}}function qn(t,i){return{_t:t._t/i,ut:t.ut/i}}function Yn(t,i,s){const n=Math.max(0,i-1),e=Math.min(t.length-1,s+1);var r,h;return[(r=t[i],h=qn(jn(t[s],t[n]),$n),{_t:r._t+h._t,ut:r.ut+h.ut}),jn(t[s],qn(jn(t[e],t[i]),$n))]}function Kn(t,i){const s=t.context;s.strokeStyle=i,s.stroke()}class Xn extends y{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Pg:e,kg:r,ct:h,Xt:a,Tg:l}=this.rt;if(null===s)return;const o=t.context;o.lineCap="butt",o.lineWidth=h*t.verticalPixelRatio,n(o,a),o.lineJoin="round";const _=this.Rg.bind(this);void 0!==r&&Un(t,i,r,s,e,_,Kn),l&&function(t,i,s,n,e){if(n.to-n.from<=0)return;const{horizontalPixelRatio:r,verticalPixelRatio:h,context:a}=t;let l=null;const o=Math.max(1,Math.floor(r))%2/2,_=s*h+o;for(let s=n.to-1;s>=n.from;--s){const n=i[s];if(n){const i=e(t,n);i!==l&&(a.beginPath(),null!==l&&a.fill(),a.fillStyle=i,l=i);const s=Math.round(n._t*r)+o,u=n.ut*h;a.moveTo(s,u),a.arc(s,u,_,0,2*Math.PI)}}a.fill()}(t,i,l,s,_)}}class Zn extends Xn{Rg(t,i){return i.vt}}class Gn extends Hn{constructor(){super(...arguments),this.Lw=new Zn}yg(t,i,s){return{...this.Cg(t,i),...s.Dr(t)}}$w(){const t=this.Jn.N(),i={ot:this.Vw,Xt:t.lineStyle,kg:t.lineVisible?t.lineType:void 0,ct:t.lineWidth,Tg:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0,lt:this.Aw,Pg:this.Qn.Vt().G_()};this.Lw.ht(i)}}const Jn={type:"Line",isBuiltIn:!0,defaultOptions:{color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},wg:(t,i)=>new Gn(t,i)};function Qn(t,i){return t.weight>i.weight?t:i}class te{constructor(){this.Dg=new o,this.Bg=function(t){let i=!1;return function(...s){i||(i=!0,queueMicrotask((()=>{t(...s),i=!1})))}}((()=>this.Dg.p(this.Eg))),this.Eg=0}Ig(){return this.Dg}m(){this.Dg.m()}options(){return this.ys}setOptions(t){this.ys=t}preprocessData(t){}updateFormatter(t){this.ys&&(this.ys.localization=t)}createConverterToInternalObj(t){return this.Bg(),t=>(t>this.Eg&&(this.Eg=t),t)}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return this.Vg(t)}formatTickmark(t){return this.Vg(t.time)}maxTickMarkWeight(t){return t.reduce(Qn,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)%120==0?10:s%60==0?9:s%36==0?8:s%12==0?7:s%6==0?6:s%3==0?5:s%1==0?4:0;var s;this.Eg=t[t.length-1].time,this.Bg()}Vg(t){if(this.ys.localization?.timeFormatter)return this.ys.localization.timeFormatter(t);if(t<12)return`${t}M`;const i=Math.floor(t/12),s=t%12;return 0===s?`${i}Y`:`${i}Y${s}M`}}const ie={yieldCurve:{baseResolution:1,minimumTimeRange:120,startTimeRange:0},timeScale:{ignoreWhitespaceIndices:!0},leftPriceScale:{visible:!0},rightPriceScale:{visible:!1},localization:{priceFormatter:t=>t.toFixed(3)+"%"}},se={lastValueVisible:!1,priceLineVisible:!1};class ne extends Nn{constructor(t,i){const s=_(ie,i||{}),n=new te;super(t,n,s),n.setOptions(this.options()),this._initWhitespaceSeries()}addSeries(t,i={},s=0){if(t.isBuiltIn&&!1===["Area","Line"].includes(t.type))throw new Error("Yield curve only support Area and Line series");const n={...se,...i};return super.addSeries(t,n,s)}_initWhitespaceSeries(){const t=this.horzBehaviour(),i=this.addSeries(Jn);let s;function n(n){const e=function(t,i){return{le:Math.max(0,t.startTimeRange),oe:Math.max(0,t.minimumTimeRange,i||0),Ag:Math.max(1,t.baseResolution)}}(t.options().yieldCurve,n),r=(({le:t,oe:i,Ag:s})=>`${t}~${i}~${s}`)(e);r!==s&&(s=r,i.setData(function({le:t,oe:i,Ag:s}){return Array.from({length:Math.floor((i-t)/s)+1},((i,n)=>({time:t+n*s})))}(e)))}n(0),t.Ig().i(n)}}function ee(t,i){return t.weight>i.weight?t:i}class re{options(){return this.ys}setOptions(t){this.ys=t}preprocessData(t){}updateFormatter(t){this.ys&&(this.ys.localization=t)}createConverterToInternalObj(t){return t=>t}key(t){return t}cacheKey(t){return t}convertHorzItemToInternal(t){return t}formatHorzItem(t){return t.toFixed(this.Cn())}formatTickmark(t,i){return t.time.toFixed(this.Cn())}maxTickMarkWeight(t){return t.reduce(ee,t[0]).weight}fillWeightsForPoints(t,i){for(let n=i;n<t.length;++n)t[n].timeWeight=(s=t[n].time)===100*Math.ceil(s/100)?8:s===50*Math.ceil(s/50)?7:s===25*Math.ceil(s/25)?6:s===10*Math.ceil(s/10)?5:s===5*Math.ceil(s/5)?4:s===Math.ceil(s)?3:2*s===Math.ceil(2*s)?1:0;var s}Cn(){return this.ys.localization.precision}}function he(t,i,s,n,e){const{context:r,horizontalPixelRatio:h,verticalPixelRatio:a}=i;r.lineTo(e._t*h,t*a),r.lineTo(n._t*h,t*a),r.closePath(),r.fillStyle=s,r.fill()}class ae extends y{constructor(){super(...arguments),this.rt=null}ht(t){this.rt=t}et(t){if(null===this.rt)return;const{ot:i,lt:s,Pg:e,ct:r,Xt:h,kg:a}=this.rt,l=this.rt.zg??(this.rt.Og?0:t.mediaSize.height);if(null===s)return;const o=t.context;o.lineCap="butt",o.lineJoin="round",o.lineWidth=r,n(o,h),o.lineWidth=1,Un(t,i,a,s,e,this.Lg.bind(this),he.bind(null,l))}}class le{Ng(t,i){const s=this.Wg,{Fg:n,Hg:e,Ug:r,$g:h,zg:a,jg:l,qg:o}=i;if(void 0===this.Yg||void 0===s||s.Fg!==n||s.Hg!==e||s.Ug!==r||s.$g!==h||s.zg!==a||s.jg!==l||s.qg!==o){const{verticalPixelRatio:s}=t,_=a||l>0?s:1,u=l*_,c=o===t.bitmapSize.height?o:o*_,d=(a??0)*_,f=t.context.createLinearGradient(0,u,0,c);if(f.addColorStop(0,n),null!=a){const t=qt((d-u)/(c-u),0,1);f.addColorStop(t,e),f.addColorStop(t,r)}f.addColorStop(1,h),this.Yg=f,this.Wg=i}return this.Yg}}class oe extends ae{constructor(){super(...arguments),this.Kg=new le}Lg(t,i){const s=this.rt;return this.Kg.Ng(t,{Fg:i.br,Hg:i.Sr,Ug:i.Cr,$g:i.yr,zg:s.zg,jg:s.jg??0,qg:s.qg??t.bitmapSize.height})}}class _e extends Xn{constructor(){super(...arguments),this.Xg=new le}Rg(t,i){const s=this.rt;return this.Xg.Ng(t,{Fg:i.gr,Hg:i.gr,Ug:i.Mr,$g:i.Mr,zg:s.zg,jg:s.jg??0,qg:s.qg??t.bitmapSize.height})}}class ue extends Hn{constructor(t,i){super(t,i),this.Lw=new C,this.Zg=new oe,this.Gg=new _e,this.Lw.st([this.Zg,this.Gg])}yg(t,i,s){return{...this.Cg(t,i),...s.Dr(t)}}$w(){const t=this.Jn.zt();if(null===t)return;const i=this.Jn.N(),s=this.Jn.Wt().Nt(i.baseValue.price,t.Ft),n=this.Qn.Vt().G_();if(null===this.Aw||0===this.Vw.length)return;let e,r;if(i.relativeGradient){e=this.Vw[this.Aw.from].ut,r=this.Vw[this.Aw.from].ut;for(let t=this.Aw.from;t<this.Aw.to;t++){const i=this.Vw[t];i.ut<e&&(e=i.ut),i.ut>r&&(r=i.ut)}}this.Zg.ht({ot:this.Vw,ct:i.lineWidth,Xt:i.lineStyle,kg:i.lineType,zg:s,jg:e,qg:r,Og:!1,lt:this.Aw,Pg:n}),this.Gg.ht({ot:this.Vw,ct:i.lineWidth,Xt:i.lineStyle,kg:i.lineVisible?i.lineType:void 0,Tg:i.pointMarkersVisible?i.pointMarkersRadius||i.lineWidth/2+2:void 0,zg:s,jg:e,qg:r,lt:this.Aw,Pg:n})}}const ce={type:"Baseline",isBuiltIn:!0,defaultOptions:{baseValue:{type:"price",price:0},relativeGradient:!1,topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},wg:(t,i)=>new ue(t,i)};class de extends ae{constructor(){super(...arguments),this.Kg=new le}Lg(t,i){return this.Kg.Ng(t,{Fg:i.mr,Hg:"",Ug:"",$g:i.wr,jg:this.rt?.jg??0,qg:t.bitmapSize.height})}}class fe extends Hn{constructor(t,i){super(t,i),this.Lw=new C,this.Jg=new de,this.Qg=new Zn,this.Lw.st([this.Jg,this.Qg])}yg(t,i,s){return{...this.Cg(t,i),...s.Dr(t)}}$w(){const t=this.Jn.N();if(null===this.Aw||0===this.Vw.length)return;let i;if(t.relativeGradient){i=this.Vw[this.Aw.from].ut;for(let t=this.Aw.from;t<this.Aw.to;t++){const s=this.Vw[t];s.ut<i&&(i=s.ut)}}this.Jg.ht({kg:t.lineType,ot:this.Vw,Xt:t.lineStyle,ct:t.lineWidth,zg:null,jg:i,Og:t.invertFilledArea,lt:this.Aw,Pg:this.Qn.Vt().G_()}),this.Qg.ht({kg:t.lineVisible?t.lineType:void 0,ot:this.Vw,Xt:t.lineStyle,ct:t.lineWidth,lt:this.Aw,Pg:this.Qn.Vt().G_(),Tg:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0})}}const pe={type:"Area",isBuiltIn:!0,defaultOptions:{topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,relativeGradient:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},wg:(t,i)=>new fe(t,i)};class ve extends y{constructor(){super(...arguments),this.qt=null,this.tM=0,this.iM=0}ht(t){this.qt=t}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.qt||0===this.qt.Xs.length||null===this.qt.lt)return;if(this.tM=this.sM(i),this.tM>=2){Math.max(1,Math.floor(i))%2!=this.tM%2&&this.tM--}this.iM=this.qt.nM?Math.min(this.tM,Math.floor(i)):this.tM;let n=null;const e=this.iM<=this.tM&&this.qt.G_>=Math.floor(1.5*i);for(let r=this.qt.lt.from;r<this.qt.lt.to;++r){const h=this.qt.Xs[r];n!==h.cr&&(t.fillStyle=h.cr,n=h.cr);const a=Math.floor(.5*this.iM),l=Math.round(h._t*i),o=l-a,_=this.iM,u=o+_-1,c=Math.min(h.Al,h.zl),d=Math.max(h.Al,h.zl),f=Math.round(c*s)-a,p=Math.round(d*s)+a,v=Math.max(p-f,this.iM);t.fillRect(o,f,_,v);const m=Math.ceil(1.5*this.tM);if(e){if(this.qt.eM){const i=l-m;let n=Math.max(f,Math.round(h.Vl*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(i,n,o-i,e-n+1)}const i=l+m;let n=Math.max(f,Math.round(h.Ol*s)-a),e=n+_-1;e>f+v-1&&(e=f+v-1,n=e-_+1),t.fillRect(u+1,n,i-u,e-n+1)}}}sM(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(t,i){return Math.floor(.3*t*i)}(a(this.qt).G_,t)))}}class me extends _n{constructor(t,i){super(t,i,!1)}Uw(t,i,s){i.j_(this.Vw,m(this.Aw)),t.Il(this.Vw,s,m(this.Aw))}rM(t,i,s){return{wt:t,jh:i.Ft[0],qh:i.Ft[1],Yh:i.Ft[2],Kh:i.Ft[3],_t:NaN,Vl:NaN,Al:NaN,zl:NaN,Ol:NaN}}Fw(){const t=this.Jn.Rh();this.Vw=this.Jn.Xs().Hr().map((i=>this.yg(i.Re,i,t)))}}class we extends me{constructor(){super(...arguments),this.Lw=new ve}yg(t,i,s){return{...this.rM(t,i,s),...s.Dr(t)}}$w(){const t=this.Jn.N();this.Lw.ht({Xs:this.Vw,G_:this.Qn.Vt().G_(),eM:t.openVisible,nM:t.thinBars,lt:this.Aw})}}const ge={type:"Bar",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},wg:(t,i)=>new we(t,i)};class Me extends y{constructor(){super(...arguments),this.qt=null,this.tM=0}ht(t){this.qt=t}et(t){if(null===this.qt||0===this.qt.Xs.length||null===this.qt.lt)return;const{horizontalPixelRatio:i}=t;if(this.tM=function(t,i){if(t>=2.5&&t<=4)return Math.floor(3*i);const s=1-.2*Math.atan(Math.max(4,t)-4)/(.5*Math.PI),n=Math.floor(t*s*i),e=Math.floor(t*i),r=Math.min(n,e);return Math.max(Math.floor(i),r)}(this.qt.G_,i),this.tM>=2){Math.floor(i)%2!=this.tM%2&&this.tM--}const s=this.qt.Xs;this.qt.hM&&this.aM(t,s,this.qt.lt),this.qt.Mi&&this.Hp(t,s,this.qt.lt);const n=this.lM(i);(!this.qt.Mi||this.tM>2*n)&&this.oM(t,s,this.qt.lt)}aM(t,i,s){if(null===this.qt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="",a=Math.min(Math.floor(e),Math.floor(this.qt.G_*e));a=Math.max(Math.floor(e),Math.min(a,this.tM));const l=Math.floor(.5*a);let o=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.pr!==h&&(n.fillStyle=s.pr,h=s.pr);const _=Math.round(Math.min(s.Vl,s.Ol)*r),u=Math.round(Math.max(s.Vl,s.Ol)*r),c=Math.round(s.Al*r),d=Math.round(s.zl*r);let f=Math.round(e*s._t)-l;const p=f+a-1;null!==o&&(f=Math.max(o+1,f),f=Math.min(f,p));const v=p-f+1;n.fillRect(f,c,v,_-c),n.fillRect(f,u+1,v,d-u),o=p}}lM(t){let i=Math.floor(1*t);this.tM<=2*i&&(i=Math.floor(.5*(this.tM-1)));const s=Math.max(Math.floor(t),i);return this.tM<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}Hp(t,i,s){if(null===this.qt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.lM(e);let l=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.dr!==h&&(n.fillStyle=s.dr,h=s.dr);let o=Math.round(s._t*e)-Math.floor(.5*this.tM);const _=o+this.tM-1,u=Math.round(Math.min(s.Vl,s.Ol)*r),c=Math.round(Math.max(s.Vl,s.Ol)*r);if(null!==l&&(o=Math.max(l+1,o),o=Math.min(o,_)),this.qt.G_*e>2*a)E(n,o,u,_-o+1,c-u+1,a);else{const t=_-o+1;n.fillRect(o,u,t,c-u+1)}l=_}}oM(t,i,s){if(null===this.qt)return;const{context:n,horizontalPixelRatio:e,verticalPixelRatio:r}=t;let h="";const a=this.lM(e);for(let t=s.from;t<s.to;t++){const s=i[t];let l=Math.round(Math.min(s.Vl,s.Ol)*r),o=Math.round(Math.max(s.Vl,s.Ol)*r),_=Math.round(s._t*e)-Math.floor(.5*this.tM),u=_+this.tM-1;if(s.cr!==h){const t=s.cr;n.fillStyle=t,h=t}this.qt.Mi&&(_+=a,l+=a,u-=a,o-=a),l>o||n.fillRect(_,l,u-_+1,o-l+1)}}}class be extends me{constructor(){super(...arguments),this.Lw=new Me}yg(t,i,s){return{...this.rM(t,i,s),...s.Dr(t)}}$w(){const t=this.Jn.N();this.Lw.ht({Xs:this.Vw,G_:this.Qn.Vt().G_(),hM:t.wickVisible,Mi:t.borderVisible,lt:this.Aw})}}const xe={type:"Candlestick",isBuiltIn:!0,defaultOptions:{upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},wg:(t,i)=>new be(t,i)};class Se extends y{constructor(){super(...arguments),this.qt=null,this._M=[]}ht(t){this.qt=t,this._M=[]}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this.qt||0===this.qt.ot.length||null===this.qt.lt)return;this._M.length||this.uM(i);const n=Math.max(1,Math.floor(s)),e=Math.round(this.qt.cM*s)-Math.floor(n/2),r=e+n;for(let i=this.qt.lt.from;i<this.qt.lt.to;i++){const h=this.qt.ot[i],a=this._M[i-this.qt.lt.from],l=Math.round(h.ut*s);let o,_;t.fillStyle=h.cr,l<=e?(o=l,_=r):(o=e,_=l-Math.floor(n/2)+n),t.fillRect(a.Uh,o,a.bi-a.Uh+1,_-o)}}uM(t){if(null===this.qt||0===this.qt.ot.length||null===this.qt.lt)return void(this._M=[]);const i=Math.ceil(this.qt.G_*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this.qt.G_*t)-i;this._M=new Array(this.qt.lt.to-this.qt.lt.from);for(let i=this.qt.lt.from;i<this.qt.lt.to;i++){const n=this.qt.ot[i],e=Math.round(n._t*t);let r,h;if(s%2){const t=(s-1)/2;r=e-t,h=e+t}else{const t=s/2;r=e-t,h=e+t-1}this._M[i-this.qt.lt.from]={Uh:r,bi:h,dM:e,ne:n._t*t,wt:n.wt}}for(let t=this.qt.lt.from+1;t<this.qt.lt.to;t++){const s=this._M[t-this.qt.lt.from],n=this._M[t-this.qt.lt.from-1];s.wt===n.wt+1&&(s.Uh-n.bi!==i+1&&(n.dM>n.ne?n.bi=s.Uh-i-1:s.Uh=n.bi+i+1))}let n=Math.ceil(this.qt.G_*t);for(let t=this.qt.lt.from;t<this.qt.lt.to;t++){const i=this._M[t-this.qt.lt.from];i.bi<i.Uh&&(i.bi=i.Uh);const s=i.bi-i.Uh+1;n=Math.min(s,n)}if(i>0&&n<4)for(let t=this.qt.lt.from;t<this.qt.lt.to;t++){const i=this._M[t-this.qt.lt.from];i.bi-i.Uh+1>n&&(i.dM>i.ne?i.bi-=1:i.Uh+=1)}}}class Ce extends Hn{constructor(){super(...arguments),this.Lw=new Se}yg(t,i,s){return{...this.Cg(t,i),...s.Dr(t)}}$w(){const t={ot:this.Vw,G_:this.Qn.Vt().G_(),lt:this.Aw,cM:this.Jn.Wt().Nt(this.Jn.N().base,a(this.Jn.zt()).Ft)};this.Lw.ht(t)}}const ye={type:"Histogram",isBuiltIn:!0,defaultOptions:{color:"#26a69a",base:0},wg:(t,i)=>new Ce(t,i)};class Pe{constructor(t,i){this.yt=t,this.fM=i,this.pM()}detach(){this.yt.detachPrimitive(this.fM)}getPane(){return this.yt}applyOptions(t){this.fM.hr?.(t)}pM(){this.yt.attachPrimitive(this.fM)}}const ke={visible:!0,horzAlign:"center",vertAlign:"center",lines:[]},Te={color:"rgba(0, 0, 0, 0.5)",fontSize:48,fontFamily:w,fontStyle:"",text:""};class Re{constructor(t){this.vM=new Map,this.qt=t}draw(t){t.useMediaCoordinateSpace((t=>{if(!this.qt.visible)return;const{context:i,mediaSize:s}=t;let n=0;for(const t of this.qt.lines){if(0===t.text.length)continue;i.font=t.k;const e=this.mM(i,t.text);e>s.width?t.eu=s.width/e:t.eu=1,n+=t.lineHeight*t.eu}let e=0;switch(this.qt.vertAlign){case"top":e=0;break;case"center":e=Math.max((s.height-n)/2,0);break;case"bottom":e=Math.max(s.height-n,0)}for(const t of this.qt.lines){i.save(),i.fillStyle=t.color;let n=0;switch(this.qt.horzAlign){case"left":i.textAlign="left",n=t.lineHeight/2;break;case"center":i.textAlign="center",n=s.width/2;break;case"right":i.textAlign="right",n=s.width-1-t.lineHeight/2}i.translate(n,e),i.textBaseline="top",i.font=t.k,i.scale(t.eu,t.eu),i.fillText(t.text,0,t.wM),i.restore(),e+=t.lineHeight*t.eu}}))}mM(t,i){const s=this.gM(t.font);let n=s.get(i);return void 0===n&&(n=t.measureText(i).width,s.set(i,n)),n}gM(t){let i=this.vM.get(t);return void 0===i&&(i=new Map,this.vM.set(t,i)),i}}class De{constructor(t){this.ys=Ee(t)}Pt(t){this.ys=Ee(t)}renderer(){return new Re(this.ys)}}function Be(t){return{...t,k:g(t.fontSize,t.fontFamily,t.fontStyle),lineHeight:t.lineHeight||1.2*t.fontSize,wM:0,eu:0}}function Ee(t){return{...t,lines:t.lines.map(Be)}}function Ie(t){return{...Te,...t}}function Ve(t){return{...ke,...t,lines:t.lines?.map(Ie)??[]}}class Ae{constructor(t){this.ys=Ve(t),this.MM=[new De(this.ys)]}updateAllViews(){this.MM.forEach((t=>t.Pt(this.ys)))}paneViews(){return this.MM}attached({requestUpdate:t}){this.bM=t}detached(){this.bM=void 0}hr(t){this.ys=Ve({...this.ys,...t}),this.bM&&this.bM()}}const ze={alpha:1,padding:0};class Oe{constructor(t){this.qt=t}draw(t){t.useMediaCoordinateSpace((t=>{const i=t.context,s=this.xM(this.qt,t.mediaSize);s&&this.qt.SM&&(i.globalAlpha=this.qt.alpha??1,i.drawImage(this.qt.SM,s._t,s.ut,s.Qi,s.$t))}))}xM(t,i){const{maxHeight:s,maxWidth:n,CM:e,yM:r,padding:h}=t,a=Math.round(i.width/2),l=Math.round(i.height/2),o=h??0;let _=i.width-2*o,u=i.height-2*o;s&&(u=Math.min(u,s)),n&&(_=Math.min(_,n));const c=_/r,d=u/e,f=Math.min(c,d),p=r*f,v=e*f;return{_t:a-.5*p,ut:l-.5*v,$t:v,Qi:p}}}class Le{constructor(t){this.PM=null,this.kM=0,this.TM=0,this.ys=t,this.M=Ne(this.ys,this.PM,this.kM,this.TM)}RM(t){void 0!==t.DM&&(this.kM=t.DM),void 0!==t.BM&&(this.TM=t.BM),void 0!==t.EM&&(this.PM=t.EM),this.Pt()}IM(t){this.ys=t,this.Pt()}zOrder(){return"bottom"}Pt(){this.M=Ne(this.ys,this.PM,this.kM,this.TM)}renderer(){return new Oe(this.M)}}function Ne(t,i,s,n){return{...t,SM:i,yM:s,CM:n}}function We(t){return{...ze,...t}}class Fe{constructor(t,i){this.VM=null,this.AM=t,this.ys=We(i),this.MM=[new Le(this.ys)]}updateAllViews(){this.MM.forEach((t=>t.Pt()))}paneViews(){return this.MM}attached(t){const{requestUpdate:i}=t;this.zM=i,this.VM=new Image,this.VM.onload=()=>{const t=this.VM?.naturalHeight??1,i=this.VM?.naturalWidth??1;this.MM.forEach((s=>s.RM({BM:t,DM:i,EM:this.VM}))),this.zM&&this.zM()},this.VM.src=this.AM}detached(){this.zM=void 0,this.VM=null}hr(t){this.ys=We({...this.ys,...t}),this.OM(),this.bM&&this.bM()}bM(){this.zM&&this.zM()}OM(){this.MM.forEach((t=>t.IM(this.ys)))}}class He{constructor(t,i){this.Jn=t,this.ah=i,this.pM()}detach(){this.Jn.detachPrimitive(this.ah)}getSeries(){return this.Jn}applyOptions(t){this.ah&&this.ah.hr&&this.ah.hr(t)}pM(){this.Jn.attachPrimitive(this.ah)}}function Ue(t,i){return Kt(Math.min(Math.max(t,12),30)*i)}function $e(t,i){switch(t){case"arrowDown":case"arrowUp":return Ue(i,1);case"circle":return Ue(i,.8);case"square":return Ue(i,.7)}}function je(t){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(Ue(t,1))}function qe(t){return Math.max(Ue(t,.1),3)}function Ye(t,i,s){return i?t:s?Math.ceil(t/2):0}function Ke(t,i,s,n){const e=($e("arrowUp",n)-1)/2*s.LM,r=(Kt(n/2)-1)/2*s.LM;i.beginPath(),t?(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut-e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut+e),i.lineTo(s._t-r,s.ut+e),i.lineTo(s._t-r,s.ut)):(i.moveTo(s._t-e,s.ut),i.lineTo(s._t,s.ut+e),i.lineTo(s._t+e,s.ut),i.lineTo(s._t+r,s.ut),i.lineTo(s._t+r,s.ut-e),i.lineTo(s._t-r,s.ut-e),i.lineTo(s._t-r,s.ut)),i.fill()}function Xe(t,i,s,n,e,r){const h=($e("arrowUp",n)-1)/2,a=(Kt(n/2)-1)/2;if(e>=i-a-2&&e<=i+a+2&&r>=(t?s:s-h)-2&&r<=(t?s+h:s)+2)return!0;return(()=>{if(e<i-h-3||e>i+h+3||r<(t?s-h-3:s)||r>(t?s:s+h+3))return!1;const n=Math.abs(e-i);return Math.abs(r-s)+3>=n/2})()}class Ze{constructor(){this.qt=null,this.Ln=new tt,this.W=-1,this.F="",this.wp=""}ht(t){this.qt=t}Nn(t,i){this.W===t&&this.F===i||(this.W=t,this.F=i,this.wp=g(t,i),this.Ln.En())}Yn(t,i){if(null===this.qt||null===this.qt.lt)return null;for(let s=this.qt.lt.from;s<this.qt.lt.to;s++){const n=this.qt.ot[s];if(n&&Je(n,t,i))return{zOrder:"normal",externalId:n.Kn??""}}return null}draw(t){t.useBitmapCoordinateSpace((t=>{this.et(t)}))}et({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null!==this.qt&&null!==this.qt.lt){t.textBaseline="middle",t.font=this.wp;for(let n=this.qt.lt.from;n<this.qt.lt.to;n++){const e=this.qt.ot[n];void 0!==e.ri&&(e.ri.Qi=this.Ln.Bi(t,e.ri.NM),e.ri.$t=this.W,e.ri._t=e._t-e.ri.Qi/2),Ge(e,t,i,s)}}}}function Ge(t,i,s,n){i.fillStyle=t.R,void 0!==t.ri&&function(t,i,s,n,e,r){t.save(),t.scale(e,r),t.fillText(i,s,n),t.restore()}(i,t.ri.NM,t.ri._t,t.ri.ut,s,n),function(t,i,s){if(0===t.zr)return;switch(t.WM){case"arrowDown":return void Ke(!1,i,s,t.zr);case"arrowUp":return void Ke(!0,i,s,t.zr);case"circle":return void function(t,i,s){const n=($e("circle",s)-1)/2;t.beginPath(),t.arc(i._t,i.ut,n*i.LM,0,2*Math.PI,!1),t.fill()}(i,s,t.zr);case"square":return void function(t,i,s){const n=$e("square",s),e=(n-1)*i.LM/2,r=i._t-e,h=i.ut-e;t.fillRect(r,h,n*i.LM,n*i.LM)}(i,s,t.zr)}t.WM}(t,i,function(t,i,s){const n=Math.max(1,Math.floor(i))%2/2;return{_t:Math.round(t._t*i)+n,ut:t.ut*s,LM:i}}(t,s,n))}function Je(t,i,s){return!(void 0===t.ri||!function(t,i,s,n,e,r){const h=n/2;return e>=t&&e<=t+s&&r>=i-h&&r<=i+h}(t.ri._t,t.ri.ut,t.ri.Qi,t.ri.$t,i,s))||function(t,i,s){if(0===t.zr)return!1;switch(t.WM){case"arrowDown":return Xe(!0,t._t,t.ut,t.zr,i,s);case"arrowUp":return Xe(!1,t._t,t.ut,t.zr,i,s);case"circle":return function(t,i,s,n,e){const r=2+$e("circle",s)/2,h=t-n,a=i-e;return Math.sqrt(h*h+a*a)<=r}(t._t,t.ut,t.zr,i,s);case"square":return function(t,i,s,n,e){const r=$e("square",s),h=(r-1)/2,a=t-h,l=i-h;return n>=a&&n<=a+r&&e>=l&&e<=l+r}(t._t,t.ut,t.zr,i,s)}}(t,i,s)}function Qe(t){return"atPriceTop"===t||"atPriceBottom"===t||"atPriceMiddle"===t}function tr(t,i,s,n,e,r,h,l){const o=function(t,i){if(Qe(i.position)&&void 0!==i.price)return i.price;if("value"in(s=t)&&"number"==typeof s.value)return t.value;var s;if(function(t){return"open"in t&&"high"in t&&"low"in t&&"close"in t}(t)){if("inBar"===i.position)return t.close;if("aboveBar"===i.position)return t.high;if("belowBar"===i.position)return t.low}}(s,i);if(void 0===o)return;const _=Qe(i.position),c=l.timeScale(),d=u(i.size)?Math.max(i.size,0):1,f=je(c.options().barSpacing)*d,p=f/2;t.zr=f;switch(i.position){case"inBar":case"atPriceMiddle":return t.ut=a(h.priceToCoordinate(o)),void(void 0!==t.ri&&(t.ri.ut=t.ut+p+r+.6*e));case"aboveBar":case"atPriceTop":{const i=_?0:n.FM;return t.ut=a(h.priceToCoordinate(o))-p-i,void 0!==t.ri&&(t.ri.ut=t.ut-p-.6*e,n.FM+=1.2*e),void(_||(n.FM+=f+r))}case"belowBar":case"atPriceBottom":{const i=_?0:n.HM;return t.ut=a(h.priceToCoordinate(o))+p+i,void 0!==t.ri&&(t.ri.ut=t.ut+p+r+.6*e,n.HM+=1.2*e),void(_||(n.HM+=f+r))}}}class ir{constructor(t,i){this.UM=[],this.St=!0,this.$M=!0,this.Gt=new Ze,this.ge=t,this.lp=i,this.qt={ot:[],lt:null}}renderer(){if(!this.ge.options().visible)return null;this.St&&this.jM();const t=this.lp.options().layout;return this.Gt.Nn(t.fontSize,t.fontFamily),this.Gt.ht(this.qt),this.Gt}qM(t){this.UM=t,this.Pt("data")}Pt(t){this.St=!0,"data"===t&&(this.$M=!0)}jM(){const t=this.lp.timeScale(),i=this.UM;this.$M&&(this.qt.ot=i.map((t=>({wt:t.time,_t:0,ut:0,zr:0,WM:t.shape,R:t.color,Kn:t.id,YM:t.YM,ri:void 0}))),this.$M=!1);const s=this.lp.options().layout;this.qt.lt=null;const n=t.getVisibleLogicalRange();if(null===n)return;const e=new xi(Math.floor(n.from),Math.ceil(n.to));if(null===this.ge.data()[0])return;if(0===this.qt.ot.length)return;let r=NaN;const h=qe(t.options().barSpacing),l={FM:h,HM:h};this.qt.lt=on(this.qt.ot,e,!0);for(let n=this.qt.lt.from;n<this.qt.lt.to;n++){const e=i[n];e.time!==r&&(l.FM=h,l.HM=h,r=e.time);const o=this.qt.ot[n];o._t=a(t.logicalToCoordinate(e.time)),void 0!==e.text&&e.text.length>0&&(o.ri={NM:e.text,_t:0,ut:0,Qi:0,$t:0});const _=this.ge.dataByIndex(e.time,0);null!==_&&tr(o,e,_,l,s.fontSize,h,this.ge,this.lp)}this.St=!1}}class sr{constructor(){this.sh=null,this.UM=[],this.KM=[],this.XM=null,this.ge=null,this.lp=null,this.ZM=!0,this.GM=null,this.JM=null,this.QM=null,this.tb=!0}attached(t){this.ib(),this.lp=t.chart,this.ge=t.series,this.sh=new ir(this.ge,a(this.lp)),this.zM=t.requestUpdate,this.ge.subscribeDataChanged((t=>this.ng(t))),this.tb=!0,this.bM()}bM(){this.zM&&this.zM()}detached(){this.ge&&this.XM&&this.ge.unsubscribeDataChanged(this.XM),this.lp=null,this.ge=null,this.sh=null,this.XM=null}qM(t){this.tb=!0,this.UM=t,this.ib(),this.ZM=!0,this.JM=null,this.bM()}sb(){return this.UM}paneViews(){return this.sh?[this.sh]:[]}updateAllViews(){this.nb()}hitTest(t,i){return this.sh?this.sh.renderer()?.Yn(t,i)??null:null}autoscaleInfo(t,i){if(this.sh){const t=this.eb();if(t)return{priceRange:null,margins:t}}return null}eb(){const t=a(this.lp).timeScale().options().barSpacing;if(this.ZM||t!==this.QM){if(this.QM=t,this.UM.length>0){const i=qe(t),s=1.5*je(t)+2*i,n=this.rb();this.GM={above:Ye(s,n.aboveBar,n.inBar),below:Ye(s,n.belowBar,n.inBar)}}else this.GM=null;this.ZM=!1}return this.GM}rb(){return null===this.JM&&(this.JM=this.UM.reduce(((t,i)=>(t[i.position]||(t[i.position]=!0),t)),{inBar:!1,aboveBar:!1,belowBar:!1,atPriceTop:!1,atPriceBottom:!1,atPriceMiddle:!1})),this.JM}ib(){if(!this.tb||!this.lp||!this.ge)return;const t=this.lp.timeScale(),i=this.ge?.data();if(null==t.getVisibleLogicalRange()||!this.ge||0===i.length)return void(this.KM=[]);const s=t.timeToIndex(a(i[0].time),!0);this.KM=this.UM.map(((i,n)=>{const e=t.timeToIndex(i.time,!0),r=e<s?1:-1,h=a(this.ge).dataByIndex(e,r),l={time:t.timeToIndex(a(h).time,!1),position:i.position,shape:i.shape,color:i.color,id:i.id,YM:n,text:i.text,size:i.size,price:i.price,sw:i.time};if("atPriceTop"===i.position||"atPriceBottom"===i.position||"atPriceMiddle"===i.position){if(void 0===i.price)throw new Error(`Price is required for position ${i.position}`);return{...l,position:i.position,price:i.price}}return{...l,position:i.position,price:i.price}})),this.tb=!1}nb(t){this.sh&&(this.ib(),this.sh.qM(this.KM),this.sh.Pt(t))}ng(t){this.tb=!0,this.bM()}}class nr extends He{constructor(t,i,s){super(t,i),s&&this.setMarkers(s)}setMarkers(t){this.ah.qM(t)}markers(){return this.ah.sb()}}class er{constructor(t){this.UM=new Map,this.hb=t}ab(t,i,s){if(this.lb(i),void 0!==s){const n=window.setTimeout((()=>{this.UM.delete(i),this.ob()}),s),e={...t,_b:n,ub:Date.now()+s};this.UM.set(i,e)}else this.UM.set(i,{...t,_b:void 0,ub:void 0});this.ob()}lb(t){const i=this.UM.get(t);i&&void 0!==i._b&&window.clearTimeout(i._b),this.UM.delete(t),this.ob()}cb(){for(const[t]of this.UM)this.lb(t)}fb(){const t=Date.now(),i=[];for(const[s,n]of this.UM)!n.ub||n.ub>t?i.push({time:n.time,sign:n.sign,value:n.value}):this.lb(s);return i}pb(t){this.hb=t}ob(){this.hb&&this.hb()}}const rr={positiveColor:"#22AB94",negativeColor:"#F7525F",updateVisibilityDuration:5e3};class hr{constructor(t,i,s,n){this.qt=t,this.mb=i,this.wb=s,this.gb=n}draw(t){t.useBitmapCoordinateSpace((t=>{const i=t.context,s=Math.max(1,Math.floor(t.horizontalPixelRatio))%2/2,n=4*t.verticalPixelRatio+s;this.qt.forEach((e=>{const r=Math.round(e._t*t.horizontalPixelRatio)+s;i.beginPath();const h=this.Mb(e.bb);i.fillStyle=h,i.arc(r,e.ut*t.verticalPixelRatio,n,0,2*Math.PI,!1),i.fill(),e.bb&&(i.strokeStyle=h,i.lineWidth=Math.floor(2*t.horizontalPixelRatio),i.beginPath(),i.moveTo((e._t-4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.bb)*t.verticalPixelRatio),i.lineTo(e._t*t.horizontalPixelRatio+s,(e.ut-7*e.bb-7*e.bb*.5)*t.verticalPixelRatio),i.lineTo((e._t+4.7)*t.horizontalPixelRatio+s,(e.ut-7*e.bb)*t.verticalPixelRatio),i.stroke())}))}))}Mb(t){return 0===t?this.mb:t>0?this.gb:this.wb}}class ar{constructor(t,i,s){this.qt=[],this.ge=t,this.uh=i,this.ys=s}Pt(t){this.qt=t.map((t=>{const i=this.ge.priceToCoordinate(t.value);if(null===i)return null;return{_t:a(this.uh.timeToCoordinate(t.time)),ut:i,bb:t.sign}})).filter(v)}renderer(){const t=function(t,i){return function(t,i){return"Area"===i}(0,i)?t.lineColor:t.color}(this.ge.options(),this.ge.seriesType());return new hr(this.qt,t,this.ys.negativeColor,this.ys.positiveColor)}}function lr(t,i){return"Line"===i||"Area"===i}class or{constructor(t){this.lp=void 0,this.ge=void 0,this.MM=[],this.$o=null,this.xb=new Map,this.Sb=new er((()=>this.bM())),this.ys={...rr,...t}}hr(t){this.ys={...this.ys,...t},this.bM()}qM(t){this.Sb.cb();const i=this.$o;i&&t.forEach((t=>{this.Sb.ab(t,i.key(t.time))}))}sb(){return this.Sb.fb()}bM(){this.zM?.()}attached(t){const{chart:i,series:s,requestUpdate:n,horzScaleBehavior:e}=t;this.lp=i,this.ge=s,this.$o=e;const r=this.ge.seriesType();if("Area"!==r&&"Line"!==r)throw new Error("UpDownMarkersPrimitive is only supported for Area and Line series types");this.MM=[new ar(this.ge,this.lp.timeScale(),this.ys)],this.zM=n,this.bM()}detached(){this.lp=void 0,this.ge=void 0,this.zM=void 0}xp(){return h(this.lp)}wo(){return h(this.ge)}updateAllViews(){this.MM.forEach((t=>t.Pt(this.sb())))}paneViews(){return this.MM}ht(t){if(!this.ge)throw new Error("Primitive not attached to series");const i=this.ge.seriesType();this.xb.clear();const s=this.$o;s&&t.forEach((t=>{$s(t)&&lr(0,i)&&this.xb.set(s.key(t.time),t.value)})),h(this.ge).setData(t)}Pt(t,i){if(!this.ge||!this.$o)throw new Error("Primitive not attached to series");const s=this.ge.seriesType(),n=this.$o.key(t.time);if(Us(t)&&this.xb.delete(n),$s(t)&&lr(0,s)){const i=this.xb.get(n);i&&this.Sb.ab({time:t.time,value:t.value,sign:_r(t.value,i)},n,this.ys.updateVisibilityDuration)}h(this.ge).update(t,i)}Cb(){this.Sb.cb()}}function _r(t,i){return t===i?0:t-i>0?1:-1}class ur extends He{setData(t){return this.ah.ht(t)}update(t,i){return this.ah.Pt(t,i)}markers(){return this.ah.sb()}setMarkers(t){return this.ah.qM(t)}clearMarkers(){return this.ah.Cb()}}const cr={...t,color:"#2196f3"};var dr=Object.freeze({__proto__:null,AreaSeries:pe,BarSeries:ge,BaselineSeries:ce,CandlestickSeries:xe,get ColorType(){return Bi},get CrosshairMode(){return $},HistogramSeries:ye,get LastPriceAnimationMode(){return Ri},LineSeries:Jn,get LineStyle(){return s},get LineType(){return i},get MismatchDirection(){return St},get PriceLineSource(){return Di},get PriceScaleMode(){return ui},get TickMarkType(){return Ei},get TrackingModeExitMode(){return Ti},createChart:function(t,i){return Fn(t,new Gi,Gi.Lc(i))},createChartEx:Fn,createImageWatermark:function(t,i,s){return new Pe(t,new Fe(i,s))},createOptionsChart:function(t,i){return Fn(t,new re,i)},createSeriesMarkers:function(t,i){const s=new nr(t,new sr);return i&&s.setMarkers(i),s},createTextWatermark:function(t,i){return new Pe(t,new Ae(i))},createUpDownMarkers:function(t,i={}){return new ur(t,new or(i))},createYieldCurveChart:function(t,i){const s=Wn(t);return new ne(s,i)},customSeriesDefaultOptions:cr,defaultHorzScaleBehavior:function(){return Gi},isBusinessDay:Vi,isUTCTimestamp:Ai,version:function(){return"5.0.6"}});window.LightweightCharts=dr}();
