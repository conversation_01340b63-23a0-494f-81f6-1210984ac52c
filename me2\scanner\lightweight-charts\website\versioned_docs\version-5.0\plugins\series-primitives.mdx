---
sidebar_label: Series Primitives
sidebar_position: 1
---

# Series Primitives

Primitives are extensions to the series which can define views and renderers to
draw on the chart using
[CanvasRenderingContext2D](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D).

Primitives are defined by implementing the
[`ISeriesPrimitive`](../api/type-aliases/ISeriesPrimitive.md) interface. The
interface defines the basic functionality and structure required for creating
custom primitives.

## Views

The primary purpose of a series primitive is to provide one, or more, views to
the library which contain the state and logic required to draw on the chart
panes.

There are two types of views which are supported within `ISeriesPrimitive` which
are:

- [`IPrimitivePaneView`](../api/interfaces/IPrimitivePaneView.md)
- [`ISeriesPrimitiveAxisView`](../api/interfaces/ISeriesPrimitiveAxisView.md)

The library will evoke the following getter functions (if defined) to get
references to the primitive's defined views for the corresponding section of the
chart:

- [`paneViews`](../api/interfaces/ISeriesPrimitiveBase.md#paneviews)
- [`priceAxisPaneViews`](../api/interfaces/ISeriesPrimitiveBase.md#priceaxispaneviews)
- [`timeAxisPaneViews`](../api/interfaces/ISeriesPrimitiveBase.md#timeaxispaneviews)
- [`priceAxisViews`](../api/interfaces/ISeriesPrimitiveBase.md#priceaxisviews)
- [`timeAxisViews`](../api/interfaces/ISeriesPrimitiveBase.md#timeaxisviews)

The first three views allow drawing on the corresponding panes (main chart pane,
price scale pane, and horizontal time scale pane) using the
[CanvasRenderingContext2D](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D)
and should implement the `ISeriesPrimitivePaneView` interface.

The views returned by the `priceAxisViews` and `timeAxisViews` getter methods
should implement the `ISeriesPrimitiveAxisView` interface and are used to define
labels to be drawn on the corresponding scales.

Below is a visual example showing the various sections of the chart where a
Primitive can draw.

import CodeBlock from '@theme/CodeBlock';
import sectionsExplainerCode from '!!raw-loader!./explainer-sections-demo.js';

<CodeBlock replaceThemeConstants chart className="language-js" chartOnly>
	{sectionsExplainerCode}
</CodeBlock>

### IPrimitivePaneView

The [`IPrimitivePaneView`](../api/interfaces/IPrimitivePaneView.md)
interface can be used to define a view which provides a renderer (implementing
the
[`IPrimitivePaneRenderer`](../api/interfaces/IPrimitivePaneRenderer.md)
interface) for drawing on the corresponding area of the chart using the
[CanvasRenderingContext2D](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D)
API. The view can define a
[`zOrder`](../api/interfaces/IPrimitivePaneView.md#zorder) to control where
in the visual stack the drawing will occur (See
[`PrimitivePaneViewZOrder`](../api/type-aliases/PrimitivePaneViewZOrder.md)
for more information).

Renderers should provide a
[`draw`](../api/interfaces/IPrimitivePaneRenderer.md#draw) method which will
be given a `CanvasRenderingTarget2D` target on which it can draw. Additionally,
a renderer can optionally provide a
[`drawBackground`](../api/interfaces/IPrimitivePaneRenderer.md#drawbackground)
method for drawing beneath other elements on the same zOrder.

:::tip

`CanvasRenderingTarget2D` is explained in more detail on the [Canvas Rendering Target](./canvas-rendering-target) page.

:::

#### Interactive Demo of zOrder layers

Below is an interactive demo chart illustrating where each zOrder is drawn
relative to the existing chart elements such as the grid, series, and crosshair.

import layersExplainerCode from '!!raw-loader!./explainer-layers-demo.js';

<CodeBlock replaceThemeConstants chart className="language-js" chartOnly>
	{layersExplainerCode}
</CodeBlock>

### ISeriesPrimitiveAxisView

The [`ISeriesPrimitiveAxisView`](../api/interfaces/ISeriesPrimitiveAxisView.md)
interface can be used to define a label on the price or time axis.

This interface provides several methods to define the appearance and position of
the label, such as the
[`coordinate`](../api/interfaces/ISeriesPrimitiveAxisView.md#coordinate) method,
which should return the desired coordinate for the label on the axis. It also
defines optional methods to set the fixed coordinate, text, text color,
background color, and visibility of the label.

Please see the
[`ISeriesPrimitiveAxisView`](../api/interfaces/ISeriesPrimitiveAxisView.md)
interface for more details.

## Lifecycle Methods

Your primitive can use the
[`attached`](../api/interfaces/ISeriesPrimitiveBase.md#attached) and
[`detached`](../api/interfaces/ISeriesPrimitiveBase.md#detached) lifecycle methods to
manage the lifecycle of the primitive, such as creating or removing external
objects and event handlers.

### attached

This method is called when the primitive is attached to a chart. The attached
method is evoked with a
[single argument](../api/interfaces/SeriesAttachedParameter.md) containing
properties for the chart, series, and a callback to request an update. The
`chart` and `series` properties are references to the chart API and the series
API instances for convenience purposes so that they don't need to be manually
provided within the primitive's constructor (if needed by the primitive).

The `requestUpdate` callback allows the primitive to notify the chart that it
should be updated and redrawn.

### detached

This method is called when the primitive is detached from a chart. This can be
used to remove any external objects or event handlers that were created during
the attached lifecycle method.

## Updating Views

Your primitive should update the views in the
[`updateAllViews()`](../api/interfaces/ISeriesPrimitiveBase.md#updateallviews) method
such that when the renderers are evoked, they can draw with the latest
information. The library invokes this method when it wants to update and redraw
the chart. If you would like to notify the library that it should trigger an
update then you can use the `requestUpdate` callback provided by the attached
lifecycle method.

## Extending the Autoscale Info

The [`autoscaleInfo()`](../api/interfaces/ISeriesPrimitiveBase.md#autoscaleinfo)
method can be provided to extend the base autoScale information of the series.
This can be used to ensure that the chart is automatically scaled correctly to
include all the graphics drawn by the primitive.

Whenever the chart needs to calculate the vertical visible range of the series
within the current time range then it will evoke this method. This method can be
omitted and the library will use the normal autoscale information for the
series. If the method is implemented then the returned values will be merged
with the base autoscale information to define the vertical visible range.

:::warning

Please note that this method will be evoked very often during
scrolling and zooming of the chart, thus it is recommended that this method is
either simple to execute, or makes use of optimisations such as caching to
ensure that the chart remains responsive.

:::
