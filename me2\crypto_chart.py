#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Crypto Chart - Simple Cryptocurrency Charting Tool
-------------------------------------------------
A single-file script for charting cryptocurrency data with technical indicators
using CCXT and lightweight-charts.
"""

import argparse
import pandas as pd
import numpy as np
import ccxt
from datetime import datetime
from lightweight_charts import Chart

class CryptoChart:
    def __init__(self, exchange_id='binance', symbol='BTC/USDT', timeframe='1h', market_type='future'):
        """
        Initialize the CryptoChart class.
        
        Args:
            exchange_id (str): The exchange ID (default: 'binance')
            symbol (str): The trading pair symbol (default: 'BTC/USDT')
            timeframe (str): The chart timeframe (default: '1h')
            market_type (str): Market type - 'future' or 'spot' (default: 'future')
        """
        self.exchange_id = exchange_id
        self.symbol = symbol
        self.timeframe = timeframe
        self.market_type = market_type
        self.exchange = self._initialize_exchange()
        self.chart = None
        
    def _initialize_exchange(self):
        """Initialize the exchange."""
        try:
            exchange_class = getattr(ccxt, self.exchange_id)
            exchange = exchange_class({
                'enableRateLimit': True,
                'options': {
                    'defaultType': self.market_type,
                }
            })
            # Load markets to ensure symbol validity
            exchange.load_markets()
            return exchange
        except Exception as e:
            print(f"Error initializing exchange: {e}")
            return None
    
    def fetch_ohlcv(self, limit=500):
        """
        Fetch OHLCV data from the exchange.
        
        Args:
            limit (int): Number of candles to fetch (default: 500)
            
        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Format for lightweight-charts
            df = df[['time', 'open', 'high', 'low', 'close', 'volume']]
            
            return df
        except Exception as e:
            print(f"Error fetching OHLCV data: {e}")
            return None
    
    def calculate_sma(self, df, period=20):
        """Calculate Simple Moving Average."""
        sma = df['close'].rolling(window=period).mean()
        sma_df = pd.DataFrame({'time': df['time'], 'value': sma})
        return sma_df.dropna()
    
    def calculate_ema(self, df, period=50):
        """Calculate Exponential Moving Average."""
        ema = df['close'].ewm(span=period, adjust=False).mean()
        ema_df = pd.DataFrame({'time': df['time'], 'value': ema})
        return ema_df.dropna()
    
    def calculate_bollinger_bands(self, df, period=20, std_dev=2):
        """Calculate Bollinger Bands."""
        # Calculate middle band (SMA)
        middle_band = df['close'].rolling(window=period).mean()
        
        # Calculate standard deviation
        std = df['close'].rolling(window=period).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)
        
        # Create DataFrame
        bbands_df = pd.DataFrame({
            'time': df['time'],
            'middle': middle_band,
            'upper': upper_band,
            'lower': lower_band
        })
        
        return bbands_df.dropna()
    
    def calculate_rsi(self, df, period=14):
        """Calculate Relative Strength Index."""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # For periods after the initial period
        for i in range(period, len(df)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (period-1) + gain.iloc[i]) / period
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (period-1) + loss.iloc[i]) / period
        
        rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)  # Avoid division by zero
        rsi = 100 - (100 / (1 + rs))
        
        rsi_df = pd.DataFrame({'time': df['time'], 'value': rsi})
        return rsi_df.dropna()
    
    def calculate_macd(self, df, fast_period=12, slow_period=26, signal_period=9):
        """Calculate Moving Average Convergence Divergence."""
        # Calculate fast and slow EMAs
        fast_ema = df['close'].ewm(span=fast_period, adjust=False).mean()
        slow_ema = df['close'].ewm(span=slow_period, adjust=False).mean()
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
        
        # Calculate histogram
        histogram = macd_line - signal_line
        
        # Create DataFrame
        macd_df = pd.DataFrame({
            'time': df['time'],
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        })
        
        return macd_df.dropna()
    
    def find_support_resistance(self, df, window=10, threshold=0.02):
        """
        Find support and resistance levels.
        
        Args:
            df (pd.DataFrame): DataFrame with OHLCV data
            window (int): Window size for finding local extrema (default: 10)
            threshold (float): Threshold for clustering levels (default: 0.02)
            
        Returns:
            tuple: (support_levels, resistance_levels)
        """
        # Find local minima (support)
        df['min'] = df['low'].rolling(window=window, center=True).min()
        df['is_support'] = (df['low'] == df['min']) & (df['low'].shift(window//2) > df['low']) & (df['low'].shift(-window//2) > df['low'])
        
        # Find local maxima (resistance)
        df['max'] = df['high'].rolling(window=window, center=True).max()
        df['is_resistance'] = (df['high'] == df['max']) & (df['high'].shift(window//2) < df['high']) & (df['high'].shift(-window//2) < df['high'])
        
        # Get support and resistance levels
        support_levels = df[df['is_support']]['low'].tolist()
        resistance_levels = df[df['is_resistance']]['high'].tolist()
        
        # Cluster levels
        support_levels = self._cluster_levels(support_levels, threshold)
        resistance_levels = self._cluster_levels(resistance_levels, threshold)
        
        return support_levels, resistance_levels
    
    def _cluster_levels(self, levels, threshold):
        """
        Cluster similar price levels.
        
        Args:
            levels (list): List of price levels
            threshold (float): Threshold for clustering
            
        Returns:
            list: Clustered price levels
        """
        if not levels:
            return []
        
        # Sort levels
        levels = sorted(levels)
        
        # Cluster levels
        clustered_levels = []
        current_cluster = [levels[0]]
        
        for i in range(1, len(levels)):
            # If the level is close to the current cluster, add it to the cluster
            if (levels[i] - current_cluster[-1]) / current_cluster[-1] < threshold:
                current_cluster.append(levels[i])
            else:
                # Otherwise, add the average of the current cluster to the result
                clustered_levels.append(sum(current_cluster) / len(current_cluster))
                current_cluster = [levels[i]]
        
        # Add the last cluster
        if current_cluster:
            clustered_levels.append(sum(current_cluster) / len(current_cluster))
        
        return clustered_levels
    
    def create_chart(self, indicators=None, support_resistance=False, volume=True, title=None):
        """
        Create and display a chart with indicators.
        
        Args:
            indicators (list): List of indicators to display (default: None)
                Options: 'sma', 'ema', 'bbands', 'rsi', 'macd'
            support_resistance (bool): Whether to show support and resistance levels (default: False)
            volume (bool): Whether to show volume (default: True)
            title (str): Chart title (default: None)
        """
        # Fetch data
        df = self.fetch_ohlcv()
        if df is None:
            print("Failed to fetch data.")
            return
        
        # Create chart
        self.chart = Chart(volume_enabled=volume)
        
        # Format DataFrame for lightweight-charts
        df_formatted = df.copy()
        df_formatted['time'] = df_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Set chart data
        self.chart.set(df_formatted)
        
        # Add indicators
        if indicators:
            if 'sma' in indicators:
                sma = self.calculate_sma(df)
                sma_formatted = sma.copy()
                sma_formatted['time'] = sma_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                sma_line = self.chart.create_line(color='blue', price_line=True, price_label=True)
                sma_line.set(sma_formatted)
            
            if 'ema' in indicators:
                ema = self.calculate_ema(df)
                ema_formatted = ema.copy()
                ema_formatted['time'] = ema_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                ema_line = self.chart.create_line(color='red', price_line=True, price_label=True)
                ema_line.set(ema_formatted)
            
            if 'bbands' in indicators:
                bbands = self.calculate_bollinger_bands(df)
                bbands_formatted = bbands.copy()
                bbands_formatted['time'] = bbands_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                
                bb_upper = self.chart.create_line(color='green', price_line=False)
                bb_middle = self.chart.create_line(color='orange', price_line=False)
                bb_lower = self.chart.create_line(color='green', price_line=False)
                
                bb_upper.set(bbands_formatted[['time', 'upper']].rename(columns={'upper': 'value'}))
                bb_middle.set(bbands_formatted[['time', 'middle']].rename(columns={'middle': 'value'}))
                bb_lower.set(bbands_formatted[['time', 'lower']].rename(columns={'lower': 'value'}))
            
            if 'rsi' in indicators:
                rsi = self.calculate_rsi(df)
                rsi_formatted = rsi.copy()
                rsi_formatted['time'] = rsi_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                rsi_chart = self.chart.create_subchart(height=0.2, position='bottom')
                rsi_line = rsi_chart.create_line(color='purple')
                rsi_line.set(rsi_formatted)
                
                # Add overbought/oversold lines
                rsi_chart.create_horizontal_line(70, color='red')
                rsi_chart.create_horizontal_line(30, color='green')
                rsi_chart.watermark('RSI')
            
            if 'macd' in indicators:
                macd = self.calculate_macd(df)
                macd_formatted = macd.copy()
                macd_formatted['time'] = macd_formatted['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                macd_chart = self.chart.create_subchart(height=0.2, position='bottom')
                
                # MACD line
                macd_line = macd_chart.create_line(color='blue')
                macd_line.set(macd_formatted[['time', 'macd']].rename(columns={'macd': 'value'}))
                
                # Signal line
                signal_line = macd_chart.create_line(color='red')
                signal_line.set(macd_formatted[['time', 'signal']].rename(columns={'signal': 'value'}))
                
                # Histogram
                histogram_data = macd_formatted[['time', 'histogram']].rename(columns={'histogram': 'value'})
                histogram = macd_chart.create_histogram(color_up='green', color_down='red')
                histogram.set(histogram_data)
                
                macd_chart.watermark('MACD')
        
        # Add support and resistance levels
        if support_resistance:
            support_levels, resistance_levels = self.find_support_resistance(df)
            
            # Add support levels
            for level in support_levels:
                self.chart.create_horizontal_line(level, color='green', line_style='dashed', line_width=1)
            
            # Add resistance levels
            for level in resistance_levels:
                self.chart.create_horizontal_line(level, color='red', line_style='dashed', line_width=1)
        
        # Set chart title
        if title:
            self.chart.watermark(title)
        else:
            self.chart.watermark(f"{self.symbol} - {self.timeframe}")
        
        # Show chart
        self.chart.show(block=True)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Crypto Chart - Simple Cryptocurrency Charting Tool')
    parser.add_argument('--exchange', type=str, default='binance', help='Exchange ID (default: binance)')
    parser.add_argument('--symbol', type=str, default='BTC/USDT', help='Trading pair symbol (default: BTC/USDT)')
    parser.add_argument('--timeframe', type=str, default='1h', help='Chart timeframe (default: 1h)')
    parser.add_argument('--market', type=str, default='future', choices=['future', 'spot'], 
                        help='Market type (default: future)')
    parser.add_argument('--indicators', type=str, nargs='+', 
                        choices=['sma', 'ema', 'bbands', 'rsi', 'macd', 'all'], 
                        default=['all'], help='Indicators to display (default: all)')
    parser.add_argument('--sr', action='store_true', help='Show support and resistance levels')
    parser.add_argument('--no-volume', action='store_true', help='Hide volume')
    return parser.parse_args()

def main():
    """Main function."""
    # Parse command line arguments
    args = parse_args()
    
    # Process indicators
    if 'all' in args.indicators:
        indicators = ['sma', 'ema', 'bbands', 'rsi', 'macd']
    else:
        indicators = args.indicators
    
    # Create chart
    chart = CryptoChart(
        exchange_id=args.exchange,
        symbol=args.symbol,
        timeframe=args.timeframe,
        market_type=args.market
    )
    
    # Display chart
    chart.create_chart(
        indicators=indicators,
        support_resistance=args.sr,
        volume=not args.no_volume
    )

if __name__ == '__main__':
    main()
