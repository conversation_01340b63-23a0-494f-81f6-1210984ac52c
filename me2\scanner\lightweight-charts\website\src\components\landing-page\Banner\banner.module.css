.Banner {
	font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
		Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	display: flex;
	flex-direction: row;
	margin: 0;
	background: linear-gradient(
		90deg,
		#34ceff 0%,
		#7284ff 19.13%,
		#ae6cff 33.85%,
		#df60ff 51.04%,
		#8b7eff 67.71%,
		#4474ff 83.33%,
		#1251ff 99.99%,
		#2962ff 100%
	);
	padding: 12px var(--container-padding);
	justify-content: center;
	align-items: center;
}

.Banner:hover {
	text-decoration: none;
	color: #fff;
}

.BannerContent {
	max-width: 1920px;

	font-style: normal;
	font-weight: 600;
	font-size: 18px;
	line-height: 24px;
	/* identical to box height, or 133% */

	display: flex;
	align-items: center;
	text-align: center;
	letter-spacing: -0.025em;
	font-feature-settings: 'tnum' on, 'lnum' on;

	color: #fff;
}

.BannerButton {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 8px 16px;
	margin-left: 16px;
	white-space: nowrap;
	color: #fff;

	border-radius: 56px;
	border: 1px solid #fff;

	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;

	letter-spacing: -0.02em;
	font-feature-settings: 'tnum' on, 'lnum' on;
}

/* Phone-Landscape */
@media (max-width: 767px) {
	.Banner {
		justify-content: space-between;
	}
	.BannerContent {
		font-size: 16px;
		line-height: 24px;
		letter-spacing: -0.02em;
		text-align: left;
	}
}

/* Phone-Vertical */
@media (max-width: 567px) {
	.BannerContent {
		font-size: 14px;
		line-height: 18px;
		letter-spacing: -0.011em;
	}
}
