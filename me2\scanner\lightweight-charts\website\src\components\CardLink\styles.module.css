.cardContainer {
	--ifm-link-color: var(--ifm-color-emphasis-800);
	--ifm-link-hover-color: var(--ifm-color-emphasis-700);
	--ifm-link-hover-decoration: none;

	box-shadow: 0 1.5px 3px 0 rgb(0 0 0 / 15%);
	border: 1px solid var(--ifm-color-emphasis-200);
	transition: all var(--ifm-transition-fast) ease;
	transition-property: border, box-shadow;

	flex-direction: row;
}

.cardContainer:hover {
	border-color: var(--ifm-color-primary);
	box-shadow: 0 3px 6px 0 rgb(0 0 0 / 20%);
}

.cardContainer *:last-child {
	margin-bottom: 0;
}

.cardDetails {
	display: flex;
	flex-direction: column;
	overflow: auto;
}

.centerDetails {
	justify-content: center;
}

.cardImage {
	margin-right: 1rem;
	height: 62px;
}

.cardImage svg {
	height: 62px;
	width: 62px;
}

.cardTitle {
	font-size: 1.2rem;
}

.cardDescription {
	font-size: 0.8rem;
}

.noWrap {
	white-space: normal;
}

.noBottomMargin {
	margin-bottom: 0px;
}
