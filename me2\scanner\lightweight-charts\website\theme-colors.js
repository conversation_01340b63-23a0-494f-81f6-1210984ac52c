export const themeColors = {
	DARK: {
		CHART_BACKGROUND_COLOR: 'black',
		CHART_BACKGROUND_RGB_COLOR: '0, 0, 0',
		LINE_LINE_COLOR: '#2962FF',
		LINE_LINE2_COLOR: 'rgb(225, 87, 90)',
		LINE_LINE3_COLOR: 'rgb(242, 142, 44)',
		LINE_LINE4_COLOR: 'rgb(164, 89, 209)',
		LINE_LINE5_COLOR: 'rgb(27, 156, 133)',
		AREA_TOP_COLOR: '#2962FF',
		AREA_BOTTOM_COLOR: 'rgba(41, 98, 255, 0.28)',
		BAR_UP_COLOR: '#26a69a',
		BAR_DOWN_COLOR: '#ef5350',
		BASELINE_TOP_LINE_COLOR: 'rgba( 38, 166, 154, 1)',
		BASELINE_TOP_FILL_COLOR1: 'rgba( 38, 166, 154, 0.28)',
		BASELINE_TOP_FILL_COLOR2: 'rgba( 38, 166, 154, 0.05)',
		BASELINE_BOTTOM_LINE_COLOR: 'rgba( 239, 83, 80, 1)',
		BASELINE_BOTTOM_FILL_COLOR1: 'rgba( 239, 83, 80, 0.05)',
		BASELINE_BOTTOM_FILL_COLOR2: 'rgba( 239, 83, 80, 0.28)',
		HISTOGRAM_COLOR: '#26a69a',
		CHART_TEXT_COLOR: 'white',
	},
	LIGHT: {
		CHART_BACKGROUND_COLOR: 'white',
		CHART_BACKGROUND_RGB_COLOR: '255, 255, 255',
		LINE_LINE_COLOR: '#2962FF',
		LINE_LINE2_COLOR: 'rgb(225, 87, 90)',
		LINE_LINE3_COLOR: 'rgb(242, 142, 44)',
		LINE_LINE4_COLOR: 'rgb(164, 89, 209)',
		LINE_LINE5_COLOR: 'rgb(27, 156, 133)',
		AREA_TOP_COLOR: '#2962FF',
		AREA_BOTTOM_COLOR: 'rgba(41, 98, 255, 0.28)',
		BAR_UP_COLOR: '#26a69a',
		BAR_DOWN_COLOR: '#ef5350',
		BASELINE_TOP_LINE_COLOR: 'rgba( 38, 166, 154, 1)',
		BASELINE_TOP_FILL_COLOR1: 'rgba( 38, 166, 154, 0.28)',
		BASELINE_TOP_FILL_COLOR2: 'rgba( 38, 166, 154, 0.05)',
		BASELINE_BOTTOM_LINE_COLOR: 'rgba( 239, 83, 80, 1)',
		BASELINE_BOTTOM_FILL_COLOR1: 'rgba( 239, 83, 80, 0.05)',
		BASELINE_BOTTOM_FILL_COLOR2: 'rgba( 239, 83, 80, 0.28)',
		HISTOGRAM_COLOR: '#26a69a',
		CHART_TEXT_COLOR: 'black',
	},
};
